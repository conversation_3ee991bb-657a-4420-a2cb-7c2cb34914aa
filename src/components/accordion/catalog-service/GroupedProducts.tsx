import React, { useEffect, useState } from 'react';
import { Col, Row, Spacer } from '../../UI-components/Grid';
import { baseTheme, colors } from '../../../themes/theme';
import {
  ProductColumns,
  GroupedProdTableColumns as columns,
} from '../../table/product/Columns';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Field as _Field } from '@zendeskgarden/react-dropdowns';
import styled from 'styled-components';
import NothingToshow from '../../UI-components/NothingToShow';
import { pageRoutes } from '../../navigation/RouteConfig';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { DataTable } from '../../table/product/DataTable';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import { Button } from '../../UI-components/Button';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { Pagination } from '@zendeskgarden/react-pagination';

const TableCard = styled.div`
  margin: 10px 0px;
`;
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const ScrollableTableHolder = styled.div`
  height: 600px;
  overflow-y: auto;
`;
interface GroupedProduct {
  id: number;
  sku: string;
  status: boolean;
  type_id: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  deleted_at: string | null;
}

interface Inventory {
  id: number;
  is_in_stock: boolean;
  qty: number;
  backorders: boolean;
}

export interface Item {
  id: number;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  name: string;
  price: number;
  special_price: number | null;
  image: string;
  product: GroupedProduct;
  position: number;
  inventory: Inventory;
}

interface GroupedProductResponse {
  items: Item[];
  count: number;
  page_count: number;
  page: number;
  page_size: number;
}
const GroupedProducts = () => {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [page, setPage] = useState(1);
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [GroupData, setGroupData] = useState<GroupedProductResponse>({
    items: [],
    count: 0,
    page_count: 0,
    page: 0,
    page_size: 0,
  });
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const {
    contextProdData,
    setContextProdData,
    setContextUpdateProdData,
    groupAssociate,
    groupProdData,
    setGroupProdData,
    setIsSearchChildProduct,
    isSearchChildProduct,
  } = useProductContext();
  const addToast = useToast();

  useEffect(() => {
    let productIds: number[] = [];

    if (queryParams.get('id')) {
      if (
        contextProdData.product_links?.associated &&
        Array.isArray(contextProdData.product_links?.associated)
      ) {
        productIds = contextProdData.product_links.associated.map(
          (item: { product_id: number; position: number }) => item.product_id,
        );
      }
    } else {
      productIds = groupAssociate.map(
        (item: { product_id: number; position: number }) => item.product_id,
      );
    }
    const uniqueProductIds = Array.from(new Set(productIds));
    // setGroupedProd(uniqueProductIds);
  }, [contextProdData, groupAssociate]);

  useEffect(() => {
    if (!queryParams.get('id')) {
      setContextProdData((prevState) => ({
        ...prevState,
        product_links: {
          ...prevState.product_links,
          associated: groupAssociate,
        },
      }));
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        product_links: {
          ...prevState.product_links,
          associated: groupAssociate,
        },
      }));
    }
  }, [groupAssociate]);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);

  const { mutate: mutateGroupedProd, isLoading: updateGroupedProdLoading } =
    useMutation(
      async (prodId: number) => {
        const postdata = {
          id: prodId,
          page: page ?? 1,
          size: 20,
        };
        if (prodId) {
          try {
            const response = await axios.post(
              // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
              `/v1/catalog-admin/search-child-products`,
              postdata,
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                  'x-api-key': `${constants.CATALOG_KEY}`,
                  'Content-Type': 'application/json',
                },
              },
            );
            return response.data;
          } catch (error) {
            throw new Error('Failed to fetch Grouped Product');
          }
        } else {
          return {
            item_count: 0,
            items: [],
            page_no: 1,
            page_size: 1,
            pages_count: 1,
          };
        }
      },
      {
        onError: (err) => {
          addToast('error', `Grouped product error  ${err}`);
        },
        onSuccess: (data) => {
          // setData(data.items);
          setGroupProdData(data.items);
          setGroupData(data);
        },
      },
    );

  // useEffect(() => {
  //   console.log('groupProdData', groupProdData);
  // }, [groupProdData]);

  const table = useReactTable({
    columns,
    data: groupProdData ?? [],
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const handleAddProduct = () => {
    localStorage.setItem('groupedId', contextProdData?.attributes_list?.name);
    if (prodId) {
      navigate(`${pageRoutes['GO_TO_ADD_GROUP_PRODUCT_TO_ID']}?id=${prodId}`);
    } else {
      navigate(`${pageRoutes['GO_TO_ADD_GROUP_PRODUCT']}`);
    }
  };

  useEffect(() => {
    if (prodId) {
      mutateGroupedProd(prodId);
    }
  }, [prodId, page]);

  useEffect(() => {
    if (isSearchChildProduct && prodId) {
      mutateGroupedProd(prodId);
      setIsSearchChildProduct(false);
    }
  }, [isSearchChildProduct]);

  useEffect(() => {
    table.setPageSize(20);
  }, []);

  return (
    <div
      style={{
        marginBottom: `${baseTheme.paddings.xl}`,
      }}
    >
      <p
        style={{
          marginBottom: `${baseTheme.paddings.md}`,
        }}
      >
        A grouped product is made up of multiple, standalone products that are
        presented as a group. You can offer variations of a single products. or
        group them by season or theme to create a coordinated set. Each product
        can be purchased separately, or as part of the group.
      </p>
      <Button isPrimary isOrange onClick={handleAddProduct}>
        Add Products to Group
      </Button>
      <Spacer />
      <Container>
        <TableCard>
          <ScrollableTableHolder>
            {updateGroupedProdLoading ? (
              <div>
                <Skeleton
                  width={`100%`}
                  height={`100px`}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
                <Skeleton
                  width={`100%`}
                  height={`100px`}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
                <Skeleton
                  width={`100%`}
                  height={`100px`}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
                <Skeleton
                  width={`100%`}
                  height={`100px`}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
                <Skeleton
                  width={`100%`}
                  height={`100px`}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                />
              </div>
            ) : (
              <>
                {table?.getRowModel()?.rows?.length > 0 && (
                  <>
                    <DataTable
                      table={table}
                      columns={columns}
                      data={groupProdData}
                    />
                  </>
                )}
              </>
            )}

            {!updateGroupedProdLoading &&
              table?.getRowModel()?.rows?.length === 0 && (
                <NothingToshow divHeight="55vh" />
              )}
          </ScrollableTableHolder>
          {table?.getRowModel()?.rows?.length > 0 && (
            <Row
              justifyContent="center"
              alignItems="center"
              style={{
                margin: '10px 0px',
                borderTop: '1px solid #ccc',
                padding: '10px 0px',
              }}
            >
              <Pagination
                color={baseTheme.colors.deepBlue}
                totalPages={GroupData?.page_count}
                pagePadding={2}
                currentPage={page}
                onChange={(e) => {
                  setPage(e);
                }}
              />
            </Row>
          )}
        </TableCard>
      </Container>
    </div>
  );
};

export default GroupedProducts;
