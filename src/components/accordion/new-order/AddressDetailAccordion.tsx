import { Field, Label as _Label, Radio } from '@zendeskgarden/react-forms';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { XMD } from '../../UI-components/Typography';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { Button } from '../../UI-components/Button';
// import EditAddressModal from '../../modal/new-order/EditAddress';
import {
  CartData,
  useNewOrderContext,
} from '../../../pages/order/NewOrderContext';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useCommonAxios';
import routes from '../../../constants/routes';
import { useParams } from 'react-router-dom';
import { useCustomerContext } from '../../../pages/customer/CustomerContext';
import RemoveAddressModal from '../../modal/customer/RemoveAddressModal';
import useToast from '../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import AddressModal from '../../modal/new-order/AddAddress';
import { Spinner, Skeleton } from '@zendeskgarden/react-loaders';

const Spacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.sm};
`;

const SmallSpacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.xsm};
`;

const Label = styled(_Label)`
  & .cIcMRP:checked ~ .StyledRadioLabel-sc-1aq2e5t-0::before {
    border-color: rgb(0, 0, 255); /* Replace with your deep blue RGB values */
    background-color: rgb(
      0,
      0,
      255
    ); /* Replace with your deep blue RGB values */
  }
`;

const AddressDetailAccordion = ({ isOpen }: { isOpen: boolean }) => {
  const [radioValue, setRadioValue] = useState('annual');

  useEffect(() => {
    baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
  }, []);

  const [showAddAddress, setShowAddAddress] = useState<boolean>(false);
  const [deleteAddress, setDeleteAddress] = useState<boolean>(false);
  const [editShowAddress, setEditAddAddress] = useState<boolean>(false);

  const [addressList, setAddressList] = useState<any[]>([]);

  const { customerId } = useParams();

  const {
    setSelectedAddress,
    selectedAddress,
    cartId,
    setIsOrderHaveAddress,
    refetchProductData,
    shippingData,
    availableCartData,
  } = useNewOrderContext();
  const isMountAdress = useRef(true);
  const cartAdressId =
    availableCartData?.shipping_addresses[0]?.customer_address_id || null;

  const isAddressExist = useMemo(() => {
    return addressList.findIndex((item) => item.id === cartAdressId);
  }, [addressList, cartAdressId]);

  // Set initial radio value when address list is loaded and there's a pre-selected address
  useEffect(() => {
    if (isAddressExist != -1 && isMountAdress.current) {
      setRadioValue(isAddressExist.toString());
      isMountAdress.current = false;
    }
  }, [isAddressExist]);

  const axios = useAxios();

  // const [filters, setFilters] = useState<any>({
  //   page: 0,
  //   size: 100,
  // });
  const {
    refetch,
    isLoading: isGettingAddress,
    isError,
    error,
  } = useQuery({
    queryKey: ['get-customer-address', customerId],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers/${customerId}/addresses`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
          // params: {
          //   ...filters,
          // },
        },
      );

      return response;
    },
    enabled: !!isOpen,
    onSuccess: (response: any) => {
      // console.log('response', response);
      setAddressList(response.data || []);
    },
    onError: (err) => {
      console.log('error', err);
      addToast('error', 'Failed to load addresses. Please try again.');
    },
  });

  // useEffect(() => {
  //   if (filters) {
  //     refetch();
  //   }
  // }, [filters]);

  const addToast = useToast();

  const { mutate: updateAddress, isLoading: isCreating } = useMutation(
    async (shippingData: CartData) => {
      const response = axios.put(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/addresses/shipping`,
        {
          ...shippingData,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        // console.log('Error', `${convertToSentenceCase(err.message)}`);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data: any) => {
        // addToast('success', 'Customer Address added successfully');
        // refetch();
        setIsOrderHaveAddress(true);
        close();
        refetchProductData();
      },
    },
  );

  useEffect(() => {
    const selectedAddressItem = addressList.find(
      (item, index) => index.toString() === radioValue,
    );
    if (selectedAddressItem) {
      const currentAddressCart: CartData = {
        cart_id: cartId,
        shipping_addresses: [
          {
            address: {
              alternate_mobile: selectedAddressItem?.alternate_telephone,
              city: selectedAddressItem?.city,
              company: selectedAddressItem?.company,
              country_code: selectedAddressItem?.country_id,
              firstname: selectedAddressItem?.firstname,
              gst_id: selectedAddressItem?.vat_id,
              lastname: selectedAddressItem?.lastname,
              postcode: selectedAddressItem?.postcode,
              region: selectedAddressItem?.region,
              region_code: selectedAddressItem?.postcode,
              region_id: selectedAddressItem?.region_id,
              save_in_address_book: true,
              street: selectedAddressItem?.street,
              telephone: selectedAddressItem?.telephone,
            },
            customer_address_id: selectedAddressItem.id,
            same_as_shipping: true,
            user_for_shipping: true,
          },
        ],
      };

      updateAddress(currentAddressCart);
    }
  }, [radioValue, addressList, cartId]);

  // If accordion is not open, don't render anything
  if (!isOpen) {
    return null;
  }

  // Enhanced loading state with skeleton UI
  if (isGettingAddress) {
    return (
      <Row>
        <Col size={12}>
          <div role="group" aria-label="Loading Address List">
            {/* Show skeleton loaders for address items */}
            {[1, 2, 3].map((index) => (
              <div key={index}>
                <Row>
                  <Col size={8}>
                    <Field>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                        }}
                      >
                        <Skeleton
                          width="20px"
                          height="20px"
                          style={{ borderRadius: '50%' }}
                        />
                        <Skeleton width="100%" height="20px" />
                      </div>
                    </Field>
                  </Col>
                  <Col size={4}>
                    <Row>
                      <Skeleton
                        width="30px"
                        height="16px"
                        style={{ marginRight: '8px' }}
                      />
                      <Skeleton width="40px" height="16px" />
                    </Row>
                  </Col>
                </Row>
                <SmallSpacer />
              </div>
            ))}

            {/* Loading state for Add new address button */}
            <Spacer />
            <Skeleton
              width="150px"
              height="36px"
              style={{ borderRadius: baseTheme.borderRadii.md }}
            />
          </div>
        </Col>
      </Row>
    );
  }

  // Error state
  if (isError) {
    return (
      <Row>
        <Col size={12}>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <XMD hue="secondary" style={{ marginBottom: '16px' }}>
              Failed to load addresses. Please try again.
            </XMD>
            <Button onClick={() => refetch()} isPrimary size="small">
              Retry
            </Button>
          </div>
        </Col>
      </Row>
    );
  }

  return (
    <>
      <Row>
        <Col size={12}>
          <div role="group" aria-label="Address List">
            {/* Empty state */}
            {addressList.length === 0 && !isGettingAddress && (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <XMD hue="secondary" style={{ marginBottom: '16px' }}>
                  No addresses found. Add your first address to continue.
                </XMD>
              </div>
            )}

            {addressList.map((item, index) => (
              <>
                <Row key={item.id}>
                  <Col size={8}>
                    <Field>
                      <Radio
                        name="default example"
                        value={`${index}`}
                        checked={
                          radioValue === `${index}` ||
                          (availableCartData?.shipping_addresses?.length > 0 &&
                            availableCartData?.shipping_addresses[0]
                              ?.customer_address_id === item.id)
                        }
                        onChange={(event) => setRadioValue(event.target.value)}
                        disabled={isCreating}
                      >
                        <Label style={{ opacity: isCreating ? 0.6 : 1 }}>
                          {`${item.street} ${item.city} ${item.region} ${item.country_code} ${item.postcode}  `}
                          {isCreating && radioValue === `${index}` && (
                            <Spinner
                              size="12px"
                              style={{ marginLeft: '8px' }}
                            />
                          )}
                        </Label>
                      </Radio>
                    </Field>
                  </Col>
                  <Col size={4}>
                    <Row>
                      {isCreating ? (
                        <Spinner size="12px" />
                      ) : (
                        <>
                          <XMD
                            hue="primary"
                            onClick={() => {
                              setEditAddAddress(true);
                              setSelectedAddress(item);
                            }}
                            style={{ cursor: 'pointer' }}
                          >
                            <u>Edit </u>
                          </XMD>
                          <XMD
                            onClick={() => {
                              setDeleteAddress(true);
                              setSelectedAddress(item);
                            }}
                            style={{ marginLeft: '8px', cursor: 'pointer' }}
                            hue="secondary"
                          >
                            <u>Delete</u>
                          </XMD>
                        </>
                      )}
                    </Row>
                  </Col>
                </Row>
                <SmallSpacer />
              </>
            ))}

            {/* <Row>
              <Col size={8}>
                <Field>
                  <Radio
                    name="default example"
                    value="perennial"
                    checked={radioValue === 'perennial'}
                    onChange={(event) => setRadioValue(event.target.value)}
                  >
                    <Label>
                      Aditya Kumar Rai, Sitapur Road, Jankipuram, Lucknow, India
                      - 226578
                    </Label>
                  </Radio>
                </Field>
              </Col>
              <Col size={4}>
                <Row>
                  <XMD
                    style={{ cursor: 'pointer' }}
                    hue="primary"
                    onClick={() => {
                      setEditAddAddress(true);
                    }}
                  >
                    <u>Edit </u>
                  </XMD>
                  <XMD style={{ marginLeft: '8px' }} hue="secondary">
                    <u>Delete</u>
                  </XMD>
                </Row>
              </Col>
            </Row> */}
          </div>
        </Col>

        <Col>
          <Spacer />
          <Button
            onClick={() => {
              setShowAddAddress(true);
            }}
            isPrimary
            disabled={isCreating}
          >
            {isCreating ? (
              <>
                <Spinner size="12px" style={{ marginRight: '8px' }} />
                Processing...
              </>
            ) : (
              'Add new address'
            )}
          </Button>
        </Col>
      </Row>
      {showAddAddress && (
        <AddressModal
          refetch={refetch}
          close={() => {
            setShowAddAddress(false);
          }}
        />
      )}

      {editShowAddress && selectedAddress && (
        <AddressModal
          close={() => {
            setEditAddAddress(false);
            // refetch();
          }}
          refetch={refetch}
          editData={selectedAddress}
          setRadioValue={setRadioValue}
        />
      )}

      {deleteAddress && selectedAddress && (
        <>
          <RemoveAddressModal
            addressData={selectedAddress}
            close={() => {
              setDeleteAddress(false);
              refetch();
            }}
          />
        </>
      )}
    </>
  );
};

export default AddressDetailAccordion;
