import React, { useEffect, useState } from 'react';
import { baseTheme } from '../../../themes/theme';
import { Col, Row, SmallSpacer } from '../../UI-components/Grid';
import {
  Field,
  Label as _Label,
  Radio,
  Input as _Input,
  Message,
} from '@zendeskgarden/react-forms';
import { XMD, XSM } from '../../UI-components/Typography';
import { Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import { useNewOrderContext } from '../../../pages/order/NewOrderContext';
import { Skeleton } from '@zendeskgarden/react-loaders';

const Label = styled(_Label)`
  & .cIcMRP:checked ~ .StyledRadioLabel-sc-1aq2e5t-0::before {
    border-color: rgb(0, 0, 255); /* Replace with your deep blue RGB values */
    background-color: rgb(
      0,
      0,
      255
    ); /* Replace with your deep blue RGB values */
  }

  font-weight: 400;
  color: ${(p) => p.theme.colors.deepBlue};
`;

const Input = styled(_Input)`
  margin-bottom: 12px;
  &:not(:focus) {
    background-color: ${(p) => p.theme.components.TextInput.bgColor};
  }
`;

const PaymentMethodAccordion = ({ isOpen }: { isOpen: boolean }) => {
  // useEffect(() => {
  //   setTimeout(() => {
  //     baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
  //   }, 500);
  // }, []);

  const [radioValue, setRadioValue] = useState('prepaid');

  const {
    paymentInfo,
    paymentInfoLoading,
    setPaymentReference,
    paymentReference,
    setPaymentMethod,
  } = useNewOrderContext();

  return (
    <>
      <Row>
        <Col size={12}>
          <div role="group" aria-label="Choose a payment method">
            {paymentInfoLoading ? (
              <>
                <Skeleton
                  width="100%"
                  height={baseTheme.components.dimension.height.md}
                />
              </>
            ) : (
              <>
                {paymentInfo.payment_methods.map((item: any) => (
                  <>
                    {item.code === 'prepaid' ? (
                      <>
                        <Row>
                          <Col size={8}>
                            <Field>
                              <Radio
                                name="Prepaid"
                                value="prepaid"
                                checked={radioValue === 'prepaid'}
                                onChange={(event) => {
                                  setRadioValue(event.target.value);
                                  setPaymentMethod(event.target.value);
                                }}
                              >
                                <Label>
                                  <Span>Prepaid order</Span>
                                </Label>
                              </Radio>
                            </Field>
                            <Field>
                              <Row alignItems="center">
                                <Col offset={0.5} size={4}>
                                  <XSM hue="black">Payment reference id</XSM>
                                </Col>
                                <Col size={6}>
                                  <Input
                                    style={{ margin: 0 }}
                                    value={paymentReference}
                                    onChange={(e) =>
                                      setPaymentReference(e.target.value)
                                    }
                                  />
                                </Col>
                              </Row>
                              <Row>
                                <Col offset={0.5} size={4}></Col>
                                <Col size={6}>
                                  {paymentReference ||
                                    (paymentReference == '' && (
                                      <>
                                        <Message>
                                          <Span hue="red">
                                            Invalid Reference *
                                          </Span>
                                        </Message>
                                      </>
                                    ))}
                                </Col>
                              </Row>
                            </Field>
                          </Col>
                        </Row>
                        <SmallSpacer />
                      </>
                    ) : (
                      <>
                        <Row>
                          <Col size={8}>
                            <Field>
                              <Radio
                                name="COD"
                                value={item.code}
                                checked={radioValue === item.code}
                                onChange={(event) => {
                                  setRadioValue(event.target.value);
                                  setPaymentMethod(event.target.value);
                                  setPaymentReference(undefined);
                                }}
                              >
                                <Label>
                                  <Span>Cash on delivery order</Span>
                                </Label>
                              </Radio>
                            </Field>
                          </Col>
                        </Row>
                        <SmallSpacer />
                      </>
                    )}
                  </>
                ))}
              </>
            )}
          </div>
        </Col>
      </Row>
    </>
  );
};

export default PaymentMethodAccordion;
