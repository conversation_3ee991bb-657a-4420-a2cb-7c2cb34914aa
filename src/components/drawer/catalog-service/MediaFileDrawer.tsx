import React, { useEffect, useState } from 'react';
import { Col, Row as _Row } from '@zendeskgarden/react-grid';
import { Paragraph, Span } from '@zendeskgarden/react-typography';
import { DrawerModal } from '@zendeskgarden/react-modals';
import {
  Checkbox,
  Field,
  Label,
  Textarea,
  Input,
} from '@zendeskgarden/react-forms';
import { RemoveBlueIcon } from '../../../utils/icons';
import styled from 'styled-components';
import { baseTheme } from '../../../themes/theme';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Button, Buttons } from '../../UI-components/Button';
import MediaDelete from '../../modal/catalog-service/MediaDelete';
import { useLocation, useParams } from 'react-router-dom';
import useAxios from '../../../hooks/useAxios';
import { useMutation } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 15px 0px;
`;
const MediaFileDrawer = ({
  refetch,
  isOpen,
  setIsOpen,
}: {
  refetch: () => void;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { id } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const axios = useAxios();
  const addToast = useToast();
  const open = () => setIsOpen(true);
  const close = () => {
    setIsOpen(false);
    setSelectedFileData(null);
  };
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [activebase, setActiveBase] = useState(false);
  const [activesmall, setActiveSmall] = useState(false);
  const [activethumbnail, setActiveThumbnail] = useState(false);
  const [activeswatch, setActiveSwatch] = useState(false);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const {
    selectedFileData,
    setSelectedFileData,
    imageTags,
    setImageTags,
    setIsMediaRemove,
    detectBrowser,
    isUpdate,
    setIsUpdate,
  } = useProductContext();
  const [fileData, setFileData] = useState<{
    id: number;
    value?: string | undefined;
    is_disabled?: boolean | undefined;
    position?: number | undefined;
    image_tags?: string[] | null;
    url?: string | undefined;
    title?: string | undefined;
    description?: string | undefined;
    delete?: boolean | undefined;
  } | null>(selectedFileData);
  const [isDeleteOpen, setIsDeleteOpen] = useState<boolean>(false);
  const [url, setUrl] = useState(fileData?.url ?? '');
  const [title, setTitle] = useState(fileData?.title ?? '');
  const [description, setDescription] = useState(fileData?.description ?? '');

  const { mutate: updateMutation, isLoading } = useMutation(
    async () => {
      const formData = new FormData();
      const metadata = {
        img1: {
          id: fileData?.id,
          value: fileData?.value,
          is_disabled: fileData?.is_disabled,
          image_tags: selectedRoles.length > 0 ? selectedRoles : [],
          url: url || '',
          title: title || '',
          description: description || '',
        },
      };
      formData.append('mediaData', JSON.stringify(metadata));

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/handle-media/${prodId}`,
        `${constants.CATALOG_KEY}/v1/catalog-admin/handle-media/${prodId}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        addToast('error', `${err?.message}`);
        refetch();
      },
      onSuccess(data, variables, context) {
        addToast('success', 'Successfully updated the file');
        close();
        setIsUpdate(!isUpdate);
        refetch();
      },
    },
  );

  const handleDelete = () => {
    setIsDeleteOpen(!isDeleteOpen);
  };

  const handleApply = () => {
    updateMutation();
  };

  const handleRoleToggle = (
    role: string,
    activeSetter: React.Dispatch<React.SetStateAction<boolean>>,
  ) => {
    setSelectedRoles((prevRoles) => {
      if (prevRoles.includes(role)) {
        return prevRoles.filter((r) => r !== role);
      } else {
        return [...prevRoles, role];
      }
    });
    activeSetter((prevActive) => !prevActive);
  };

  useEffect(() => {
    if (fileData?.image_tags) {
      if (fileData.image_tags.includes('base')) {
        setActiveBase(true);
        setSelectedRoles((prev) => [...prev, 'base']);
      }
      if (fileData.image_tags.includes('small')) {
        setActiveSmall(true);
        setSelectedRoles((prev) => [...prev, 'small']);
      }
      if (fileData.image_tags.includes('thumbnail')) {
        setActiveThumbnail(true);
        setSelectedRoles((prev) => [...prev, 'thumbnail']);
      }
      if (fileData.image_tags.includes('swatch')) {
        setActiveSwatch(true);
        setSelectedRoles((prev) => [...prev, 'swatch']);
      }
    }
  }, [fileData?.image_tags]);

  useEffect(() => {}, [selectedRoles]);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);
  useEffect(() => {
    setIsMediaRemove(false);
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '35%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
            }}
          >
            Media
          </DrawerModal.Header>
          <DrawerModal.Body>
            <>
              {/* {extension === 'image' ?
             ( */}
              <>
                <Row>
                  <Col>
                    <Label>Role</Label>
                    <div
                      style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}
                    >
                      <Buttons
                        isPrimary={activebase}
                        onClick={() => handleRoleToggle('base', setActiveBase)}
                      >
                        Base
                      </Buttons>
                      <Buttons
                        isPrimary={activesmall}
                        onClick={() =>
                          handleRoleToggle('small', setActiveSmall)
                        }
                      >
                        Small
                      </Buttons>
                      <Buttons
                        isPrimary={activethumbnail}
                        onClick={() =>
                          handleRoleToggle('thumbnail', setActiveThumbnail)
                        }
                      >
                        Thumbnail
                      </Buttons>
                      <Buttons
                        isPrimary={activeswatch}
                        onClick={() =>
                          handleRoleToggle('swatch', setActiveSwatch)
                        }
                      >
                        Swatch
                      </Buttons>
                    </div>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Field>
                      <Checkbox
                        checked={fileData?.is_disabled}
                        onChange={(e) =>
                          setFileData((prev) => ({
                            ...prev,
                            id: prev?.id || 0,
                            is_disabled: e.target.checked,
                          }))
                        }
                      >
                        <Label>Hide From Product Page</Label>
                      </Checkbox>
                    </Field>
                  </Col>
                </Row>
                <Row>
                  <Col style={{ alignItems: 'center' }}>
                    <Row>
                      <Col size={3}>
                        <Label>Delete Image</Label>
                      </Col>
                      <Col>
                        <RemoveBlueIcon
                          style={{ cursor: 'pointer' }}
                          onClick={handleDelete}
                        />
                      </Col>
                    </Row>
                    {isDeleteOpen && (
                      <>
                        <MediaDelete
                          refetch={refetch}
                          prodId={prodId}
                          drawer={isOpen}
                          setDrawer={setIsOpen}
                          visible={isDeleteOpen}
                          setVisible={setIsDeleteOpen}
                        />
                      </>
                    )}
                  </Col>
                </Row>
              </>
            </>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isBasic onClick={close}>
                Cancel
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange onClick={handleApply}>
                {isLoading ? <Spinner /> : 'Apply'}
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default MediaFileDrawer;
