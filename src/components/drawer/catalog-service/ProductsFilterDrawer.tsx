import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Label as _Label } from '@zendeskgarden/react-forms';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import styled from 'styled-components';
import { debounce, filter } from 'lodash';
import {
  useProductContext,
  ProductFilterTypes,
} from '../../../pages/catalog-service/ProductFilterContext';
import ProductDropdown from '../../dropdown/catalog-service/ProductDropdown';
import { MediaInput } from '../../UI-components/MediaInput';
import { CalenderIcon, CrossIcon } from '../../../utils/icons';
import Input from '../../UI-components/Input';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import useToast from '../../../hooks/useToast';
import { RangeInput } from '../../layouts/catalog-service/RangeInput';
import { Separator } from '@zendeskgarden/react-dropdowns';
import { useLocation, useNavigate } from 'react-router-dom';

export const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const ProductsFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
  isreset,
  setIsReset,
  setIsFilterDisabled,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<ProductFilterTypes>>;
  filters: ProductFilterTypes;
  reset: any;
  isreset: boolean;
  setIsReset: React.Dispatch<React.SetStateAction<boolean>>;
  setIsFilterDisabled: React.Dispatch<
    React.SetStateAction<{ count: number; isdisabled: boolean }>
  >;
}) => {
  const {
    visibilityList,
    manfacturerList,
    manufacturerData,
    visibilityData,
    setPage,
  } = useProductContext();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [idFrom, setIdFrom] = useState<any>(filters?.id?.from || undefined);
  const [idTo, setIdTo] = useState<any>(filters?.id?.to || undefined);
  const [priceFrom, setPriceFrom] = useState<any>(
    filters?.price?.from || undefined,
  );
  const [priceTo, setPriceTo] = useState<any>(filters?.price?.to || undefined);
  const [msrpFrom, setMsrpFrom] = useState(filters?.msrp?.from || undefined);
  const [msrpTo, setMsrpTo] = useState(filters?.msrp?.to || undefined);
  const [quantityFrom, setQuantityFrom] = useState(
    filters?.quantity?.min || undefined,
  );
  const [quantityTo, setQuantityTo] = useState(
    filters?.quantity?.max || undefined,
  );
  const [prodExpiryFrom, setProdExpiryFrom] = useState<Date | undefined>(
    filters?.product_expiry?.from || undefined,
  );
  const [prodExpiryTo, setProdExpiryTo] = useState<Date | undefined>(
    filters?.product_expiry?.to || undefined,
  );
  const [gtin, setGtin] = useState(filters?.gtin);
  const [name, setName] = useState(filters?.name);
  const [sku, setSku] = useState(filters?.sku);
  const [error, setError] = useState({});
  const defaultFilters = {
    id: {},
    price: {},
    product_expiry: {},
    quantity: {},
  };

  const addToast = useToast();

  // Product type id

  const [productType, setProductType] = useState<string | undefined>(() => {
    const typeId = filters?.type_id;

    switch (typeId) {
      case 'simple':
        return 'Simple Product';
      case 'grouped':
        return 'Grouped Product';
      case 'virtual':
        return 'Virtual Product';
      default:
        return undefined;
    }
  });

  const [inputProdType, setInputProdType] = useState('');
  const handleSelectProdType = (item: string) => {
    if (item === 'Select') {
      setProductType(undefined);
    } else {
      setProductType(item);
    }
  };
  const handleInputProdTypeChange = (value: string) => {
    setInputProdType(value);
  };

  // Backorders
  const [backorder, setBackorder] = useState<string | undefined>(
    filters?.backorders != undefined
      ? filters.backorders
        ? 'Allow Qty Below 0'
        : 'No Backorders'
      : undefined,
  );
  const [inputBackOrder, setInputBackOrder] = useState('');
  const handleSelectBackOrder = (item: string) => {
    if (item === 'Select') {
      setBackorder(undefined);
    } else {
      setBackorder(item);
    }
  };
  const handleInputBackOrderChange = (value: string) => {
    setInputBackOrder(value);
  };

  // Manufacturer
  const [manufacturer, setManufacturer] = useState<string | undefined>(
    filters?.manufacturer || undefined,
  );
  const [inputManufacturer, setInputManufacturer] = useState('');
  const handleSelectManufacturer = (item: string) => {
    if (item === 'Select Manufacturer') {
      setManufacturer(undefined);
    } else {
      setManufacturer(item);
    }
  };
  const handleInputManufacturerChange = (value: string) => {
    setInputManufacturer(value);
  };

  useEffect(() => {
    const selectedManufacturer = manufacturerData?.find(
      (val: any) => val.id === filters.manufacturer,
    );
    const manufactureValue = selectedManufacturer?.value;
    setManufacturer(manufactureValue);
  }, [manufacturerData]);

  // visibility
  const [visibility, setVisibility] = useState<string | undefined>();
  const [inputVisibility, setInputVisibility] = useState('');
  const handleSelectVisibility = (item: string) => {
    if (item === 'Select visibility') {
      setVisibility(undefined);
    } else {
      setVisibility(item);
    }
  };
  const handleInputVisibilityChange = (value: string) => {
    setInputVisibility(value);
  };

  useEffect(() => {
    const selectedVisibility = visibilityData?.find(
      (val: any) => val.id === filters.visibility,
    );
    const visibleValue = selectedVisibility?.value;
    setVisibility(visibleValue);
  }, [visibilityData]);

  // Status
  const [status, setStatus] = useState<string | undefined>(
    filters?.status != undefined
      ? filters.status
        ? 'Enabled'
        : 'Disabled'
      : undefined,
  );
  const [inputStatus, setInputStatus] = useState('');
  const handleSelectStatus = (item: string) => {
    if (item === 'Select') {
      setStatus(undefined);
    } else {
      setStatus(item);
    }
  };
  const handleInputStatusChange = (value: string) => {
    setInputStatus(value);
  };

  // Demo Available
  const [demoAval, setDemoAval] = useState<string | undefined>(
    filters?.demo_available != undefined
      ? filters.demo_available
        ? 'Enabled'
        : 'Disabled'
      : undefined,
  );
  const [inputDemo, setInputDemo] = useState('');
  const handleSelectDemo = (item: string) => {
    if (item === 'Select') {
      setDemoAval(undefined);
    } else {
      setDemoAval(item);
    }
  };
  const handleInputDemoChange = (value: string) => {
    setInputDemo(value);
  };
  // useEffect(()=>{
  //   setDemoAval(filters?.demo_available != undefined ? filters.demo_available === true ? 'True' : 'False' : undefined,)
  // },[filters])

  // Stock Status
  const [stockStatus, setStockStatus] = useState<string | undefined>(
    filters.is_in_stock != undefined
      ? filters.is_in_stock
        ? 'In Stock'
        : 'Out of Stock'
      : undefined,
  );
  const [inputStockStatus, setInputStockStatus] = useState('');
  const handleSelectStockStatus = (item: string) => {
    if (item === 'Select') {
      setStockStatus(undefined);
    } else {
      setStockStatus(item);
    }
  };
  const handleInputStockStatusChange = (value: string) => {
    setInputStockStatus(value);
  };

  const resetFilters = () => {
    setIdFrom(undefined);
    setIdTo(undefined);
    setMsrpFrom(undefined);
    setMsrpTo(undefined);
    setPriceFrom(undefined);
    setPriceTo(undefined);
    setProdExpiryFrom(undefined);
    setProdExpiryTo(undefined);
    setProductType(undefined);
    setQuantityFrom(undefined);
    setQuantityTo(undefined);
    setStatus(undefined);
    setVisibility(undefined);
    setManufacturer(undefined);
    setStockStatus(undefined);
    setName(undefined);
    setSku(undefined);
    setGtin(undefined);
    setDemoAval(undefined);
    setBackorder(undefined);
    setFilters((prev: any) => ({
      ...prev,
      id: { from: undefined, to: undefined },
      price: { from: undefined, to: undefined },
      msrp: { from: undefined, to: undefined },
      quantity: { min: undefined, max: undefined },
      product_expiry: { from: undefined, to: undefined },
      backorders: undefined,
      is_in_stock: undefined,
      status: undefined,
      type_id: undefined,
      gtin: undefined,
      name: undefined,
      sku: undefined,
      demo_available: undefined,
      manufacturer: undefined,
      visibility: undefined,
    }));
    setIsOpen(false);
    setPage(1);
    queryParams.set('page', '1');
    navigate(`?${queryParams.toString()}`, {
      replace: true,
    });
  };

  const applyFilters = () => {
    if (Object.keys(error).length > 0) {
      addToast(
        'error',
        `${Object.keys(error).join(', ')} ${Object.values(error)}`,
      );
      return;
    }
    const selectedManufacturer = manufacturerData?.find(
      (val: any) => val.value === manufacturer,
    );
    const manufactId = selectedManufacturer?.id;
    const selectedVisibility = visibilityData?.find(
      (val: any) => val.value === visibility,
    );
    const visibleId = selectedVisibility?.id;
    setFilters((prev: any) => ({
      ...prev,
      id: {
        from: idFrom,
        to: idTo,
      },
      name: name ? name : undefined,
      sku: sku ? sku : undefined,
      price: {
        from: priceFrom,
        to: priceTo,
      },
      msrp: {
        from: msrpFrom,
        to: msrpTo,
      },
      quantity: {
        min: quantityFrom,
        max: quantityTo,
      },
      product_expiry: {
        from:
          prodExpiryFrom !== undefined ? new Date(prodExpiryFrom) : undefined,
        to: prodExpiryTo !== undefined ? new Date(prodExpiryTo) : undefined,
      },
      visibility: visibleId ?? undefined,
      manufacturer: manufactId ?? undefined,
      backorders:
        backorder != undefined
          ? backorder === 'Allow Qty Below 0'
            ? true
            : false
          : undefined,
      is_in_stock:
        stockStatus != undefined
          ? stockStatus === 'In Stock'
            ? true
            : false
          : undefined,
      gtin: gtin !== '' ? gtin : undefined,
      demo_available:
        demoAval != undefined
          ? demoAval === 'Enabled'
            ? true
            : false
          : undefined,
      status: status != undefined ? (status === 'Enabled' ? 1 : 2) : undefined,
      type_id: productType
        ? productType === 'Simple Product'
          ? 'simple'
          : productType === 'Grouped Product'
          ? 'grouped'
          : productType === 'Virtual Product'
          ? 'virtual'
          : undefined
        : undefined,
    }));
    setPage(1);
    queryParams.set('page', '1');
    navigate(`?${queryParams.toString()}`, {
      replace: true,
    });
    setIsOpen(false);
  };
  const variablefilters = {
    idFrom,
    idTo,
    name,
    sku,
    priceFrom,
    priceTo,
    quantityFrom,
    quantityTo,
    prodExpiryFrom,
    prodExpiryTo,
    visibility,
    manufacturer,
    backorder,
    stockStatus,
    gtin,
    demoAval,
    status,
    productType,
    msrpFrom,
    msrpTo,
  };
  const countActiveFilters = (filters: Record<string, any>): number => {
    return Object.values(filters).filter((value) => value !== undefined).length;
  };
  const areAllFiltersUndefined = (filters: Record<string, any>): boolean => {
    return Object.values(filters).every((value) => value === undefined);
  };
  const close = () => setIsOpen(false);

  useEffect(() => {
    if (isreset) {
      setFilters(defaultFilters);
      setIsOpen(false);
      setIsReset(false);
    }
  }, [isreset]);

  useEffect(() => {
    console.log(
      'countActiveFilters(variablefilters): ',
      countActiveFilters(variablefilters),
    );
  }, [countActiveFilters(variablefilters)]);

  useEffect(() => {
    setIsFilterDisabled({
      count: countActiveFilters(variablefilters),
      isdisabled: areAllFiltersUndefined(variablefilters),
    });
  }, []);
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '30%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
              fontSize: `${baseTheme.fontSizes.lg}`,
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
            <div>
              <Row>
                <Col>
                  <RangeInput
                    label="ID Range"
                    fromValue={idFrom}
                    toValue={idTo}
                    onFromChange={setIdFrom}
                    onToChange={setIdTo}
                    error={
                      idTo !== undefined &&
                      idFrom !== undefined &&
                      idTo < idFrom
                        ? 'To value must be greater than From value'
                        : undefined
                    }
                    setError={setError}
                    error_value={error}
                  />
                </Col>
              </Row>
              <Separator />
              <Row>
                <Col>
                  <RangeInput
                    label="Price Range"
                    fromValue={priceFrom}
                    toValue={priceTo}
                    onFromChange={setPriceFrom}
                    onToChange={setPriceTo}
                    error={
                      priceTo !== undefined &&
                      priceFrom !== undefined &&
                      priceTo < priceFrom
                        ? 'To value must be greater than From value'
                        : undefined
                    }
                    setError={setError}
                    error_value={error}
                  />
                </Col>
              </Row>
              <Separator />
              <Row>
                <Col>
                  <RangeInput
                    label="Request Price"
                    fromValue={msrpFrom}
                    toValue={msrpTo}
                    onFromChange={setMsrpFrom}
                    onToChange={setMsrpTo}
                    error={
                      msrpTo !== undefined &&
                      msrpFrom !== undefined &&
                      msrpTo < msrpFrom
                        ? 'To value must be greater than From value'
                        : undefined
                    }
                    setError={setError}
                    error_value={error}
                  />
                </Col>
              </Row>
              <Separator />
              <Row>
                <Col>
                  <RangeInput
                    label="Quantity"
                    fromValue={quantityFrom}
                    toValue={quantityTo}
                    onFromChange={setQuantityFrom}
                    onToChange={setQuantityTo}
                    error={
                      quantityTo !== undefined &&
                      quantityFrom !== undefined &&
                      quantityTo < quantityFrom
                        ? 'To value must be greater than From value'
                        : undefined
                    }
                    setError={setError}
                    error_value={error}
                  />
                </Col>
              </Row>
              <Separator />
              <Row>
                <Col>
                  <ColSection>
                    <Label>Product Expiry</Label>
                  </ColSection>
                  <ColSection>
                    <div style={{ width: '48%' }}>
                      <Datepicker
                        value={prodExpiryFrom}
                        onChange={setProdExpiryFrom}
                      >
                        <MediaInput
                          end={
                            <div
                              style={{
                                alignItems: 'center',
                                display: 'flex',
                                justifyContent: 'center',
                              }}
                            >
                              {prodExpiryFrom ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setProdExpiryFrom(undefined);
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </div>
                    <div style={{ width: '48%' }}>
                      <Datepicker
                        value={prodExpiryTo}
                        onChange={setProdExpiryTo}
                      >
                        <MediaInput
                          end={
                            <div
                              style={{
                                alignItems: 'center',
                                display: 'flex',
                                justifyContent: 'center',
                              }}
                            >
                              {prodExpiryTo ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => setProdExpiryTo(undefined)}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </div>
                  </ColSection>
                </Col>
              </Row>
              <Separator />
              <Row>
                <Col>
                  <ColSection>
                    <Label>Product Type</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={productType}
                      onSelect={handleSelectProdType}
                      inputValue={inputProdType}
                      onInputValueChange={handleInputProdTypeChange}
                      options={[
                        'Select',
                        'Simple Product',
                        'Grouped Product',
                        'Virtual Product',
                      ]}
                    />
                  </div>
                </Col>
              </Row>
              {/* <Row>
                <Col>
                  <ColSection>
                      <Label>Name</Label>
                  </ColSection>
                  <div style={{margin: '0px 20px'}}>
                      <Input
                        value={name}
                        onChange={(e) => {
                          setName(e.target.value);
                        }}
                      />
                  </div>
                </Col>
              </Row> */}
              <Row>
                <Col>
                  <ColSection>
                    <Label>SKU</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <Input
                      value={sku}
                      onChange={(e) => {
                        setSku(e.target.value);
                      }}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Status</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={status}
                      onSelect={handleSelectStatus}
                      inputValue={inputStatus}
                      onInputValueChange={handleInputStatusChange}
                      options={['Select', 'Enabled', 'Disabled']}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Backorders</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={backorder}
                      onSelect={handleSelectBackOrder}
                      inputValue={inputBackOrder}
                      onInputValueChange={handleInputBackOrderChange}
                      options={['Select', 'Allow Qty Below 0', 'No Backorders']}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Brand</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={manufacturer}
                      onSelect={handleSelectManufacturer}
                      inputValue={inputManufacturer}
                      onInputValueChange={handleInputManufacturerChange}
                      options={manfacturerList}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Visibility</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={visibility}
                      onSelect={handleSelectVisibility}
                      inputValue={inputVisibility}
                      onInputValueChange={handleInputVisibilityChange}
                      options={visibilityList}
                    />
                  </div>
                </Col>
              </Row>

              <Row>
                <Col>
                  <ColSection>
                    <Label>Gtin</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <Input
                      value={gtin}
                      onChange={(e) => setGtin(e.target.value)}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Demo Available</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={demoAval}
                      onSelect={handleSelectDemo}
                      inputValue={inputDemo}
                      onInputValueChange={handleInputDemoChange}
                      options={['Select', 'Enabled', 'Disabled']}
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Label>Stock Status</Label>
                  </ColSection>
                  <div style={{ margin: '0px 20px' }}>
                    <ProductDropdown
                      selectedItem={stockStatus}
                      onSelect={handleSelectStockStatus}
                      inputValue={inputStockStatus}
                      onInputValueChange={handleInputStockStatusChange}
                      options={['Select', 'In Stock', 'Out of Stock']}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                onClick={resetFilters}
                disabled={areAllFiltersUndefined(variablefilters)}
              >
                Reset Filter
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button
                disabled={areAllFiltersUndefined(variablefilters)}
                isPrimary
                isOrange
                onClick={applyFilters}
              >
                Apply Filter
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ProductsFilterDrawer;
