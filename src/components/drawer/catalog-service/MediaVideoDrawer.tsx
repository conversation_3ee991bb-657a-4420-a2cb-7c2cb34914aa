import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Col, Row as _Row } from '@zendeskgarden/react-grid';
import { Paragraph, Span } from '@zendeskgarden/react-typography';
import { DrawerModal } from '@zendeskgarden/react-modals';
import {
  Checkbox,
  Field,
  Label as _Label,
  Textarea,
  Input,
  FileUpload,
} from '@zendeskgarden/react-forms';
import {
  CrossIcon,
  Plus,
  PlusIcon,
  RemoveBlueIcon,
} from '../../../utils/icons';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Button, Buttons } from '../../UI-components/Button';
import MediaDelete from '../../modal/catalog-service/MediaDelete';
import { useLocation, useParams } from 'react-router-dom';
import useAxios from '../../../hooks/useAxios';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { useDropzone } from 'react-dropzone';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 15px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const StyledFileUpload = styled(FileUpload)`
  min-height: ${(p) => p.theme.space.base * 60}px;
  font-size: 14px;
  pointer-events: ${(props) => (props.disabled ? 'none' : 'auto')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
`;

const StyledThumbnailUpload = styled(FileUpload)`
  height: ${(p) => p.theme.space.base * 20}px;
`;

interface VideoDetail {
  title?: string;
  description?: string;
  default_thumbnail?: string;
  medium_thumbnail?: string;
}

interface MediaGallery {
  id: number;
  value: string;
  is_disabled?: boolean;
  position?: number;
  url?: string;
  image_tags: string;
  title?: string;
  description?: string;
}
interface FileData {
  files: File;
  value?: string;
  position?: number;
  image_tags?: string[] | null;
  title?: string;
  is_disabled?: boolean;
  description?: string;
  url?: string;
  delete?: boolean;
}

const MediaVideoDrawer = ({
  media,
  refetch,
  isOpen,
  setIsOpen,
}: {
  media: MediaGallery[];
  refetch: () => void;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const axios = useAxios();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const addToast = useToast();
  const {
    selectedFileData,
    setSelectedFileData,
    setIsMediaRemove,
    detectBrowser,
    isUpdate,
    setIsUpdate,
    contextProdData,
    setContextProdData,
  } = useProductContext();
  const close = () => {
    setSelectedFileData(null);
    setIsOpen(false);
  };
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const [isError, setIsError] = useState('');
  const [isDisabled, setIsDisabled] = useState<boolean>(true);

  const [fileData, setFileData] = useState<{
    id: number;
    value?: string | undefined;
    is_disabled?: boolean | undefined;
    position?: number | undefined;
    image_tags?: string[] | null;
    url?: string | undefined;
    title?: string | undefined;
    description?: string | undefined;
    delete?: boolean | undefined;
  } | null>(selectedFileData);

  const [videoFileData, setVideoFileData] = useState<VideoDetail>({
    title: fileData?.title || '',
    description: fileData?.description || '',
    default_thumbnail: fileData?.url || '',
    medium_thumbnail: fileData?.url || '',
  });

  const [isDeleteOpen, setIsDeleteOpen] = useState<boolean>(false);
  const [url, setUrl] = useState(fileData?.url ?? '');
  const [mediumThumbnail, setMediumThumbnail] = useState<any>('');
  const [defaultThumbnail, setDefaultThumbnail] = useState<any>('');
  const [files, setFiles] = useState<FileData[]>([]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const filteredFiles = acceptedFiles.filter(
        (file) =>
          (file.type.startsWith('image/') &&
            /\.(png|jpe?g)$/i.test(file.name)) ||
          (file.type === 'video/mp4' && /\.mp4$/i.test(file.name)),
      );

      const remainingSpace = 5 - files.length;
      const newFiles = filteredFiles.slice(0, remainingSpace).map((file) => ({
        files: file,
        value: '',
        position: 1,
        image_tags: null,
        title: '',
        is_disabled: false,
        description: '',
        url: '',
      }));
      setFiles([...files, ...newFiles]);
    },
    [files],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.webp', '.mp4'] },
    onDrop,
  });

  const getVideoDetail = async () => {
    const response = await axios.get<VideoDetail>(
      // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/yt-video/meta?url=${url}`,
      `/v1/catalog-admin/video-info?url=${url}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          'x-api-key': `${constants.CATALOG_KEY}`,
          'admin-identifier': `${localStorage.getItem('username')}`,
        },
      },
    );
    return response as unknown as VideoDetail;
  };

  const { mutate: mutateDetail } = useMutation(getVideoDetail, {
    onError: (err: any) => {
      setVideoFileData({
        title: '',
        description: '',
        default_thumbnail: '',
        medium_thumbnail: '',
      });
      setIsDisabled(true);
      addToast('error', `${err.message}`);
      setMediumThumbnail('');
      setDefaultThumbnail('');
      setIsError('error');
    },
    onSuccess(data) {
      setIsError('none');
      setIsDisabled(false);
      setMediumThumbnail(data.medium_thumbnail);
      setDefaultThumbnail(data.default_thumbnail);
    },
  });

  const { mutate: mutateVideo, isLoading: isVideoLoading } = useMutation(
    getVideoDetail,
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
        setMediumThumbnail('');
        setDefaultThumbnail('');
        setIsError('error');
        setIsDisabled(true);
      },
      onSuccess(data) {
        setVideoFileData(data);
        setIsError('none');
        setIsDisabled(false);
        setMediumThumbnail(data.medium_thumbnail);
        setDefaultThumbnail(data.default_thumbnail);
      },
    },
  );

  const { mutate: addMutation, isLoading: isAddLoading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}/videos`,
        `/v1/catalog-admin/save-video-media/${prodId}`,
        {
          title: videoFileData?.title,
          description: videoFileData?.description,
          thumbnail: videoFileData?.default_thumbnail,
          url: url,
          medium_thumbnail: videoFileData?.medium_thumbnail,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err?.message}`);
      },
      onSuccess(data, variables, context) {
        addToast('success', 'Successfully Uploaded the file');
        refetch();
        setIsUpdate(!isUpdate);
        setIsOpen(false);
      },
    },
  );

  const { mutate: updateMutation, isLoading: isUpdateLoading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}/videos`,
        `/v1/catalog-admin/save-video-media/${prodId}`,
        {
          db_video_id: selectedFileData?.id,
          title: videoFileData?.title,
          description: videoFileData?.description,
          value: videoFileData?.default_thumbnail,
          url: url,
          // medium_thumbnail: videoFileData?.medium_thumbnail,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err?.response?.data?.message}`);
      },
      onSuccess(data, variables, context) {
        addToast('success', 'Successfully updated the file');
        refetch();
        setIsUpdate(!isUpdate);
      },
    },
  );
  const handleDelete = () => {
    setIsDeleteOpen(!isDeleteOpen);
  };

  const handeChangeUrl = () => {
    if (url != '') {
      setIsError('none');
      mutateVideo();
    } else {
      addToast('warning', 'Please enter the YouTube Url!');
    }
  };

  const handleApply = () => {
    if (selectedFileData && selectedFileData?.id) {
      updateMutation();
    } else {
      addMutation();
    }
  };
  useEffect(() => {
    if (fileData?.url && fileData?.id) {
      mutateDetail();
    }
  }, [fileData]);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);

  useEffect(() => {
    setIsMediaRemove(false);
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);

  useEffect(() => {
    console.log('fileData: ', fileData);
  }, [fileData]);
  return (
    <div>
      <Row>
        <Col>
          <DrawerModal style={{ width: '35%' }} isOpen={isOpen} onClose={close}>
            <DrawerModal.Header
              tag="h2"
              style={{
                display: 'flex',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                color: 'white',
                justifyContent: 'center',
              }}
            >
              Media
            </DrawerModal.Header>
            <DrawerModal.Body>
              <>
                <Row>
                  <Col>
                    <StyledFileUpload>
                      {fileData?.value == null ? (
                        'Preview'
                      ) : (
                        <img
                          style={{
                            // objectFit: 'contain',
                            // height: '100%',
                            padding: '10px',
                          }}
                          src={fileData?.value}
                          alt="thumbnail"
                        />
                      )}
                    </StyledFileUpload>
                  </Col>
                </Row>
                <Col>
                  <Label>Preview Image</Label>
                </Col>
                <Row>
                  <Col>
                    <StyledThumbnailUpload>
                      {defaultThumbnail === '' ? (
                        ''
                      ) : (
                        <img
                          src={defaultThumbnail}
                          style={{
                            objectFit: 'contain',
                            height: '100%',
                          }}
                          alt="thumbnail"
                        />
                      )}
                    </StyledThumbnailUpload>
                  </Col>
                  <Col>
                    <StyledThumbnailUpload>
                      {mediumThumbnail != '' ? (
                        <img
                          src={mediumThumbnail}
                          style={{
                            objectFit: 'contain',
                            height: '100%',
                          }}
                          alt="thumbnail"
                        />
                      ) : (
                        ''
                      )}
                    </StyledThumbnailUpload>
                  </Col>
                  <Col></Col>
                </Row>
                <Row>
                  <Col>
                    <Field>
                      <Label>URL</Label>
                      <Input
                        value={url}
                        onChange={(e) => {
                          setIsError('error');
                          setUrl(e.target.value);
                          setIsDisabled(true);
                        }}
                      />
                    </Field>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    {isVideoLoading ? (
                      <Button disabled>
                        <Spinner />
                      </Button>
                    ) : (
                      <Button size="small" isPrimary onClick={handeChangeUrl}>
                        Get Video Information
                      </Button>
                    )}
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Field>
                      <Label>Title</Label>
                      <Input
                        value={videoFileData?.title}
                        onChange={(e) =>
                          setVideoFileData((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                      />
                    </Field>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Field>
                      <Label>Description</Label>
                      <Textarea
                        rows={8}
                        value={videoFileData?.description}
                        onChange={(e) => {
                          setVideoFileData({
                            ...videoFileData,
                            description: e.target.value,
                          });
                        }}
                      />
                    </Field>
                  </Col>
                </Row>
                {fileData?.id && (
                  <Row>
                    <Col style={{ alignItems: 'center' }}>
                      <Row>
                        <Col size={3}>
                          <Label>Delete Video</Label>
                        </Col>
                        <Col>
                          <RemoveBlueIcon
                            style={{ cursor: 'pointer' }}
                            onClick={handleDelete}
                          />
                        </Col>
                      </Row>
                      {isDeleteOpen && (
                        <>
                          <MediaDelete
                            refetch={refetch}
                            prodId={prodId}
                            drawer={isOpen}
                            setDrawer={setIsOpen}
                            visible={isDeleteOpen}
                            setVisible={setIsDeleteOpen}
                          />
                        </>
                      )}
                    </Col>
                  </Row>
                )}
              </>
            </DrawerModal.Body>
            <DrawerModal.Footer>
              <DrawerModal.FooterItem>
                <Button isBasic onClick={close}>
                  Cancel
                </Button>
              </DrawerModal.FooterItem>
              <DrawerModal.FooterItem>
                <Button
                  isPrimary
                  disabled={isDisabled}
                  isOrange
                  onClick={handleApply}
                >
                  {isUpdateLoading || isAddLoading ? <Spinner /> : 'Apply'}
                </Button>
              </DrawerModal.FooterItem>
            </DrawerModal.Footer>
            <DrawerModal.Close style={{ color: 'white' }} />
          </DrawerModal>
        </Col>
      </Row>
      {/* { isThumbnailOpen && (
          <ThumbnailUpload
              refetch={refetch}
              visible={isThumbnailOpen}
              setVisible={setIsThumbnailOpen}
          />
      )
      } */}
    </div>
  );
};

export default MediaVideoDrawer;
