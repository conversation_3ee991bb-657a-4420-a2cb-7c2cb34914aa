import React, { useState, useEffect } from 'react';
import {
  Dropdown,
  Multiselect,
  Field,
  Menu,
  Item,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../../themes/theme';

const PAGES = [
  'home',
  'pdp',
  'shorts',
  'category',
  'brand',
  'membership',
  'my_membership',
  'investor_relation',
  'waldent_dealership',
  'magazine',
  'payment',
  'reward_zone',
  'refer_and_earn',
  'account',
  'cart',
  'thank_you',
];

const PageTypeDropdown = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  const [selectedPages, setSelectedPages] = useState<string[]>([]);

  useEffect(() => {
    if (value) {
      setSelectedPages(value.split(','));
    }
  }, [value]);

  const handleSelectionChange = (devices: string[]) => {
    const newValue = devices.join(',');
    onChange(newValue);
    setSelectedPages(devices);
  };

  function formatText(text: string) {
    return text
      .toLowerCase()
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, (char) => char.toUpperCase());
  }

  return (
    <>
      <Dropdown
        selectedItems={selectedPages}
        onSelect={(items) => handleSelectionChange(items)}
        downshiftProps={{ defaultHighlightedIndex: 0 }}
      >
        <Field>
          <Label style={{ color: baseTheme.colors.primaryHue }}>
            Active In Page{' '}
            <span style={{ color: baseTheme.colors.deepRed }}> *</span>
          </Label>
          <Multiselect
            renderItem={({ value, removeValue }: any) => (
              <Tag>
                <span>{formatText(value)}</span>
                <Tag.Close onClick={() => removeValue()} />
              </Tag>
            )}
          />
        </Field>
        <Menu>
          {PAGES.map((device) => (
            <Item key={device} value={device}>
              {formatText(device)}
            </Item>
          ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default PageTypeDropdown;
