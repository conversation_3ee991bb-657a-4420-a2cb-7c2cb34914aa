import styled from 'styled-components';
import { SModal as _SModal } from '../../UI-components/Modal';
import { Header as _Header } from '@zendeskgarden/react-modals';

import {
  Dropdown,
  Item,
  Menu,
  Select,
  Label as _Dlabel,
  Field as _DField,
} from '@zendeskgarden/react-dropdowns';

import {
  Field,
  Message,
  Toggle,
  Input as _Input,
  Label as _Label,
} from '@zendeskgarden/react-forms';

export const AddressInput = styled(_Input)`
  margin-bottom: 12px;
  &:not(:focus) {
    background-color: ${(p) => p.theme.components.TextInput.bgColor};
  }
`;

export const DField = styled(_DField)`
  & > [data-garden-id='forms.faux_input'] {
    margin: 0;
  }
`;

export const DLabel = styled(_Dlabel)`
  color: ${(p) => p.theme.colors.deepBlue};
`;

export const SModal = styled(_SModal)``;

export const Header = styled(_Header)`
  background-color: ${(p) => p.theme.colors.deepBlue};
`;
