import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import {
  Checkbox,
  Field,
  FileUpload,
  Input,
  Label,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import ProductDropdown from '../../dropdown/catalog-service/ProductDropdown';
import useAxios from '../../../hooks/useAxios';
import routes from '../../../constants/routes';
import useToast from '../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import { useLocation, useParams } from 'react-router-dom';
import {
  CrossIcon,
  DeleteIcon,
  DropIcon,
  Plus,
  UploadPlus,
  XCircle,
} from '../../../utils/icons';
import { useDropzone } from 'react-dropzone';
import { JUSTIFY_CONTENT } from '@zendeskgarden/react-grid/dist/typings/types';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

// const StyledFileUpload = styled(FileUpload)`
//   min-height: ${(p) => p.theme.space.base * 20}px;
//   font-size: 14px;
//   pointer-events: ${(props) => (props.disabled ? 'none' : 'auto')};
//   cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
// `;

const StyledFileUpload = styled(FileUpload)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${baseTheme.components.dimension.width.base150};
  height: ${baseTheme.components.dimension.width.base150};
  border: 2px dashed ${baseTheme.colors.grey};
  border-radius: 8px;
  background-color: ${({ isDragging }) =>
    isDragging ? baseTheme.colors.lightGrey : 'transparent'};
`;

const FilesUpload = styled.div`
  position: relative;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid ${baseTheme.colors.grey};
  border-radius: 8px;
  overflow: hidden;
`;

const Image = styled.img`
  width: ${baseTheme.components.dimension.width.base200};
  height: ${baseTheme.components.dimension.width.base200};
  /* object-fit: cover; */
`;

const Video = styled.video`
  width: ${baseTheme.components.dimension.width.base200};
  height: ${baseTheme.components.dimension.width.base200};
  object-fit: cover;
`;

const File = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: ${baseTheme.components.dimension.width.base200};
  height: ${baseTheme.components.dimension.width.base200};
  /* cursor: pointer; */
`;

interface MediaGallery {
  id: number;
  value: string;
  is_disabled?: boolean;
  position?: number;
  url?: string;
  image_tags: string;
  title?: string;
  description?: string;
}

interface FileData {
  files: File;
  value?: string;
  position?: number;
  image_tags?: string[] | null;
  title?: string;
  is_disabled?: boolean;
  description?: string;
  url?: string;
  delete?: boolean;
}

interface MediaUploadProps {
  media: MediaGallery[];
  refetch: () => void;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  media,
  refetch,
  visible,
  setVisible,
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { id } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const { setSelectedFileData, detectBrowser, isUpdate, setIsUpdate } =
    useProductContext();
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const [files, setFiles] = useState<FileData[]>([]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const filteredFiles = acceptedFiles.filter(
        (file) =>
          (file.type.startsWith('image/') &&
            /\.(png|jpe?g|webp)$/i.test(file.name)) ||
          (file.type === 'video/mp4' && /\.mp4$/i.test(file.name)),
      );

      const remainingSpace = 5 - files.length;
      const newFiles = filteredFiles
        .slice(0, remainingSpace)
        .map((file, index) => ({
          files: file,
          value: '',
          // position: media.length + index + 1,
          image_tags: null,
          title: '',
          is_disabled: false,
          description: '',
          url: '',
        }));
      setFiles([...files, ...newFiles]);
    },
    [files],
  );
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.webp'] },
    onDrop,
  });

  const handleFileClick = (fileData: any) => {
    setSelectedFileData(fileData);
  };

  const handleDeleteClick = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };
  function createNewFileWithName(file: File, newName: string): File {
    const blob = file.slice(0, file.size, file.type);
    return new globalThis.File([blob], newName, { type: file.type });
  }

  const { mutate: uploadMutation, isLoading } = useMutation(
    async () => {
      const formData = new FormData();
      const metafiles: any = {};
      files.forEach((fileData) => {
        const filename = fileData.files.name.substring(
          0,
          fileData.files.name.lastIndexOf('.'),
        );
        const randomFileName = filename
          .replace(/[^\x00-\x7F]/g, '')
          .replaceAll(' ', '');
        const newFileName = randomFileName + '.jpg';

        const renamedFile = createNewFileWithName(fileData.files, newFileName);
        formData.append('files', renamedFile);
        const fileName = renamedFile.name.substring(
          0,
          renamedFile.name.lastIndexOf('.'),
        );
        metafiles[fileName] = {
          is_disabled: fileData.is_disabled,
          position: fileData.position,
          image_tags: [],
        };
      });
      formData.append('mediaData', JSON.stringify(metafiles));
      if (!formData.has('files')) {
        addToast(
          'error',
          'No files to upload. Please add files before uploading.',
        );
        return null;
      }

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/handle-media/${prodId}`,
        `/v1/catalog-admin/handle-media/${prodId}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `Error found in uploading file.`);
        // refetch();
      },
      onSuccess(data: any) {
        if (data) {
          addToast('success', 'Successfully uploaded the file');
          setVisible(false);
          refetch();
          setIsUpdate(!isUpdate);
        }
      },
    },
  );

  const handleUpload = () => {
    if (files.length > 0) {
      uploadMutation();
    } else {
      addToast('info', 'Please add files!');
    }
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);

  return (
    <div>
      {visible && (
        <Modal isLarge onClose={() => setVisible(false)}>
          <Header
            tag="h2"
            style={{
              display: 'flex',
              color: '#FFF',
              fontSize: '20px',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              justifyContent: 'center',
            }}
          >
            Upload Image
          </Header>
          <Body>
            <Row>
              {files.map((file, index) => (
                <Col key={index}>
                  <File onClick={() => handleFileClick(file)}>
                    <>
                      <FilesUpload>
                        <Image
                          src={URL.createObjectURL(file.files)}
                          alt={file.files.name}
                        />
                        <XCircle
                          style={{
                            position: 'absolute',
                            width: '20px',
                            height: '20px',
                            top: '5px',
                            right: '5px',
                            cursor: 'pointer',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(index);
                          }}
                        />
                      </FilesUpload>
                    </>
                  </File>
                </Col>
              ))}
              <Col style={{ display: 'flex', justifyContent: 'center' }}>
                <Field
                  style={{
                    display: 'flex',
                    width: `${baseTheme.components.dimension.width.base200}`,
                    height: `${baseTheme.components.dimension.width.base200}`,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <StyledFileUpload
                    {...getRootProps()}
                    isDragging={isDragActive}
                  >
                    {isDragActive ? (
                      <span>Drop files here</span>
                    ) : (
                      <div>
                        <UploadPlus />
                      </div>
                    )}
                    <Input {...getInputProps()} />
                  </StyledFileUpload>
                </Field>
              </Col>
            </Row>
          </Body>
          <Footer style={{ justifyContent: 'center' }}>
            <FooterItem>
              <Button onClick={handleUpload} isPrimary>
                {isLoading ? <Spinner /> : 'Upload'}
              </Button>
            </FooterItem>
          </Footer>
          <Close aria-label="Close modal" style={{ color: 'white' }} />
        </Modal>
      )}
    </div>
  );
};

export default MediaUpload;
