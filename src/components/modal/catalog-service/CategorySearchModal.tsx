import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { Anchor, Button } from '../../UI-components/Button';
import {
  Header,
  Modal,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Input } from '@zendeskgarden/react-forms';
import { Item, Menu, Dropdown, Trigger } from '@zendeskgarden/react-dropdowns';
import { debounce, identity } from 'lodash';
import { baseTheme } from '../../../themes/theme';
import { DeleteIcon, DownIcon, SearchIcon } from '../../../utils/icons';
import styled from 'styled-components';
import { useInfiniteQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import { Product } from './types';
import { Spinner } from '@zendeskgarden/react-loaders';
import { IconButton } from '../../UI-components/IconButton';
import constants from '../../../constants';
import useOutsideClick from '../../../hooks/useOutsideClick';
import krakendPaths from '../../../constants/krakendPaths';

// Search type options
const searchTypes = [
  { value: 'name', label: 'Name' },
  { value: 'sku', label: 'SKU' },
  { value: 'id', label: 'Product ID' },
];

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: 16px;
`;

const SearchInputWrapper = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
`;

const SuggestionWrapper = styled.div`
  position: absolute;
  top: 100%;
  width: 100%;
  max-height: 320px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  z-index: 10;
  padding: 8px;
`;

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
`;

const ProductCard = styled.div<{ isSelected: boolean }>`
  padding: 12px;
  cursor: pointer;
  background-color: ${({ isSelected }) => (isSelected ? '#eef8f7' : '#fff')};
  border: 1px solid
    ${({ isSelected }) =>
      isSelected ? baseTheme.colors.primaryHue : '#e0e0e0'};
  border-radius: 8px;
  box-shadow: ${({ isSelected }) =>
    isSelected
      ? '0 2px 8px rgba(0, 115, 92, 0.15)'
      : '0 1px 4px rgba(0, 0, 0, 0.05)'};
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: ${({ isSelected }) =>
      isSelected ? '#e5f4f2' : '#f9f9f9'};
    border-color: ${({ isSelected }) =>
      isSelected ? baseTheme.colors.primaryHue : '#c0c0c0'};
    transform: translateY(-2px);
  }

  &:after {
    content: ${({ isSelected }) => (isSelected ? '"\u2713"' : '""')};
    position: absolute;
    top: 8px;
    right: 8px;
    color: ${baseTheme.colors.deepBlue};
    font-weight: bold;
    background: ${({ isSelected }) =>
      isSelected ? 'rgba(0, 115, 92, 0.1)' : 'transparent'};
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: ${({ isSelected }) => (isSelected ? '1' : '0')};
    transition: opacity 0.2s ease;
  }
`;

const SelectedProductsHeader = styled.h3`
  margin: 16px 0 12px;
  font-size: 14px;
  color: ${baseTheme.colors.grey[800]};
  font-weight: 600;
`;

const EmptySelectionMessage = styled.div`
  text-align: center;
  padding: 24px;
  color: ${baseTheme.colors.grey[600]};
  font-style: italic;
  background: ${baseTheme.colors.grey[100]};
  border-radius: 8px;
`;

const SelectedCard = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:hover {
    background-color: #fafafa;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  }

  & > *:not(:last-child) {
    margin-right: 12px;
  }
`;

const ImageField = styled.img`
  height: 50px;
  width: 50px;
  object-fit: cover;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f5f5f5;
  padding: 2px;
`;

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
`;

const ProductName = styled.div`
  font-weight: 500;
  color: ${baseTheme.colors.grey[800]};
  margin-bottom: 4px;
  line-height: 1.3;
`;

const ProductSku = styled.div`
  font-size: 12px;
  color: ${baseTheme.colors.grey[600]};
`;

const RemoveButton = styled(IconButton)`
  margin-left: auto;
  min-height: 32px;
  min-width: 32px;
  height: 32px;
  width: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
`;
const defaultPlacholder =
  'https://www.dentalkart.com/media/catalog/product/no_selection';

const CategorySearchFilter = ({
  close,
  loading,
  setProducts,
}: {
  close: () => void;
  setProducts: (products: Product[]) => any;
  loading?: boolean;
}) => {
  const [rotatedType, setRotatedType] = useState<boolean>();
  const [searchContent, setsearchContent] = useState<string>('');
  const [selectedProducts, setselectedProducts] = useState<Product[]>([]);
  const [searchType, setsearchType] = useState<'name' | 'id' | 'sku'>('name');
  const [content, setcontent] = useState<string>();
  const [showSuggestion, setshowSuggestion] = useState(false);
  const suggestionRef = useRef<HTMLDivElement>(null);
  const axios = useAxios();

  const { data, isLoading, isFetching, fetchNextPage, hasNextPage } =
    useInfiniteQuery({
      queryKey: ['product-list', content, searchType],
      queryFn: async ({ pageParam = 1 }) => {
        const response = content
          ? await axios.post(
              // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
              `/v1/catalog-admin/search-products`,
              {
                product_ids:
                  searchType === 'id' && content ? [+content] : undefined,
                search_by_keyword:
                  searchType !== 'id'
                    ? {
                        field: searchType,
                        value: content.trim(),
                      }
                    : undefined,
                filters: {},
                pagination: {
                  page: pageParam,
                  size: 10,
                },
              },
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                  admin_identifier: `${localStorage.getItem('username')}`,
                  'x-api-key': `${constants.CATALOG_KEY}`,
                },
              },
            )
          : {};
        return response as any;
      },
      getNextPageParam: (lastPage) => {
        return lastPage?.page_no < lastPage?.pages_count
          ? lastPage?.page_no + 1
          : false;
      },
    });

  useOutsideClick(suggestionRef, () => {
    setshowSuggestion(false);
  });

  return (
    <Modal style={{ width: '900px' }} onClose={close}>
      <Header
        style={{
          backgroundColor: baseTheme.colors.deepBlue,
          color: 'white',
          justifyContent: 'space-between',
          display: 'flex',
          alignItems: 'center',
          padding: '16px 24px',
        }}
        tag="h2"
      >
        <span>Product Search</span>
      </Header>
      <Body
        style={{
          padding: '24px',
          // maxHeight: '70vh',
          minHeight: '400px',
          overflow: 'hidden',
        }}
      >
        <SearchContainer>
          <SearchInputWrapper>
            <Col lg={9}>
              <Input
                placeholder={`Search by ${searchType}...`}
                value={searchContent}
                onChange={(e) => setsearchContent(e.target.value)}
                onFocus={() => setshowSuggestion(true)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (searchContent) {
                      setcontent(searchContent);
                      setshowSuggestion(true);
                    }
                  }
                }}
              />
            </Col>
            <Col lg={3}>
              <Dropdown
                onSelect={(item) => setsearchType(item)}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') &&
                  setRotatedType(options.isOpen)
                }
              >
                <Trigger>
                  <Button
                    isPrimary
                    style={{ width: '100%', justifyContent: 'space-between' }}
                  >
                    {searchType ? `By ${searchType}` : 'Select'}
                    <Button.EndIcon isRotated={rotatedType}>
                      <DownIcon
                        style={{
                          height: baseTheme.iconSizes.md,
                          width: baseTheme.iconSizes.md,
                        }}
                      />
                    </Button.EndIcon>
                  </Button>
                </Trigger>
                <Menu
                  style={{
                    width: '150px',
                    transform: 'translateX(0px)',
                    borderRadius: baseTheme.borderRadii.md,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <Item value="name">Name</Item>
                  <Item value="sku">SKU</Item>
                  <Item value="id">Product ID</Item>
                </Menu>
              </Dropdown>
            </Col>
          </SearchInputWrapper>

          {(isLoading || data) && showSuggestion && (
            <SuggestionWrapper ref={suggestionRef}>
              {data?.pages.map((page) =>
                page?.items?.map((p: Product, ind: number) => {
                  return (
                    <ProductCard
                      onClick={() => {
                        if (
                          !selectedProducts.find(
                            (product) => product.id === p.id,
                          )
                        ) {
                          setselectedProducts((prev) => [...prev, p]);
                        }
                        setshowSuggestion(false);
                      }}
                      isSelected={selectedProducts.some(
                        (prod) => prod.id === p.id,
                      )}
                      key={ind}
                    >
                      <Row>
                        <Col size={2}>
                          <ImageField src={defaultPlacholder} />
                        </Col>
                        <Col size={10}>
                          <ProductInfo>
                            <ProductName>
                              {p?.attributes_list?.name}
                            </ProductName>
                            <ProductSku>SKU: {p?.sku || 'N/A'}</ProductSku>
                          </ProductInfo>
                        </Col>
                      </Row>
                    </ProductCard>
                  );
                }),
              )}
              {(isFetching || isLoading) && (
                <Row justifyContent="center" style={{ padding: '12px' }}>
                  <Spinner size="small" />
                </Row>
              )}
              {hasNextPage && !isFetching && (
                <Row justifyContent="center" style={{ padding: '8px 0' }}>
                  <Button isBasic onClick={() => fetchNextPage()} size="small">
                    Load More
                  </Button>
                </Row>
              )}
            </SuggestionWrapper>
          )}
        </SearchContainer>

        <div style={{ marginTop: '24px' }}>
          <SelectedProductsHeader>
            Selected Products ({selectedProducts.length})
          </SelectedProductsHeader>

          <ProductsGrid>
            {selectedProducts.length === 0 ? (
              <EmptySelectionMessage>
                No products selected. Search and select products above.
              </EmptySelectionMessage>
            ) : (
              selectedProducts.map((product, ind) => {
                return (
                  <SelectedCard key={ind}>
                    <ImageField src={defaultPlacholder} />
                    <ProductInfo>
                      <ProductName>
                        {product?.attributes_list?.name}
                      </ProductName>
                      <ProductSku>SKU: {product?.sku || 'N/A'}</ProductSku>
                    </ProductInfo>
                    <RemoveButton
                      onClick={() =>
                        setselectedProducts((prev) =>
                          prev.filter((p) => p.id !== product.id),
                        )
                      }
                      isDanger
                      size="small"
                    >
                      <DeleteIcon />
                    </RemoveButton>
                  </SelectedCard>
                );
              })
            )}
          </ProductsGrid>
        </div>
      </Body>
      <Footer style={{ padding: '16px 24px', borderTop: '1px solid #e0e0e0' }}>
        <FooterItem>
          <Button
            isPrimary
            onClick={() => {
              setProducts(selectedProducts);
              close();
            }}
            size="large"
            style={{ minWidth: '120px' }}
          >
            {loading ? (
              <Spinner size="small" />
            ) : (
              `Add ${selectedProducts.length} Product${
                selectedProducts.length !== 1 ? 's' : ''
              }`
            )}
          </Button>
        </FooterItem>
      </Footer>
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default CategorySearchFilter;
