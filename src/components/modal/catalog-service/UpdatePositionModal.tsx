import { useState } from 'react';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';
import constants from '../../../constants';
import { Field, Label } from '@zendeskgarden/react-forms';
import Input from '../../UI-components/Input';
import { Button } from '../../UI-components/Button';
import {
  Header,
  Modal,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { baseTheme } from '../../../themes/theme';

const UpdatePositionModal = ({
  close,
  product,
  categoryId,
  onSuccess,
}: {
  close: () => void;
  product: any;
  categoryId: number;
  onSuccess: () => void;
}) => {
  const [position, setPosition] = useState<number | string>(product.position);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const axios = useAxios();
  const addToast = useToast();

  const handlePositionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setError(null);

    if (value === '') {
      // Allow empty input for clearing
      setPosition('');
    } else {
      // Convert to number and validate
      const numValue = parseInt(value);
      if (!isNaN(numValue) && numValue > 0) {
        setPosition(numValue);
      } else {
        setPosition(value);
        setError('Please enter a positive integer');
      }
    }
  };

  const updatePosition = async () => {
    if (position === product.position) {
      addToast('warning', 'New position is same as current position');
      return;
    }
    // Validate before submitting
    if (
      position === '' ||
      (typeof position === 'string' &&
        (isNaN(parseInt(position)) || parseInt(position) <= 0))
    ) {
      setError('Please enter a valid positive integer');
      return;
    }

    // Ensure position is a valid number before API call
    const positionValue =
      position === ''
        ? 0
        : typeof position === 'string'
        ? parseInt(position)
        : position;

    setIsUpdating(true);
    try {
      await axios.patch(
        `/v1/catalog-admin/category/products-position`,
        {
          category_id: categoryId,
          product_positions_data: [
            {
              id: product.id,
              position: positionValue,
            },
          ],
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      addToast('success', 'Product position updated successfully');
      onSuccess();
      close();
    } catch (error) {
      addToast('error', (error as any)?.message || 'Failed to update position');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Modal onClose={close}>
      <Header
        style={{
          backgroundColor: baseTheme.colors.deepBlue,
          color: 'white',
          justifyContent: 'space-between',
          display: 'flex',
          alignItems: 'center',
          padding: '16px 24px',
        }}
        tag="h2"
      >
        Update Product Position
      </Header>
      <Body
        style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '0',
          maxWidth: '100%',
        }}
      >
        <div style={{ marginBottom: '20px' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '16px',
              paddingBottom: '12px',
            }}
          >
            <div
              style={{
                width: '60px',
                height: '60px',
                marginRight: '16px',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
              }}
            >
              <img
                src={
                  product.thumbnail ||
                  'https://www.dentalkart.com/media/catalog/product/no_selection'
                }
                alt={product.name}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            </div>
            <div>
              <h4 style={{ margin: '0 0 4px 0' }}>{product.name}</h4>
              <p style={{ margin: '0', fontSize: '14px' }}>
                SKU: {product.sku || 'N/A'}
              </p>
            </div>
          </div>

          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '8px 12px',
              borderRadius: '4px',
              marginBottom: '20px',
            }}
          >
            <span style={{ fontWeight: 500 }}>Current Position:</span>
            <span
              style={{
                fontWeight: 600,
                color: baseTheme.colors.deepBlue,
                backgroundColor: 'white',
                padding: '4px 12px',
                borderRadius: '16px',
              }}
            >
              {product.position}
            </span>
          </div>
        </div>

        <Field>
          <Label
            style={{
              marginBottom: '8px',
              fontWeight: 600,
            }}
          >
            New Position
          </Label>
          <Input
            type="number"
            min="1"
            validation={error ? 'error' : undefined}
            value={position}
            onChange={handlePositionChange}
            style={{
              height: '44px',
              fontSize: '16px',
              transition: 'all 0.2s ease',
              boxShadow: 'none',
            }}
          />
          {error && (
            <p
              style={{
                margin: '4px 0 0',
                fontSize: '13px',
                color: '#D93025', // Error red color
                fontWeight: 500,
              }}
            >
              {error}
            </p>
          )}
          <p
            style={{
              margin: '8px 0 0',
              fontSize: '13px',
              fontStyle: 'italic',
            }}
          >
            Enter a positive number to set the product's display order
          </p>
        </Field>
      </Body>
      <Footer>
        <FooterItem>
          <Button isBasic onClick={close}>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button isPrimary onClick={updatePosition} disabled={isUpdating}>
            {isUpdating ? 'Updating...' : 'Save Position'}
          </Button>
        </FooterItem>
      </Footer>
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default UpdatePositionModal;
