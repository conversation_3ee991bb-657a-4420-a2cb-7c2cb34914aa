import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import { <PERSON><PERSON>, <PERSON><PERSON>, Body, Close } from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { useState, useEffect } from 'react';
import { Spinner } from '@zendeskgarden/react-loaders';
import { Field, Label, Radio } from '@zendeskgarden/react-forms';
import { MenuItem } from '../../../pages/catalog-service/CategoryDropDown';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin-bottom: 20px;
`;

const RadioField = styled(Field)`
  margin: 20px;
`;

interface MediaDeleteProps {
  refetch: () => void;
  draggedItem: MenuItem | null;
  setDraggedItem: React.Dispatch<React.SetStateAction<MenuItem | null>>;
  dropItem: any | null;
  parentX: any;
  parentY: any;
  change: React.Dispatch<React.SetStateAction<boolean>>;
  visible: boolean;
  close: React.Dispatch<React.SetStateAction<boolean>>;
}

const SwapOrChangeParentModal: React.FC<MediaDeleteProps> = ({
  refetch,
  draggedItem,
  setDraggedItem,
  dropItem,
  parentX,
  parentY,
  visible,
  close,
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const [radioValue, setRadioValue] = useState('Swap Position');

  const { mutate, isLoading } = useMutation({
    mutationFn: async (updatedCategory: {
      id: any;
      parent_id: any;
      new_position: any;
    }) => {
      const response = await axios.put(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/hierarchy`,
        `/v1/catalog-admin/category/update-hierarchy/`,
        {
          category_id: updatedCategory.id,
          new_position: updatedCategory.new_position,
          new_parent_id: updatedCategory.parent_id,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return response;
    },
    onSuccess() {
      addToast('success', 'Category updated successfully');
      refetch();
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
    onSettled() {
      setDraggedItem(null);
      close(false);
    },
  });

  const { mutate: changeCategory, isLoading: isChangeCategoryLoading } =
    useMutation({
      mutationFn: async (updatedCategory: { id: any; parent_id: any }) => {
        const formData = new FormData();
        if (updatedCategory.parent_id !== null) {
          formData.append('parent_id', updatedCategory.parent_id.toString());
        }

        const response = await axios.post(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/${updatedCategory.id}`,
          `/v1/catalog-admin/category/update/${updatedCategory.id}`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              admin_identifier: `${localStorage.getItem('username')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'multipart/form-data;',
            },
          },
        );
        return response;
      },
      onSuccess() {
        addToast('success', 'Category updated successfully');
        refetch();
        close(false);
      },
      onError(err) {
        addToast('error', (err as any).message);
      },
      onSettled() {
        setDraggedItem(null);
      },
    });

  const handleYes = () => {
    if (!draggedItem || !dropItem) return;

    const mutateData = {
      id: draggedItem.id,
      parent_id:
        radioValue === 'Change Category'
          ? dropItem.id
          : parentY?.parent_category?.id,
      new_position:
        radioValue === 'Swap Position'
          ? dropItem?.position
            ? dropItem.position
            : dropItem?.positon
          : undefined,
    };

    if (radioValue === 'Swap Position') {
      mutate(mutateData);
    } else {
      changeCategory(mutateData);
    }
  };
  return (
    <div>
      {visible && (
        <Modal onClose={() => close(false)}>
          <Header
            tag="h2"
            style={{
              display: 'flex',
              color: '#FFF',
              fontSize: '20px',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              justifyContent: 'center',
            }}
          >
            Select Action
          </Header>
          <Body>
            <Row>
              <Col
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  marginLeft: '40px',
                }}
              >
                <div role="group" aria-label="Choose a plant lifecycle">
                  <RadioField>
                    <Radio
                      name="default example"
                      value="Swap Position"
                      checked={radioValue === 'Swap Position'}
                      onChange={(event) => setRadioValue(event.target.value)}
                    >
                      <Label>Primary Category Reposition</Label>
                    </Radio>
                  </RadioField>
                  <RadioField>
                    <Radio
                      name="default example"
                      value="Change Category"
                      checked={radioValue === 'Change Category'}
                      onChange={(event) => setRadioValue(event.target.value)}
                    >
                      <Label>Reassign Subcategory</Label>
                    </Radio>
                  </RadioField>
                </div>
              </Col>
            </Row>
            <Row>
              <Col style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  isAction
                  style={{ marginRight: '10px' }}
                  onClick={() => close(false)}
                >
                  Close
                </Button>
                <Button isAction isPrimary isOrange onClick={handleYes}>
                  {isLoading ? <Spinner /> : 'Apply'}
                </Button>
              </Col>
            </Row>
          </Body>
          <Close aria-label="Close modal" style={{ color: 'white' }} />
        </Modal>
      )}
    </div>
  );
};

export default SwapOrChangeParentModal;
