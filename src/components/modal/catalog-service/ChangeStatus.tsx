import React, { useCallback, useEffect, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Checkbox, Fieldset, Field, Radio } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { Label as _Label } from '../../UI-components/Label';
import { Span } from '@zendeskgarden/react-typography';
import useToast from '../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import { ProductColumns } from '../../table/product/Columns';
import { Button } from '../../UI-components/Button';
import routes from '../../../constants/routes';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import constants from '../../../constants';
import { Spinner } from '@zendeskgarden/react-loaders';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Label = styled(_Label)`
  font-weight: bold;
`;
interface ChangeStatusProps {
  ids: number[];
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const ChangeStatus: React.FC<ChangeStatusProps> = ({
  ids,
  visible,
  setVisible,
}) => {
  const [enable, setEnable] = useState<boolean | null>(null);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const addToast = useToast();
  const axios = useAxios();
  const { detectBrowser } = useProductContext();

  const { mutate, isLoading } = useMutation(
    async () => {
      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products`,
        `/v1/catalog-admin/products`,
        {
          product_ids: ids,
          newStatus: enable,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err) => {
        // console.log(err);
        addToast('error', 'Error found in Status Change.');
      },
      onSuccess(data, variables, context) {
        // console.log(data);
        addToast('success', 'Selected Product Status Changed.');
        setVisible(false);
      },
    },
  );

  const handelDone = () => {
    if (ids.length > 0) {
      mutate();
    } else {
      addToast('warning', 'Please Select Product to Change Status.');
    }
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
    // console.log("detectBrowser: ", metaData);
  }, []);

  // console.log("enable: ",enable)
  return (
    <Modal onClose={() => setVisible(false)}>
      <Header
        tag="h2"
        style={{
          display: 'flex',
          color: '#FFF',
          fontSize: '20px',
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          justifyContent: 'center',
        }}
      >
        Change Status
      </Header>
      <Body style={{ display: 'flex', justifyContent: 'center' }}>
        <Row justifyContent="center">
          <Col>
            <Fieldset>
              <Field>
                <Radio
                  name="status"
                  value="enabled"
                  checked={enable === true}
                  onChange={() => setEnable(true)}
                >
                  <Label>Enabled Selected Products</Label>
                </Radio>
              </Field>
              <Field>
                <Radio
                  name="status"
                  value="disabled"
                  checked={enable === false}
                  onChange={() => setEnable(false)}
                >
                  <Label>Disabled Selected Products</Label>
                </Radio>
              </Field>
            </Fieldset>
          </Col>
        </Row>
      </Body>
      <Footer style={{ justifyContent: 'center' }}>
        <FooterItem>
          <Button onClick={handelDone} isPrimary>
            {isLoading ? <Spinner /> : 'Done'}
          </Button>
        </FooterItem>
      </Footer>

      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default ChangeStatus;
