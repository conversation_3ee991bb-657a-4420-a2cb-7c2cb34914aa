import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { Anchor, Button } from '../../../UI-components/Button';
import {
  Header,
  Modal,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Checkbox, Field, Input, Label } from '@zendeskgarden/react-forms';
import {
  Item,
  Menu,
  Label as DropLabel,
  Field as DropField,
  Dropdown,
  Autocomplete,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import { debounce, identity } from 'lodash';
import { baseTheme } from '../../../../themes/theme';
import { DeleteIcon, DownIcon } from '../../../../utils/icons';
import styled from 'styled-components';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { ProductsResponse } from '../../../../pages/delivery/Products';
import { Product } from '../types';
import { Spinner } from '@zendeskgarden/react-loaders';
import { IconButton } from '../../../UI-components/IconButton';
import routes from '../../../../constants/routes';
import useOutsideClick from '../../../../hooks/useOutsideClick';
import { AttachmentProductsColumns } from '../../../table/product/Columns';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';
import axios from 'axios';

const SuggestionWrapper = styled.div`
  position: absolute;
  top: 100%;
  width: 70%;
  max-height: 200px;
  border-radius: 0px 0px 8px 8px;
  background: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
  overflow-y: scroll;
  z-index: 10;
`;

const ProductCard = styled.div<{ isSelected: boolean }>`
  padding: 10px;
  cursor: pointer;
  background-color: ${({ isSelected }) => (isSelected ? '#d4f1d4' : '#fff')};
  border: 2px solid ${({ isSelected }) => (isSelected ? 'green' : '#ddd')};
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  &:hover {
    background-color: ${({ isSelected }) =>
      isSelected ? '#c7eac7' : '#f5f5f5'};
    border-color: ${({ isSelected }) => (isSelected ? 'darkgreen' : '#ccc')};
  }
`;

const SelectedCard = styled.div`
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  background-color: #f9f9f9;
  border: 2px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, border-color 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #bbb;
  }

  & > *:not(:last-child) {
    margin-right: 10px;
  }

  img {
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;
interface AttachProduct {
  id: number;
  sku: string;
  type_id: string;
  catalogProductFlatRelations: {
    name: string;
    price: number;
    thumbnail: string;
    visibility: number;
  };
}

const ImageField = styled.img`
  height: 50px;
  width: 50px;
  object-fit: cover;
  border: 2px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;
const defaultPlacholder =
  'https://www.dentalkart.com/media/catalog/product/no_selection';

const ProductSearchModal = ({
  close,
  loading,
  setProducts,
}: {
  close: () => void;
  setProducts: (products: AttachmentProductsColumns[]) => any;
  loading?: boolean;
}) => {
  const [rotatedType, setRotatedType] = useState<boolean>();
  const [products, setproducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<
    AttachmentProductsColumns[]
  >([]);
  const [searchType, setsearchType] = useState<'name' | 'id' | 'sku'>('id');
  const [searchContent, setsearchContent] = useState<string>();
  // const axios = useAxios();
  const [content, setcontent] = useState<string>();
  const [showSuggestion, setShowSuggestion] = useState(false);
  const suggestionRef = useRef<HTMLDivElement>(null);
  const [priceFrom, setPriceFrom] = useState<number | undefined>(undefined);
  const [priceTo, setPriceTo] = useState<number | undefined>(undefined);
  const [searchId, setSearchId] = useState();
  const [searchName, setSearchName] = useState();
  const [searchSku, setsearchSku] = useState();
  const {
    data,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['attachment-product-list', searchType],

    queryFn: async ({ pageParam = 1 }) => {
      const params: any = {
        price_from: priceFrom,
        price_to: priceTo,
      };

      if (searchType === 'id') {
        params.id = searchContent;
      } else if (searchType === 'name') {
        params.name = searchContent;
      } else if (searchType === 'sku') {
        params.sku = searchContent;
      }
      const response =
        searchContent &&
        (await axios.get(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/products?page=${pageParam}`,
          {
            params,
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
            },
          },
        ));
      console.log('response: ', response);
      return response as any;
    },

    getNextPageParam: (lastPage, pages) => {
      if (lastPage?.data?.page_no < lastPage?.data?.pages_count)
        return lastPage?.data?.page_no + 1;
      return lastPage?.data?.page_no;
    },
  });

  useOutsideClick(suggestionRef, () => {
    setShowSuggestion(false);
  });

  useEffect(() => {
    console.log('data: ', data);
  }, [data]);

  return (
    <Modal isLarge onClose={close}>
      <Header
        style={{
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          color: 'white',
          justifyContent: 'center',
          display: 'flex',
        }}
        tag="h2"
      >
        Search
      </Header>
      <Body
        style={{
          minHeight: '200px',
          maxHeight: '400px',
        }}
      >
        <Row style={{ position: 'relative' }}>
          <Col lg={9}>
            <Input
              placeholder="Search by name,sku or product id"
              value={searchContent}
              onChange={(e) => {
                setsearchContent(e.target.value);
              }}
              onFocus={() => {
                setShowSuggestion(true);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchContent) {
                  setcontent(searchContent);
                  setShowSuggestion(true);
                  refetch();
                }
              }}
            />
          </Col>
          <Col lg={3}>
            <Dropdown
              onSelect={(item) => setsearchType(item)}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') &&
                setRotatedType(options.isOpen)
              }
            >
              <Trigger>
                <Button isPrimary>
                  {searchType ? searchType : 'Select'}
                  <Button.EndIcon isRotated={rotatedType}>
                    <DownIcon
                      style={{
                        height: baseTheme.iconSizes.md,
                        width: baseTheme.iconSizes.md,
                      }}
                    />
                  </Button.EndIcon>
                </Button>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base200,
                  transform: 'translateX(4px)',
                  borderRadius: baseTheme.borderRadii.lg,
                }}
              >
                {
                  <>
                    <Item value="name">Name</Item>
                    <Item value="sku">SKU</Item>
                    <Item value="id">Product Id</Item>
                  </>
                }
              </Menu>
            </Dropdown>
          </Col>
          {(isLoading || data) && showSuggestion && (
            <SuggestionWrapper ref={suggestionRef}>
              {data?.pages?.map((page) =>
                page?.data?.items?.map((p: any, ind: number) => {
                  return (
                    <ProductCard
                      onClick={() => {
                        if (
                          !selectedProducts.some(
                            (product) => product.id === p.id,
                          )
                        ) {
                          setSelectedProducts((prev) => [...prev, p]);
                        }
                        setProducts([]);
                        setShowSuggestion(false);
                      }}
                      isSelected={selectedProducts.some(
                        (prod) => prod.id === p.id,
                      )}
                      key={ind}
                    >
                      <Row>
                        <Col lg={2}>
                          <ImageField src={defaultPlacholder} />
                        </Col>
                        <Col style={{ display: 'flex', alignItems: 'center' }}>
                          {p?.attributes_list?.name}
                        </Col>
                      </Row>
                    </ProductCard>
                  );
                }),
              )}
              {(isFetching || isLoading) && (
                <Row justifyContent="center">
                  <Spinner />
                </Row>
              )}
              {hasNextPage && !(isFetching || isLoading) && (
                <Row justifyContent="center">
                  <Button
                    isBasic
                    onClick={() => {
                      fetchNextPage();
                    }}
                    style={{ marginBottom: `${baseTheme.paddings.base}` }}
                  >
                    Load More
                  </Button>
                </Row>
              )}
            </SuggestionWrapper>
          )}
        </Row>

        <div style={{ padding: '1rem' }}>
          {selectedProducts.map((product: any, ind: number) => {
            return (
              <SelectedCard key={ind}>
                <Col lg={2}>
                  <ImageField src={defaultPlacholder} />
                </Col>
                <Col>{product?.attributes_list?.name}</Col>
                <Col size={1}>
                  <IconButton
                    onClick={() => {
                      setSelectedProducts((prev) =>
                        prev.filter((p) => p.id !== product.id),
                      );
                    }}
                    isDanger
                  >
                    <DeleteIcon />
                  </IconButton>
                </Col>
              </SelectedCard>
            );
          })}
        </div>
      </Body>
      <Footer>
        <FooterItem>
          <Button
            isPrimary
            isOrange
            onClick={() => {
              setProducts(selectedProducts);
              close();
            }}
          >
            {loading ? <Spinner /> : 'Add Product'}
          </Button>
        </FooterItem>
      </Footer>
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default ProductSearchModal;
