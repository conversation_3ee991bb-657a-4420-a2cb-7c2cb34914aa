import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Row, Col } from '@zendeskgarden/react-grid';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import { useAttachmentContext } from '../../../../pages/catalog-service/attachment/AttachmentContext';
import constants from '../../../../constants';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import krakendPaths from '../../../../constants/krakendPaths';

const DeleteAttachmentModal = ({
  id,
  close,
}: {
  id: number | undefined;
  close: () => void;
}) => {
  const navigate = useNavigate();
  const axios = useAxios();
  const addToast = useToast();
  const { refetchAttachment, setRefetchAttachment } = useAttachmentContext();
  const { mutate, isLoading: loading } = useMutation(
    async () => {
      const response = await axios.delete(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/${id}/attachments`,
        `/v1/catalog-admin/attachments/${id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            admin_identifier: `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `Error in deletion of Attachment`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Deleted the Attachment.`);
        setRefetchAttachment(!refetchAttachment);
        close();
        navigate(`${pageRoutes['GO_TO_CATALOGUE_ATTACHMENT']}/`);
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2" isDanger>
        Remove Attachment
      </Header>
      <Body>Are you sure you want to delete this attachment ?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={() => {
              mutate();
            }}
          >
            {loading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteAttachmentModal;
