import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const ChangeAttachStatusModal = ({
  ids,
  status,
  close,
}: {
  ids: number[];
  status: boolean | undefined;
  close: () => void;
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { mutate, isLoading: loading } = useMutation(
    async () => {
      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/attachments/bulk-update`,
        `/v1/catalog-admin/attachments/bulk-update`,
        { ids: ids, status: status },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Changed the status.`);
        close();
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2">Change Status</Header>
      <Body>Are you sure you want to change status of attachments ?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            onClick={() => {
              mutate();
            }}
          >
            {loading ? <Spinner /> : 'Change'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default ChangeAttachStatusModal;
