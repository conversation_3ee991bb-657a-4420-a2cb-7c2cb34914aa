import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import { useTagContext } from '../../../../pages/catalog-service/Tags/TagsContext';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const DeleteBulkTagProductModal = ({
  prodId,
  ids,
  close,
}: {
  prodId: number | undefined;
  ids: number[];
  close: () => void;
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { refetchTagProduct, setRefetchTagProduct } = useTagContext();
  const { mutate, isLoading: loading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
        `/v1/catalog-admin/tags/${prodId}/products`,
        {
          // addProductIds: [],
          removeProductIds: ids,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Deleted.`);
        setRefetchTagProduct(!refetchTagProduct);
        close();
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2" isDanger>
        Remove Tag Products
      </Header>
      <Body>Are you sure you want to delete all selected Tag Products?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={() => {
              if (ids.length === 0) {
                addToast('warning', 'Please select products to remove');
              } else {
                mutate();
              }
            }}
          >
            {loading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteBulkTagProductModal;
