import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import { useTagContext } from '../../../../pages/catalog-service/Tags/TagsContext';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const DeleteTagModal = ({
  id,
  close,
}: {
  id: number | undefined;
  close: () => void;
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { refetchTag, setRefetchTag } = useTagContext();
  const { mutate, isLoading: loading } = useMutation(
    async () => {
      const response = await axios.delete(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${id}`,
        `/v1/catalog-admin/tags/${id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            admin_identifier: `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        console.log(err);
        addToast('error', `Error in deletion of Tag`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Deleted the Tag.`);
        setRefetchTag(!refetchTag);
        close();
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2" isDanger>
        Remove Tag
      </Header>
      <Body>Are you sure you want to delete this tag ?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={() => {
              mutate();
            }}
          >
            {loading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteTagModal;
