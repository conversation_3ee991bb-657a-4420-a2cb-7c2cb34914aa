import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const DeleteBulkTagModal = ({
  ids,
  close,
}: {
  ids: number[];
  close: () => void;
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { mutate, isLoading: loading } = useMutation(
    async () => {
      const response = await axios.delete(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/attachments/bulk-delete`,
        `/v1/catalog-admin/tags/bulk-delete`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
          data: {
            ids: ids,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        console.log(err);
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Deleted.`);
        close();
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2" isDanger>
        Remove Tags
      </Header>
      <Body>Are you sure you want to delete all selected tags ?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={() => {
              mutate();
            }}
          >
            {loading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteBulkTagModal;
