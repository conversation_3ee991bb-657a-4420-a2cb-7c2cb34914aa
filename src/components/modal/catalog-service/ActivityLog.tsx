import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';

import {
  Body as TableBody,
  Cell,
  GroupRow,
  Head,
  HeaderCell as _HeaderCell,
  HeaderRow,
  Row as TRow,
  Table,
} from '@zendeskgarden/react-tables';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import Toast from '../../UI-components/Toast';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin-bottom: 20px;
`;

const HCol = styled(Col)`
  font-weight: bold;
`;

const HeaderCell = styled(_HeaderCell)`
  background-color: ${baseTheme.colors.deepBlue};
  color: white;
`;

interface Change {
  old_value: any;
  new_value: any;
}

interface Changes {
  [key: string]: Change | { [subKey: string]: Change };
}

type TableRowProps = {
  field: string;
  subField?: string;
  oldValue: string;
  newValue: string;
};

interface ActivityLogProps {
  identifier: string;
  revertId: number;
  type: string;
  changes: Changes;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const hasChanges = (changes: Changes): boolean => {
  for (const change of Object.values(changes)) {
    if (
      typeof change === 'object' &&
      change !== null &&
      !Array.isArray(change)
    ) {
      if ('old_value' in change || 'new_value' in change) {
        const oldValue =
          change.old_value !== null ? JSON.stringify(change.old_value) : '-';
        const newValue =
          change.new_value !== null ? JSON.stringify(change.new_value) : '-';
        if (oldValue !== '-' || newValue !== '-') {
          return true; // Changes are found
        }
      } else if (hasChanges(change)) {
        return true; // Recursive check for nested objects
      }
    }
  }
  return false; // No changes
};

const ActivityLog: React.FC<ActivityLogProps> = ({
  identifier,
  revertId,
  type,
  changes,
  visible,
  setVisible,
}) => {
  const { detectBrowser, refetchLog, setRefetchLog, setIsRevert } =
    useProductContext();

  const addToast = useToast();

  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });

  const { mutate, isLoading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/activity-logs/${revertId}/revert`,
        `/v1/catalog-admin/products/activity-log/${revertId}/revert`,
        {},
        {
          headers: {
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (error: any) => {
        addToast('error', `${error?.response?.data?.message}`);
      },
      onSuccess(data, variables, context) {
        addToast('success', 'Revert is Successful.');
        setIsRevert(true);
        setRefetchLog(!refetchLog);
      },
    },
  );

  const handleRevert = () => {
    mutate();
    setVisible(false);
  };

  const renderChanges = (changes: Changes, parentKey = ''): JSX.Element[] => {
    const rows: JSX.Element[] = [];

    for (const [key, change] of Object.entries(changes)) {
      const fullKey = parentKey ? `${parentKey}(${key})` : key;

      if (
        typeof change === 'object' &&
        change !== null &&
        !Array.isArray(change) &&
        ('old_value' in change || 'new_value' in change)
      ) {
        const oldValue =
          change.old_value !== null ? JSON.stringify(change.old_value) : '-';
        const newValue =
          change.new_value !== null ? JSON.stringify(change.new_value) : '-';

        if (oldValue !== '-' || newValue !== '-') {
          rows.push(
            <TRow key={fullKey}>
              <Cell style={{ wordWrap: 'break-word', whiteSpace: 'pre-wrap' }}>
                {fullKey}
              </Cell>
              <Cell style={{ wordWrap: 'break-word', whiteSpace: 'pre-wrap' }}>
                {oldValue}
              </Cell>
              <Cell style={{ wordWrap: 'break-word', whiteSpace: 'pre-wrap' }}>
                {newValue}
              </Cell>
            </TRow>,
          );
        }
      } else if (
        typeof change === 'object' &&
        change !== null &&
        !Array.isArray(change)
      ) {
        rows.push(...renderChanges(change, fullKey));
      }
    }

    if (rows.length === 0) {
      return [
        <TRow key="nothing-changed">
          <Cell colSpan={3} style={{ textAlign: 'center', padding: '20px' }}>
            Nothing has changed
          </Cell>
        </TRow>,
      ];
    }

    return rows;
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
    // console.log('detectBrowser: ', metaData);
  }, [detectBrowser]);
  return (
    <Row>
      <Col textAlign="center">
        {visible && (
          <Modal
            style={{ borderRadius: '12px' }}
            isLarge
            onClose={() => setVisible(false)}
          >
            <Header
              tag="h2"
              style={{
                display: 'flex',
                color: '#FFF',
                fontSize: '20px',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                justifyContent: 'center',
              }}
            >
              Activity Log
            </Header>
            <Body>
              <Row>
                <HCol size={2}>User agent -</HCol>
                <HCol size={4}>
                  <div>browser name : {userData.browser}</div>
                  <div>Device: {userData.device}</div>
                </HCol>
                <HCol>
                  <div>version : 124.0.0</div>
                  <div>platform : {userData.os}</div>
                </HCol>
              </Row>
              <Row>
                <>
                  <Table style={{ width: '100%' }}>
                    <Head>
                      <HeaderRow>
                        <HeaderCell>Field Name</HeaderCell>
                        <HeaderCell>Old Value</HeaderCell>
                        <HeaderCell>New Value</HeaderCell>
                      </HeaderRow>
                    </Head>
                    <TableBody>{renderChanges(changes)}</TableBody>
                  </Table>
                </>
              </Row>
            </Body>
            <Footer>
              <FooterItem style={{ gap: '10px' }}>
                <Button
                  disabled={
                    [
                      'product_media',
                      'create',
                      'bulk_sku_update',
                      'bulk_attributes_import',
                      'viniculum_inventory_update',
                    ].includes(type) || !hasChanges(changes)
                  }
                  onClick={handleRevert}
                  isPrimary
                  isOrange
                >
                  {isLoading ? <Spinner /> : 'Revert'}
                </Button>
              </FooterItem>
            </Footer>
            <Close aria-label="Close modal" style={{ color: 'white' }} />
          </Modal>
        )}
      </Col>
    </Row>
  );
};

export default ActivityLog;
