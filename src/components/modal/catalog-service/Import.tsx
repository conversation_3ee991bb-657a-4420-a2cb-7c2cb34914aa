import React, { useEffect, useRef, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { Field, Input, Label, Radio } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import routes from '../../../constants/routes';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Import = ({
  visible,
  setVisible,
}: {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const addToast = useToast();
  const axios = useAxios();
  const { detectBrowser } = useProductContext();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { mutate, isLoading } = useMutation({
    mutationFn: async (formData: FormData) => {
      if (!selectedFile) {
        throw new Error('No file selected.');
      }

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/attributes/bulk-update`,
        `/v1/catalog-admin/bulk-update-attributes`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );

      return response.data;
    },
    onSuccess() {
      addToast('success', 'Product Uploaded successfully');
      setVisible(false);
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
  });

  const handleUpload = async () => {
    if (!selectedFile) {
      addToast('info', 'No File Selected!');
      return;
    }
    if (selectedFile.type !== 'text/csv') {
      addToast(
        'error',
        'Invalid file format. only csv format file is uploadable.',
      );
      return;
    }

    // Validate CSV columns
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvContent = e.target?.result as string;
        const lines = csvContent.split('\n');
        if (lines.length > 0) {
          const uploadedHeaders = lines[0].trim().split(',');

          // Get expected headers from sampleData
          const sampleFirstLine = sampleData.split('\n')[0];
          const expectedHeaders = sampleFirstLine.trim().split(',');

          // Check if columns match
          const missingColumns = expectedHeaders.filter(
            (header) => !uploadedHeaders.includes(header),
          );
          const extraColumns = uploadedHeaders.filter(
            (header) => !expectedHeaders.includes(header),
          );

          if (missingColumns.length > 0 || extraColumns.length > 0) {
            let errorMessage = 'CSV column mismatch. ';
            if (missingColumns.length > 0) {
              errorMessage += `Missing columns: ${missingColumns.join(', ')}. `;
            }
            if (extraColumns.length > 0) {
              errorMessage += `Unexpected columns: ${extraColumns.join(', ')}.`;
            }
            addToast('error', errorMessage);
            return;
          }

          // If validation passes, proceed with upload
          const formData = new FormData();
          formData.append('file', selectedFile);
          await mutate(formData);
        } else {
          addToast('error', 'Empty CSV file');
        }
      } catch (error) {
        console.error('CSV validation error:', error);
        addToast('error', 'Error validating CSV format');
      }
    };

    reader.onerror = () => {
      addToast('error', 'Error reading the CSV file');
    };

    reader.readAsText(selectedFile);
  };

  // Define the sample data outside the function to make it accessible for validation
  const sampleData = `id,name,type_id,price,category_associated,weight,short_description,description,features,key_specifications,packaging,htext,warranty,other_info,special_price,visibility,manufacturer,msrp,features,tax_class_id,hsn_code,is_cod,qty,is_in_stock,max_sale_qty,min_sale_qty,backorders,status,pd_expiry_date,international_active,dispatch_days
    51739,qwert,simple,1000,"125,112,59",500,random short description,<p>random description</p>,<p>random features text</p>,<p>random key specification</p>,<p>random packaging</p>,<p>random htext</p>,<p>random warranty</p>,<p>random other info</p>,800,4,16,1000,random features text,10,abcd101,true,999,true,100,1,true,TRUE,2025-06-23,true,1284
    `;

  const handleDownload = () => {
    const blob = new Blob([sampleData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'sample.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
    // console.log("detectBrowser: ", metaData);
  }, []);

  return (
    <Row>
      <Col textAlign="center">
        {visible && (
          <Modal
            style={{ borderRadius: '15px' }}
            onClose={() => setVisible(false)}
          >
            <Header
              tag="h2"
              style={{
                display: 'flex',
                color: '#FFF',
                fontSize: '20px',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                justifyContent: 'center',
              }}
            >
              Import
            </Header>
            <Body>
              <Row>
                <Col>
                  <Label>Upload File</Label>
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) =>
                        setSelectedFile(
                          e.target.files ? e.target.files[0] : null,
                        )
                      }
                    />
                    <Button onClick={() => handleUpload()} isPrimary>
                      {isLoading ? <Spinner /> : 'Upload'}
                    </Button>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col style={{ display: 'flex', justifyContent: 'center' }}>
                  <Button onClick={handleDownload} isPrimary>
                    Download Sample File
                  </Button>
                </Col>
              </Row>
            </Body>
            <Close style={{ color: 'white' }} aria-label="Close modal" />
          </Modal>
        )}
      </Col>
    </Row>
  );
};

export default Import;
