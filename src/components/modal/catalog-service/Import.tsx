import React, { useEffect, useRef, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { Field, Input, Label, Radio } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import routes from '../../../constants/routes';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Import = ({
  visible,
  setVisible,
}: {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const addToast = useToast();
  const axios = useAxios();
  const { detectBrowser } = useProductContext();

  const [selectedFileCreate, setSelectedFileCreate] = useState<File | null>(
    null,
  );
  const [selectedFileUpdate, setSelectedFileUpdate] = useState<File | null>(
    null,
  );
  const { mutate: mutateCreate, isLoading: isLoadingCreate } = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await axios.post(
        `/v1/catalog-admin/bulk-update-attributes`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    onSuccess() {
      addToast('success', 'Products created successfully');
      setVisible(false);
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
  });

  const { mutate: mutateUpdate, isLoading: isLoadingUpdate } = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await axios.post(
        `/v1/catalog-admin/bulk-update-attributes`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    onSuccess() {
      addToast('success', 'Products updated successfully');
      setVisible(false);
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
  });

  // Validation function for CSV headers
  const validateCSVHeaders = (
    csvContent: string,
    requiredColumns: string[],
  ): { isValid: boolean; errorMessage?: string } => {
    const lines = csvContent.split('\n');
    if (lines.length === 0) {
      return { isValid: false, errorMessage: 'Empty CSV file' };
    }

    const uploadedHeaders = lines[0]
      .trim()
      .split(',')
      .map((header) => header.trim().toLowerCase());
    const requiredHeadersLower = requiredColumns.map((col) =>
      col.toLowerCase(),
    );

    const missingColumns = requiredHeadersLower.filter(
      (header) => !uploadedHeaders.includes(header),
    );

    if (missingColumns.length > 0) {
      return {
        isValid: false,
        errorMessage: `Missing required columns: ${missingColumns.join(', ')}`,
      };
    }

    return { isValid: true };
  };

  // Handle upload for creating new products
  const handleUploadCreate = async () => {
    if (!selectedFileCreate) {
      addToast('info', 'No File Selected for Create!');
      return;
    }
    if (selectedFileCreate.type !== 'text/csv') {
      addToast(
        'error',
        'Invalid file format. Only CSV format file is uploadable.',
      );
      return;
    }

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvContent = e.target?.result as string;

        // Validate required columns for creating products
        const requiredColumns = [
          'name',
          'price',
          'type_id',
          'visibility',
          'status',
          'manufacturer',
        ];
        const validation = validateCSVHeaders(csvContent, requiredColumns);

        if (!validation.isValid) {
          addToast('error', validation.errorMessage!);
          return;
        }

        // If validation passes, proceed with upload
        const formData = new FormData();
        formData.append('file', selectedFileCreate);
        await mutateCreate(formData);
      } catch (error) {
        console.error('CSV validation error:', error);
        addToast('error', 'Error validating CSV format');
      }
    };

    reader.onerror = () => {
      addToast('error', 'Error reading the CSV file');
    };

    reader.readAsText(selectedFileCreate);
  };

  // Handle upload for updating existing products
  const handleUploadUpdate = async () => {
    if (!selectedFileUpdate) {
      addToast('info', 'No File Selected for Update!');
      return;
    }
    if (selectedFileUpdate.type !== 'text/csv') {
      addToast(
        'error',
        'Invalid file format. Only CSV format file is uploadable.',
      );
      return;
    }

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvContent = e.target?.result as string;

        // Validate required columns for updating products
        const requiredColumns = ['id'];
        const validation = validateCSVHeaders(csvContent, requiredColumns);

        if (!validation.isValid) {
          addToast('error', validation.errorMessage!);
          return;
        }

        // If validation passes, proceed with upload
        const formData = new FormData();
        formData.append('file', selectedFileUpdate);
        await mutateUpdate(formData);
      } catch (error) {
        console.error('CSV validation error:', error);
        addToast('error', 'Error validating CSV format');
      }
    };

    reader.onerror = () => {
      addToast('error', 'Error reading the CSV file');
    };

    reader.readAsText(selectedFileUpdate);
  };

  // Sample data for creating new products (without id)
  const sampleDataCreate = `name,type_id,status,price,special_price,visibility,manufacturer,category_associated,weight,short_description,description,features,key_specifications,packaging,htext,warranty,other_info,msrp,tax_class_id,hsn_code,is_cod,qty,is_in_stock,max_sale_qty,min_sale_qty,backorders,pd_expiry_date,international_active,dispatch_days,tier_prices,product_links
Simple Product,simple,TRUE,1000,800,4,16,"125,112,59",500,random short description,<p>random description</p>,<p>random features text</p>,<p>random key specification</p>,<p>random packaging</p>,<p>random htext</p>,<p>random warranty</p>,<p>random other info</p>,1000,10,abcd101,true,999,true,100,1,true,2025-06-23,true,1284,"10:950:General:Fixed|20:900:General:Fixed|50:850:General:Fixed"
Grouped Product,grouped,TRUE,0,0,4,16,"125,112,59",0,grouped product description,<p>grouped description</p>,<p>grouped features</p>,<p>grouped specifications</p>,<p>grouped packaging</p>,<p>grouped htext</p>,<p>grouped warranty</p>,<p>grouped other info</p>,0,10,abcd102,true,0,true,0,0,true,2025-06-23,true,1284,,"1005003:1,1005002:2,1005001:3,1005004:4,1005005:5"`;

  // Sample data for updating existing products (with id)
  const sampleDataUpdate = `id,name,type_id,status,price,special_price,visibility,manufacturer,category_associated,weight,short_description,description,features,key_specifications,packaging,htext,warranty,other_info,msrp,tax_class_id,hsn_code,is_cod,qty,is_in_stock,max_sale_qty,min_sale_qty,backorders,pd_expiry_date,international_active,dispatch_days,tier_prices,product_links
51739,Updated Simple Product,simple,TRUE,1200,900,4,16,"125,112,59",500,updated short description,<p>updated description</p>,<p>updated features text</p>,<p>updated key specification</p>,<p>updated packaging</p>,<p>updated htext</p>,<p>updated warranty</p>,<p>updated other info</p>,1200,10,abcd101,true,999,true,100,1,true,2025-06-23,true,1284,"15:1150:General:Fixed|30:1100:General:Fixed|100:1050:General:Fixed"
51740,Updated Grouped Product,grouped,TRUE,0,0,4,16,"125,112,59",0,updated grouped description,<p>updated grouped description</p>,<p>updated grouped features</p>,<p>updated grouped specifications</p>,<p>updated grouped packaging</p>,<p>updated grouped htext</p>,<p>updated grouped warranty</p>,<p>updated grouped other info</p>,0,10,abcd102,true,0,true,0,0,true,2025-06-23,true,1284,,"1005003:1,1005002:2,1005001:3,1005004:4,1005005:5"`;

  const handleDownloadCreate = () => {
    const blob = new Blob([sampleDataCreate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'sample-create.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const handleDownloadUpdate = () => {
    const blob = new Blob([sampleDataUpdate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'sample-update.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
    // console.log("detectBrowser: ", metaData);
  }, []);

  return (
    <Row>
      <Col textAlign="center">
        {visible && (
          <Modal
            style={{ borderRadius: '15px' }}
            onClose={() => setVisible(false)}
          >
            <Header
              tag="h2"
              style={{
                display: 'flex',
                color: '#FFF',
                fontSize: '20px',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                justifyContent: 'center',
              }}
            >
              Import
            </Header>
            <Body>
              <Row>
                <Col>
                  <Label>Upload to Create</Label>
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) =>
                        setSelectedFileCreate(
                          e.target.files ? e.target.files[0] : null,
                        )
                      }
                    />
                    <Button onClick={() => handleUploadCreate()} isPrimary>
                      {isLoadingCreate ? <Spinner /> : 'Upload'}
                    </Button>
                  </div>
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '5px',
                    }}
                  >
                    Required columns: name, price
                  </div>
                </Col>
              </Row>

              <Row>
                <Col>
                  <Label>Upload to Update</Label>
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) =>
                        setSelectedFileUpdate(
                          e.target.files ? e.target.files[0] : null,
                        )
                      }
                    />
                    <Button onClick={() => handleUploadUpdate()} isPrimary>
                      {isLoadingUpdate ? <Spinner /> : 'Upload'}
                    </Button>
                  </div>
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '5px',
                    }}
                  >
                    Required columns: id
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    gap: '10px',
                  }}
                >
                  <Button onClick={handleDownloadCreate} isPrimary>
                    Download Create Sample
                  </Button>
                  <Button onClick={handleDownloadUpdate} isPrimary>
                    Download Update Sample
                  </Button>
                </Col>
              </Row>
            </Body>
            <Close style={{ color: 'white' }} aria-label="Close modal" />
          </Modal>
        )}
      </Col>
    </Row>
  );
};

export default Import;
