import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '@zendeskgarden/react-buttons';
import { useToast } from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import constants from '../../../constants';
import { Spinner } from '@zendeskgarden/react-loaders';

interface DeleteBannerModalProps {
  close: () => void;
  bannerId: number;
  onSuccess: () => void;
  bannerTitle?: string;
}

const DeleteBannerModal: React.FC<DeleteBannerModalProps> = ({
  close,
  bannerId,
  onSuccess,
  bannerTitle,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const addToast = useToast();
  const axios = useAxios();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await axios.delete(
        `/v1/catalog-admin/category/banners/${bannerId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin_identifier': `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      
      addToast('success', 'Banner deleted successfully');
      onSuccess();
      close();
    } catch (error: any) {
      addToast(
        'error',
        error?.message || 'Failed to delete banner. Please try again.',
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Modal onClose={close}>
      <Header>Delete Banner</Header>
      <Body>
        <div style={{ padding: '16px 0' }}>
          <p style={{ margin: '0 0 16px 0', fontSize: '14px' }}>
            Are you sure you want to delete this banner?
          </p>
          {bannerTitle && (
            <p style={{ 
              margin: '0 0 16px 0', 
              fontSize: '13px', 
              fontStyle: 'italic',
              color: '#666'
            }}>
              Banner: {bannerTitle}
            </p>
          )}
          <p style={{ 
            margin: '0', 
            fontSize: '13px', 
            color: '#d93f0b',
            fontWeight: '500'
          }}>
            This action cannot be undone.
          </p>
        </div>
      </Body>
      <Footer>
        <FooterItem>
          <Button isBasic onClick={close} disabled={isDeleting}>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button 
            isDanger 
            onClick={handleDelete} 
            disabled={isDeleting}
          >
            {isDeleting ? <Spinner size="12px" /> : 'Delete Banner'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteBannerModal;
