import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>er,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '@zendeskgarden/react-buttons';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import constants from '../../../constants';
import { Spinner } from '@zendeskgarden/react-loaders';

interface CategoryBanner {
  id: string;
  title: string;
  landing_page_type: 'category' | 'product' | 'external' | 'sale';
  landing_page_url: string;
  web_url: string;
  mobile_url: string;
  tab_url: string;
  icon_url: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface DeleteBannerModalProps {
  close: () => void;
  banner: CategoryBanner;
  onSuccess: () => void;
  refetchBanner: () => void;  
}

const DeleteBannerModal: React.FC<DeleteBannerModalProps> = ({
  close,
  banner,
  onSuccess,
  refetchBanner,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const addToast = useToast();
  const axios = useAxios();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await axios.delete(`/v1/catalog-admin/category/banners/${banner.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          'admin-identifier': `${localStorage.getItem('username')}`,
          'x-api-key': `${constants.CATALOG_KEY}`,
        },
      });

      addToast('success', 'Banner deleted successfully');
      onSuccess();
      refetchBanner();
      close();
    } catch (error: any) {
      addToast(
        'error',
        error?.message || 'Failed to delete banner. Please try again.',
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Modal onClose={close}>
      <Header>Delete Banner</Header>
      <Body>
        <div style={{ padding: '16px 0' }}>
          <p style={{ margin: '0 0 16px 0', fontSize: '14px' }}>
            Are you sure you want to delete this banner?
          </p>
          <div
            style={{
              background: '#f8f9fa',
              padding: '12px',
              borderRadius: '4px',
              margin: '0 0 16px 0',
            }}
          >
            <p
              style={{
                margin: '0 0 8px 0',
                fontSize: '13px',
                fontWeight: '600',
                color: '#333',
              }}
            >
              Banner Details:
            </p>
            <p
              style={{
                margin: '0 0 4px 0',
                fontSize: '13px',
                color: '#666',
              }}
            >
              <strong>Title:</strong> {banner.title || 'Untitled Banner'}
            </p>
            <p
              style={{
                margin: '0 0 4px 0',
                fontSize: '13px',
                color: '#666',
              }}
            >
              <strong>Type:</strong> {banner.landing_page_type}
            </p>
            <p
              style={{
                margin: '0',
                fontSize: '13px',
                color: '#666',
              }}
            >
              <strong>Status:</strong>{' '}
              {banner.is_active ? 'Active' : 'Inactive'}
            </p>
          </div>
          <p
            style={{
              margin: '0',
              fontSize: '13px',
              color: '#d93f0b',
              fontWeight: '500',
            }}
          >
            This action cannot be undone.
          </p>
        </div>
      </Body>
      <Footer>
        <FooterItem>
          <Button isBasic onClick={close} disabled={isDeleting}>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button isDanger onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? (
              <>
                <Spinner size="12px" style={{ marginRight: '8px' }} />
                Deleting...
              </>
            ) : (
              'Delete Banner'
            )}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteBannerModal;
