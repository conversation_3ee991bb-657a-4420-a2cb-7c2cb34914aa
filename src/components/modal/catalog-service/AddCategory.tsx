import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { Button } from '../../UI-components/Button';
import {
  Header,
  Modal,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Field, Input, Label } from '@zendeskgarden/react-forms';
import {
  Item,
  Menu,
  Label as DropLabel,
  Field as DropField,
  Dropdown,
  Autocomplete,
  Select,
  Separator,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { Spinner } from '@zendeskgarden/react-loaders';
import routes from '../../../constants/routes';
import { MenuItem } from '../../../pages/catalog-service/CategoryDropDown';
import { SearchIcon } from '../../../utils/icons';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const capitalizeFirstLetter = (item: string) => {
  return item.charAt(0).toUpperCase() + item.slice(1);
};

const isValidString = (str: string) => {
  const regex = /^[a-zA-Z0-9\s()]+$/;
  return str.trim().length > 0 && regex.test(str);
};

const DENTAL_BRANDS_ID = 1971;

const AddCategory = ({
  close,
  parentCategories,
  refetch,
}: {
  close: () => void;
  parentCategories: { name: string; id: number }[];
  refetch: () => void;
}) => {
  const [selectedCategory, setSelectedCategory] = useState<{
    name: string;
    id: number;
  }>();
  const [categoryData, setCategoryData] = useState<
    { name: string; id: number }[]
  >([]);
  const wordLimit = 40;
  const [categoryName, setcategoryName] = useState<string>('');
  const [nameError, setNameError] = useState<string>('');
  const axios = useAxios();
  const addToast = useToast();
  const dropdownRef = useRef<HTMLUListElement>(null);
  const [categName, setCategName] = useState('');

  const { data: treeData, refetch: refetchCategoryData } = useQuery({
    queryKey: ['add-category-tree-data'],
    queryFn: async () => {
      const res = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/tree`,
        `/v1/catalog-admin/category/category-tree/`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return res?.data?.categoryList as MenuItem[];
    },
  });

  const handleSave = () => {
    if (!nameError && isValidString(categoryName)) {
      if (selectedCategory?.id === DENTAL_BRANDS_ID) {
        return addToast(
          'info',
          'Unable to create brand, use Brand management section.',
        );
      } else {
        mutate();
      }
    } else {
      addToast('warning', 'Category name is invalid or empty.');
    }
  };

  const findCategoryPath = (
    categories: MenuItem[] | undefined,
    categoryId: number,
    currentPath = '',
  ) => {
    if (categories)
      for (const category of categories) {
        const newPath = currentPath
          ? `${currentPath}/${category.name}`
          : category.name;

        if (category.id === categoryId) {
          return newPath as string;
        }

        if (category.children) {
          const foundPath: string = findCategoryPath(
            category.children,
            categoryId,
            newPath,
          );
          if (foundPath) {
            return foundPath as string;
          }
        }
      }
    return '';
  };

  const fetchCatagoryList = async ({ pageParam = 1, search = '' }) => {
    try {
      const response = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories?page_no=${pageParam}&category_name=${search}`,
        `/v1/catalog-admin/category?page_no=${pageParam}&category_name=${search}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      // console.log('API response:', response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const {
    data,
    isLoading: isLoadCategory,
    isFetching,
    fetchNextPage,
    hasNextPage,
    error,
    refetch: refetchcatlist,
  } = useInfiniteQuery({
    queryKey: ['category-list-add-new', categName],
    queryFn: ({ pageParam = 1 }) =>
      fetchCatagoryList({ pageParam, search: categName }),
    getNextPageParam: (lastPage: any) => {
      const currentPage = lastPage.page_no;
      const totalPages = Math.ceil(lastPage.item_count / lastPage.page_size);
      if (currentPage < totalPages) {
        return currentPage + 1;
      } else {
        return undefined;
      }
    },
  });
  const { mutate, isLoading } = useMutation({
    mutationFn: async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories`,
        `/v1/catalog-admin/category`,
        {
          name: categoryName,
          parent_id: selectedCategory?.id ? selectedCategory.id : 2,
          status: true,
        },

        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response as any;
    },
    onSuccess() {
      addToast('success', 'Category added successfully');
      refetch();
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
    onSettled(data, error, variables, context) {
      close();
    },
  });
  useEffect(() => {
    if (data) {
      const value = data.pages.flatMap((page: any) =>
        page.items.map((d: MenuItem) => ({ name: d.name, id: d.id })),
      );
      setCategoryData(value);
    }
  }, [data]);

  return (
    <Modal
      isLarge
      style={{ height: `${baseTheme.components.dimension.height.base * 70}px` }}
      onClose={close}
    >
      <Header
        style={{
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          color: 'white',
          justifyContent: 'center',
          display: 'flex',
        }}
        tag="h2"
      >
        Add Category
      </Header>
      <Body>
        <Row>
          <Col>
            <Field>
              <Label>Name</Label>
              <Input
                value={categoryName}
                onChange={(e) => {
                  const newValue = e.target.value;
                  const onlyNumbersRegex = /^\d+$/;
                  if (onlyNumbersRegex.test(newValue)) {
                    setNameError('Category Name should not be an integer!');
                  } else if (!isValidString(newValue)) {
                    setNameError('Category Name contains invalid characters!');
                  } else {
                    setNameError('');
                  }
                  if (newValue.length <= wordLimit) {
                    setcategoryName(capitalizeFirstLetter(newValue));
                  }
                }}
                placeholder="Category Name"
              />
            </Field>
            {nameError && <div style={{ color: 'red' }}>{nameError}</div>}
          </Col>
        </Row>
        <Row>
          <Col style={{ marginBottom: '0px', marginTop: '10px' }}>
            <Dropdown
              inputValue={categName}
              selectedItem={selectedCategory}
              onSelect={(i) => {
                setSelectedCategory(i);
              }}
              onInputValueChange={(value) => setCategName(value)}
              downshiftProps={{
                itemToString: (item: { name: string; id: number }) => {
                  return item;
                },
              }}
            >
              <DropField>
                <Label>Parent Category</Label>
                <Autocomplete start={<SearchIcon />}>
                  {selectedCategory?.name}
                </Autocomplete>
              </DropField>
              <Menu style={{ maxHeight: '300px' }}>
                {categoryData.length ? (
                  categoryData.map((option, index) => {
                    const categoryPath = findCategoryPath(treeData, option.id);
                    return (
                      <Item key={index} value={option}>
                        <Row>
                          <Col>{`${option.name} (ID: ${option.id})`}</Col>
                          <Col size={7}>
                            <span
                              style={{ fontSize: '12px' }}
                            >{`${categoryPath}`}</span>
                          </Col>
                        </Row>
                      </Item>
                    );
                  })
                ) : (
                  <Item disabled>No matches found</Item>
                )}
                {(isFetching || isLoading) && (
                  <Row justifyContent="center">
                    <Spinner />
                  </Row>
                )}
                {hasNextPage && categoryData.length > 0 && !isLoading && (
                  <Row justifyContent="center">
                    <Button
                      isBasic
                      onClick={() => {
                        fetchNextPage();
                      }}
                      style={{ marginBottom: `${baseTheme.paddings.base}` }}
                    >
                      Load More
                    </Button>
                  </Row>
                )}
              </Menu>
            </Dropdown>
          </Col>
        </Row>
      </Body>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          padding: '5px 10px',
        }}
      >
        <Button isPrimary isOrange onClick={handleSave}>
          {isLoading ? <Spinner /> : 'Save'}
        </Button>
      </div>
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default AddCategory;
