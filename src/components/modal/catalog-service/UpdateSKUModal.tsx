import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { Checkbox, Field, Input, Label } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { debounce } from 'lodash';
import styled from 'styled-components';
import { useMutation } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin-bottom: 20px;
`;

interface UpdateSKUProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const UpdateSKUModal: React.FC<UpdateSKUProps> = ({ visible, setVisible }) => {
  const [skuData, setSkuData] = useState<string[]>();
  const [skuValue, setSkuValue] = useState('');
  const axios = useAxios();
  const addToast = useToast();

  const { mutate, isLoading } = useMutation(
    async () => {
      const payload = {
        sku_list: skuData,
      };
      const response = await axios.post(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/products/skus/bulk-update`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err?.response?.data?.message}`);
      },
      onSuccess: () => {
        addToast('success', `Successfully Updated the SKU.`);
      },
    },
  );

  const handleReset = () => {
    setSkuValue('');
  };

  const handleUpdateSKU = () => {
    const trimmedSkuValue = skuValue.trim();
    if (trimmedSkuValue.length > 0) {
      const val = skuValue.split(',');
      setSkuData(val);
      mutate();
    } else {
      addToast('info', 'Please add SKU!');
    }
  };
  return (
    <Row>
      <Col textAlign="center">
        {/* <Button onClick={() => setVisible(true)}>Open modal</Button> */}
        {visible && (
          <Modal onClose={() => setVisible(false)}>
            <Header
              tag="h2"
              style={{
                display: 'flex',
                color: '#FFF',
                fontSize: '20px',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                justifyContent: 'center',
              }}
            >
              Update SKU
            </Header>
            <Body>
              <Row>
                <Col>
                  <Field>
                    <Label>
                      SKU(s)<span style={{ color: 'red' }}>*</span>
                    </Label>
                    <Input
                      value={skuValue}
                      onChange={(e) => {
                        setSkuValue(e.target.value);
                      }}
                    />
                  </Field>
                  <div style={{ marginTop: '10px' }}>
                    Enter Here The SKU(s) You Want To Update Separated By
                    Commas. You Can Update Up To 500 SKUs At Once.
                  </div>
                </Col>
              </Row>
            </Body>
            <Footer>
              <FooterItem style={{ gap: '20px' }}>
                <Button onClick={handleReset}>Reset</Button>
                <Button onClick={handleUpdateSKU} isPrimary isOrange>
                  {isLoading ? <Spinner /> : 'Update SKU'}
                </Button>
              </FooterItem>
            </Footer>
            <Close aria-label="Close modal" style={{ color: 'white' }} />
          </Modal>
        )}
      </Col>
    </Row>
  );
};

export default UpdateSKUModal;
