import { useState } from 'react';
import { FaqCategoryOutputItem } from '../../../gql/graphql';
import FaqCategoryTable from '../../table/faq/FaqCategoryTable';
import NetPromoterScoreTable from '../../table/netpromoterscore/NetPromoterScoreTable';

const NetPromoterScoreLayout = ({
  rows,
  count,
  refetch,
  filters,
  setFilters,
}: {
  rows: any;
  count: number;
  refetch: any;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <NetPromoterScoreTable
        data={rows}
        count={count}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default NetPromoterScoreLayout;
