import { useState } from 'react';
import QuestionAndAnswerTable from '../../table/questions-and-answers/QuestionAndAnswerTable';
import QuestionAndAnswerTableV2 from '../../table/questions-and-answersv2/QuestionAndAnswerTable';

import { QuestionsOutputData } from '../../../gql/graphql';
const QuestionAndAnswerLayout = ({
  rows,
  count,
  refetch,
  filters,
  setFilters,
  isVersionTwo,
  setIsQueryEnabled,
}: {
  rows: QuestionsOutputData[];
  count: number;
  refetch: any;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  isVersionTwo?: boolean;
  setIsQueryEnabled: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    filters?.product_id ||
      filters?.search ||
      filters?.product_name ||
      undefined,
  );
  // Add more objects as needed
  if (isVersionTwo) {
    return (
      <>
        <QuestionAndAnswerTableV2
          data={rows}
          count={count}
          searchContent={searchContent}
          setSearchContent={setSearchContent}
          refetch={refetch}
          filters={filters}
          setFilters={setFilters}
          setIsQueryEnabled={setIsQueryEnabled}
        />
      </>
    );
  }
  return (
    <>
      <QuestionAndAnswerTable
        data={rows}
        count={count}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default QuestionAndAnswerLayout;
