import { useState } from 'react';
import RefundPendingTable from '../../table/refund/RefundPendingTable';

const RefundPendingLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: any[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        filters?.order_id || undefined
    );
    // Add more objects as needed

    return (
        <>
            <RefundPendingTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default RefundPendingLayout;
