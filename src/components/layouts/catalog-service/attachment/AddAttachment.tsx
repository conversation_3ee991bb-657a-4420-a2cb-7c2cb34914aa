import React, { useEffect, useRef, useState } from 'react';
import ThumbnailIcon from '../../../../utils/icons/thumbnail.svg';
import { Button, Buttons } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { useNavigate, useNavigation, useParams } from 'react-router-dom';
import { DeleteIcon, LeftArrowIcon } from '../../../../utils/icons';
import { pageRoutes } from '../../../navigation/RouteConfig';
import styled from 'styled-components';
import {
  Field,
  Input,
  Label as _Label,
  Toggle,
  Fieldset,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../../themes/theme';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { Spinner } from '@zendeskgarden/react-loaders';
import { IconButton } from '../../../UI-components/IconButton';
import DeleteAttachmentModal from '../../../modal/catalog-service/Attachment/DeleteAttachmentModal';
import { useAttachmentContext } from '../../../../pages/catalog-service/attachment/AttachmentContext';
import AttachProducts from './AttachProducts';
import constants from '../../../../constants';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Dropdown, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import krakendPaths from '../../../../constants/krakendPaths';
import { useAuth } from '../../../providers/AuthProvider';

const Row = styled(_Row)`
  margin: calc(${baseTheme.paddings.base}*10) 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const DropdownItem = styled.div`
  max-height: ${baseTheme.components.dimension.width.base300};
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: ${baseTheme.paddings.md};
  padding: ${baseTheme.paddings.lg} calc(${baseTheme.paddings.sm}*3);
  cursor: pointer;
`;

const TopBar = styled.div`
  padding: ${baseTheme.paddings.md} ${baseTheme.paddings.md};
  background-color: ${baseTheme.colors.white};
  justify-content: space-between;
  display: flex;
`;
const ContentContainer = styled.div`
  margin-top: ${baseTheme.paddings.lg};
`;

const CategoryImage = styled.img`
  height: ${baseTheme.components.dimension.width.base100};
`;
const Main = styled.div`
  margin: ${baseTheme.paddings.md} ${baseTheme.paddings.md};
  background-color: ${baseTheme.colors.white};
  height: 100%;
  border-radius: ${baseTheme.paddings.sm};
`;

const TabContainer = styled.div`
  display: flex;
`;
const TabButton = styled.button<{ active: boolean }>`
  padding: ${baseTheme.paddings.md} ${baseTheme.paddings.lg};
  border-top: ${baseTheme.paddings.sm} solid
    ${(props) =>
      props.active
        ? `${baseTheme.colors.primaryHue}`
        : `${baseTheme.colors.textColorGrey}`};
  background-color: ${(props) =>
    props.active
      ? `${baseTheme.colors.white}`
      : `${baseTheme.colors.veryLightGrey}`};
  color: ${(props) =>
    props.active
      ? `${baseTheme.colors.primaryHue}`
      : `${baseTheme.colors.black}`};
  border-right: none;
  border-left: none;
  border-bottom: none;
  font-weight: ${baseTheme.customFontWeights.mediumBold};
  cursor: pointer;
`;
interface TabContentProps {
  active: boolean;
}

const TabContent = styled.div<TabContentProps>`
  display: ${(props) => (props.active ? 'block' : 'none')};
  height: 100vh;
  padding: ${baseTheme.paddings.lg} ${baseTheme.paddings.lg};
  line-height: ${baseTheme.paddings.md};
`;
interface AttachmentFilters {
  page: number;
  limit: number;
}

interface AttachmentData {
  id: number;
  status: boolean;
  active_device: string;
  customer_group: string;
  description: string;
  thumbnail: string;
  url: string;
  created_at: string;
  updated_at: string;
}

const tabs = [
  { id: 'tab1', label: 'Information' },
  { id: 'tab2', label: 'Attach products' },
];

interface CheckboxProps {
  checked: boolean;
  onChange: () => void;
  label: string;
  identifier: string;
}

const CheckboxContainer = styled.label`
  display: flex;
  align-items: center;
`;

const CheckboxInput = styled.input`
  margin-right: ${baseTheme.paddings.sm};
`;
const CheckboxLabel = styled.span<{ htmlFor: string }>``;

const CheckboxActive: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  label,
  identifier,
}) => (
  <CheckboxContainer>
    <CheckboxInput
      type="checkbox"
      checked={checked}
      onChange={onChange}
      id={identifier}
    />
    <CheckboxLabel htmlFor={identifier}>{label}</CheckboxLabel>
  </CheckboxContainer>
);

const AddAttachment = () => {
  const { id } = useParams();
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const {
    attachmentValue,
    setAttachmentValue,
    newAttachmentValue,
    setNewAttachmentValue,
  } = useAttachmentContext();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const [status, setStatus] = useState<boolean>(true);
  const [description, setDescription] = useState<string>('');
  const [url, setUrl] = useState<string>('');
  const [thumbnail, setThumbnail] = useState<string>('');
  const fileInputRef = useRef(null);
  const urlInputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [selectedfileUrl, setSelectedFileUrl] = useState<any>(null);
  const [isDelete, setIsDelete] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('tab1');
  const [filteredTabs, setFilteredTabs] = useState<
    { id: string; label: string }[]
  >([]);
  const [activeDevice, setActiveDevice] = useState<string>(
    attachmentValue?.active_device ?? '',
  );
  const [activeOn, setActiveOn] = useState<string>(
    attachmentValue?.customer_group ?? '',
  );
  const handleTabClick = (id: string) => {
    setActiveTab(id);
  };

  const [activeDeviceList, setActiveDeviceList] = useState<string[]>(
    [] as string[],
  );
  const [activeOnList, setActiveOnList] = useState<string[]>([] as string[]);

  const activedeviceboxs: { label: string; identifier: string }[] = [
    { label: 'All', identifier: 'all' },
    { label: 'Website', identifier: 'website' },
    { label: 'Phone Browser', identifier: 'phone_browser' },
    { label: 'Ipad Browser', identifier: 'ipad_browser' },
    { label: 'Phone Application', identifier: 'phone_application' },
  ];

  const activeonboxs: { label: string; identifier: string }[] = [
    { label: 'ALL GROUPS', identifier: 'ALL GROUPS' },
    { label: 'NOT LOGGED IN', identifier: 'NOT LOGGED IN' },
    { label: 'General', identifier: 'General' },
    { label: 'Dentalkart VIP', identifier: 'Dentalkart VIP' },
  ];

  const handleCheckboxActiveDeviceChange = (identifier: string) => {
    const columnIndex = activeDeviceList.findIndex(
      (v: string) => v === identifier,
    );

    if (columnIndex === -1) {
      setActiveDeviceList((prevList: string[]) => [...prevList, identifier]);
    } else {
      setActiveDeviceList((prevList: string[]) =>
        prevList.filter((item: string) => item !== identifier),
      );
    }
  };

  const handleCheckboxActiveOnChange = (identifier: string) => {
    const columnIndex = activeOnList.findIndex((v: string) => v === identifier);

    if (columnIndex === -1) {
      setActiveOnList((prevList: string[]) => [...prevList, identifier]);
    } else {
      setActiveOnList((prevList: string[]) =>
        prevList.filter((item: string) => item !== identifier),
      );
    }
  };

  const axios = useAxios();
  const addToast = useToast();

  const [userData, setUserData] = useState<AttachmentData>(
    {} as AttachmentData,
  );

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['attachment-item'],
      queryFn: async (): Promise<any> => {
        if (id) {
          return await axios.get(
            // `${krakendPaths.CATALOG_URL}/admin-api/v1/attachment-list?id=${id}`,
            `/v1/catalog-admin/attachments/list?id=${id}`,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                'x-api-key': `${constants.CATALOG_KEY}`,
                'admin-identifier': `${localStorage.getItem('username')}`,
              },
            },
          );
        } else {
          return [];
        }
      },
      enabled: !!id,
      onError: () => {
        addToast('error', 'Error found in fetching data.');
      },
      onSuccess: (data) => {
        setUserData(data?.attachments[0]);
        setAttachmentValue(data?.attachments[0]);
      },
    });

  const { mutate: addMutation, isLoading: addLoading } = useMutation(
    async () => {
      const payload = {
        description: newAttachmentValue.description,
        thumbnail: newAttachmentValue?.thumbnail,
        status: newAttachmentValue?.status || true,
        url: newAttachmentValue?.url,
        active_device: activeDeviceList || ['all'],
        customer_group: activeOnList || ['ALL GROUPS'],
      };
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/attachments`,
        `/v1/catalog-admin/attachments`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (error: {
        message: string;
        error: string;
        statusCode: number;
      }) => {
        addToast('error', error?.message);
      },
      onSuccess: () => {
        addToast('success', `Attachment Created Successfully.`);
        navigate(`${pageRoutes['GO_TO_CATALOGUE_ATTACHMENT']}/`);
        refetch();
      },
    },
  );

  const { mutate: updateAttachment, isLoading: updateLoading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}/attachments`,
        `/v1/catalog-admin/attachments/${prodId}/products`,
        {
          thumbnail: attachmentValue?.thumbnail,
          description: attachmentValue.description,
          status: attachmentValue.status,
          url: attachmentValue?.url,
          active_device: activeDeviceList,
          customer_group: activeOnList,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess() {
        addToast('success', 'Attachment Updated Successfully.');
        refetch();
        setSelectedFile(null);
      },
      onError(error: { message: string; error: string; statusCode: number }) {
        addToast('error', error?.message);
      },
    },
  );

  const { mutate: removeThumnailMutation, isLoading: removeThumbnailLoading } =
    useMutation(
      async () => {
        const response = await axios.post(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}/attachments`,
          `/v1/catalog-admin/attachments/${prodId}/products`,
          {
            thumbnail: 'none',
          },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'admin-identifier': `${localStorage.getItem('username')}`,
            },
          },
        );

        return response;
      },
      {
        onSuccess() {
          addToast('success', 'Thumbnail removed successfully.');
          refetch();
        },
        onError(error: { message: string }) {
          addToast('error', error?.message);
        },
      },
    );

  const { mutate: removeMutation, isLoading: removeLoading } = useMutation(
    async () => {
      const response = await axios.delete(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/${prodId}/attachments`,
        `/v1/catalog-admin/attachments/${prodId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (error: { message: string }) => {
        addToast('error', `${error?.message}`);
      },
      onSuccess: () => {
        addToast('success', `Attachment Removed Successfully.`);
        navigate(`${pageRoutes['GO_TO_CATALOGUE_ATTACHMENT']}/`);
        refetch();
      },
    },
  );
  const { mutate: uploadfileUrlMutation, isLoading: uploadurlLoading } =
    useMutation(
      async (selectedFile: string) => {
        const formData = new FormData();
        formData.append('file', selectedFile);
        const response = await axios.post(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/file/upload`,
          `${constants.CATALOG_URL}/return/api/v1/returns/file/upload`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'multipart/form-data',
              'admin-identifier': `${localStorage.getItem('username')}`,
            },
          },
        );
        return response;
      },
      {
        onError: () => {
          addToast('error', 'Error in Url upload.');
        },
        onSuccess: (data: any) => {
          addToast('success', 'Url uploaded.');
          setAttachmentValue((prev) => ({
            ...prev,
            url: data.file_url,
          }));
          setNewAttachmentValue((prev) => ({
            ...prev,
            url: data.file_url,
          }));
          setUrl(data.file_url);
        },
      },
    );
  const { mutate: uploadfileMutation, isLoading: uploadLoading } = useMutation(
    async (selectedFile: string) => {
      const formData = new FormData();
      formData.append('file', selectedFile);
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/file/upload`,
        `${constants.CATALOG_URL}/return/api/v1/returns/file/upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'Content-Type': 'multipart/form-data',
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response;
    },
    {
      onError: () => {
        addToast('error', 'Error in Thumbnail upload.');
      },
      onSuccess: (data: any) => {
        addToast('success', 'Thumbnail uploaded.');
        setAttachmentValue((prev) => ({
          ...prev,
          thumbnail: data.file_url,
        }));
        setNewAttachmentValue((prev) => ({
          ...prev,
          thumbnail: data.file_url,
        }));
        setThumbnail(data.file_url);
      },
    },
  );

  const handleButtonClick = () => {
    (fileInputRef?.current as any)?.click();
  };
  const handleUrlLink = () => {
    (urlInputRef?.current as any)?.click();
  };
  const handleFileChange = (e: any) => {
    const file = e.target.files[0];
    setSelectedFile(file);
  };
  const handleUrlChange = (e: any) => {
    const file = e.target.files[0];
    setSelectedFileUrl(file);
  };
  useEffect(() => {
    if (userData.status) {
      setStatus(userData.status);
    }
    if (userData.description) {
      setDescription(userData.description);
    }
    if (userData.url) {
      setUrl(userData.url);
    }
    if (userData.thumbnail) {
      setThumbnail(userData.thumbnail);
    }
    if (userData.active_device) {
      const arr: string[] = userData.active_device.split(',');
      setActiveDeviceList(arr);
    }
    if (userData.customer_group) {
      const arr: string[] = userData.customer_group.split(',');
      setActiveOnList(arr);
    }
  }, [userData]);

  useEffect(() => {
    if (selectedFile) {
      uploadfileMutation(selectedFile);
    }
  }, [selectedFile]);

  useEffect(() => {
    if (selectedfileUrl) {
      uploadfileUrlMutation(selectedfileUrl);
    }
  }, [selectedfileUrl]);

  useEffect(() => {
    if (id) {
      setProdId(Number(id));
    }
    refetch();
    if (prodId) {
      setFilteredTabs([
        { id: 'tab1', label: 'Information' },
        { id: 'tab2', label: 'Attach products' },
      ]);
    } else {
      setFilteredTabs([{ id: 'tab1', label: 'Information' }]);
    }
  }, [id, prodId]);

  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: id ? 'Edit Attachment' : 'Add Attachment',
      breadcrumbParent: '',
    });
    if (!prodId) {
      setFilteredTabs([{ id: 'tab1', label: 'Information' }]);
    }
  }, []);

  return (
    <div>
      <TopBar>
        <>
          <Col
            style={{
              display: 'flex',
              gap: `calc(${baseTheme.paddings.base}*10)`,
            }}
          >
            {id ? (
              <>
                <img src={ThumbnailIcon} alt="thumbnail" />
                <div>
                  <div>ID - {attachmentValue.id}</div>
                </div>
              </>
            ) : (
              <></>
            )}
          </Col>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <Buttons
              style={{ margin: `0px ${baseTheme.paddings.md}` }}
              onClick={() => {
                navigate(`${pageRoutes['GO_TO_CATALOGUE_ATTACHMENT']}/`);
              }}
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
            <Buttons
              style={{ margin: `0px ${baseTheme.paddings.md}` }}
              onClick={() => setIsDelete(true)}
            >
              Delete
            </Buttons>
            <Button
              onClick={() => {
                if (prodId && attachmentValue.description) {
                  updateAttachment();
                } else {
                  if (attachmentValue.description) {
                    addMutation();
                  } else {
                    addToast('warning', 'Please add the required fields');
                  }
                }
              }}
              isPrimary
            >
              {addLoading || updateLoading ? <Spinner /> : 'Save'}
            </Button>
          </Col>
        </>
      </TopBar>
      <Main>
        <TabContainer>
          {filteredTabs.map((tab: any) => (
            <TabButton
              key={tab.id}
              id={tab.id}
              active={activeTab === tab.id}
              onClick={() => handleTabClick(tab.id)}
            >
              {tab.label}
            </TabButton>
          ))}
        </TabContainer>
        <ContentContainer>
          <TabContent active={activeTab === 'tab1'}>
            <>
              <Row>
                <Col size={3}>
                  <Label>Status</Label>
                  <Field>
                    <Toggle
                      checked={prodId ? userData.status : status}
                      onChange={() => {
                        setAttachmentValue((prev) => ({
                          ...prev,
                          status: !status,
                        }));
                        setNewAttachmentValue((prev) => ({
                          ...prev,
                          status: !status,
                        }));
                        setUserData((prev) => ({
                          ...prev,
                          status: !userData.status,
                        }));
                        setStatus(!status);
                      }}
                      style={{
                        backgroundColor: `${baseTheme.colors.deepBlue}`,
                      }}
                    >
                      <Label hidden>Status</Label>
                    </Toggle>
                  </Field>
                </Col>
                <Col>
                  <Dropdown>
                    <div
                      style={{
                        color: `${baseTheme.colors.deepBlue}`,
                        fontWeight: 'bold',
                        padding: `${baseTheme.paddings.sm} 0px`,
                      }}
                    >
                      Active devices{' '}
                      <span style={{ color: `${baseTheme.colors.deepRed}` }}>
                        *
                      </span>
                    </div>
                    <Trigger>
                      <Input
                        id="styledInput"
                        type="text"
                        value={activedeviceboxs
                          .map(
                            (item: { label: string; identifier: string }) => {
                              if (activeDeviceList.includes(item.identifier)) {
                                return item.label;
                              }
                              return null;
                            },
                          )
                          .filter(Boolean)
                          .join(', ')}
                        style={{
                          padding: `${baseTheme.paddings.md}`,
                          width: `${baseTheme.components.dimension.width.base600}`,
                          border: `1px solid ${baseTheme.colors.grey}`,
                          borderRadius: `${baseTheme.borderRadii.xs}`,
                          cursor: 'pointer',
                        }}
                      />
                    </Trigger>
                    <Menu
                      style={{
                        width: `${baseTheme.components.dimension.width.base350}`,
                      }}
                    >
                      <DropdownItem>
                        {activedeviceboxs.map(
                          (checkbox: { label: string; identifier: string }) => (
                            <CheckboxActive
                              key={checkbox.identifier}
                              checked={activeDeviceList.includes(
                                checkbox?.identifier as string,
                              )}
                              onChange={() =>
                                handleCheckboxActiveDeviceChange(
                                  checkbox?.identifier,
                                )
                              }
                              label={checkbox?.label}
                              identifier={checkbox?.identifier}
                            />
                          ),
                        )}
                      </DropdownItem>
                    </Menu>
                  </Dropdown>
                </Col>
              </Row>
              <Row>
                <Col size={5}>
                  <Field>
                    <Label>
                      Description{' '}
                      <span style={{ color: `${baseTheme.colors.deepRed}` }}>
                        *
                      </span>
                    </Label>
                    <Input
                      value={attachmentValue.description}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === '') {
                          setAttachmentValue((prev) => ({
                            ...prev,
                            description: undefined,
                          }));
                          setNewAttachmentValue((prev) => ({
                            ...prev,
                            description: undefined,
                          }));
                        } else {
                          setAttachmentValue((prev) => ({
                            ...prev,
                            description: value,
                          }));
                          setNewAttachmentValue((prev) => ({
                            ...prev,
                            description: value,
                          }));
                        }
                        // setDescription(e.target.value);
                      }}
                    />
                  </Field>
                  <div
                    style={{
                      fontSize: `${baseTheme.fontSizes.sm}`,
                      marginTop: `${baseTheme.paddings.sm}`,
                    }}
                  >
                    Enter attachment sort descripton
                  </div>
                </Col>
              </Row>
              <Row>
                <Col size={3}>
                  <Label>Thumbnail</Label>
                  <Field>
                    <Input
                      type="file"
                      ref={fileInputRef}
                      style={{ display: 'none' }}
                      onChange={handleFileChange}
                    />
                    {selectedFile ? (
                      <CategoryImage
                        src={URL.createObjectURL(selectedFile)}
                        alt="attachment Img"
                      />
                    ) : attachmentValue.thumbnail &&
                      attachmentValue.thumbnail != 'none' ? (
                      <CategoryImage
                        src={attachmentValue.thumbnail}
                        alt="attachment Img"
                      />
                    ) : (
                      <></>
                    )}
                  </Field>
                  <div>
                    <Button onClick={handleButtonClick}>
                      {selectedFile ||
                      (attachmentValue.thumbnail &&
                        attachmentValue.thumbnail !== 'none')
                        ? 'Change'
                        : 'Add'}
                    </Button>
                    {(attachmentValue.thumbnail &&
                      attachmentValue.thumbnail !== 'none') ||
                    selectedFile ? (
                      <Tooltip content="Delete Category">
                        <IconButton
                          onClick={() => {
                            if (prodId) {
                              // setSelectedFile(null);
                              removeThumnailMutation();
                            } else {
                              setSelectedFile(null);
                              setAttachmentValue((prev) => ({
                                ...prev,
                                thumbnail: 'none',
                              }));
                            }
                          }}
                          style={{ marginRight: `${baseTheme.paddings.md}` }}
                          isDanger
                        >
                          {updateLoading ? <Spinner /> : <DeleteIcon />}
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <></>
                    )}
                  </div>
                  <div
                    style={{
                      fontSize: `${baseTheme.fontSizes.sm}`,
                      marginTop: `${baseTheme.paddings.sm}`,
                    }}
                  >
                    jpg, jpeg, gif, png, icon
                  </div>
                </Col>
              </Row>
              <Row>
                <Col size={2}>
                  <Field>
                    <Label>Choose File</Label>
                    <Input
                      type="file"
                      ref={urlInputRef}
                      style={{ display: 'none' }}
                      onChange={handleUrlChange}
                    />
                    <Input
                      onClick={handleUrlLink}
                      style={{ cursor: 'pointer' }}
                      value={'Choose file'}
                    />
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col size={5}>
                  <Field>
                    <Label>URL</Label>
                    <Input
                      value={attachmentValue.url}
                      onChange={(e) => {
                        setAttachmentValue((prev) => ({
                          ...prev,
                          url: e.target.value,
                        }));
                        setNewAttachmentValue((prev) => ({
                          ...prev,
                          url: e.target.value,
                        }));
                      }}
                      placeholder="Upload file or enter URL"
                    />
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col>
                  <Dropdown>
                    <div
                      style={{
                        color: `${baseTheme.colors.deepBlue}`,
                        fontWeight: 'bold',
                        padding: `${baseTheme.paddings.sm} 0px`,
                      }}
                    >
                      Active On{' '}
                      <span style={{ color: `${baseTheme.colors.deepRed}` }}>
                        *
                      </span>
                    </div>
                    <Trigger>
                      <Input
                        id="styledInput"
                        type="text"
                        value={activeonboxs
                          .map(
                            (item: { label: string; identifier: string }) => {
                              if (activeOnList.includes(item.identifier)) {
                                return item.label;
                              }
                              return null;
                            },
                          )
                          .filter(Boolean)
                          .join(', ')}
                        style={{
                          padding: `${baseTheme.paddings.md}`,
                          width: `${baseTheme.components.dimension.width.base600}`,
                          border: `1px solid ${baseTheme.colors.grey}`,
                          borderRadius: `${baseTheme.borderRadii.xs}`,
                          cursor: 'pointer',
                        }}
                      />
                    </Trigger>
                    <Menu
                      style={{
                        width: `${baseTheme.components.dimension.width.base350}`,
                      }}
                    >
                      <DropdownItem>
                        {activeonboxs.map(
                          (checkbox: { label: string; identifier: string }) => (
                            <CheckboxActive
                              key={checkbox?.identifier}
                              checked={activeOnList.includes(
                                checkbox?.identifier as string,
                              )}
                              onChange={() =>
                                handleCheckboxActiveOnChange(
                                  checkbox?.identifier,
                                )
                              }
                              label={checkbox?.label}
                              identifier={checkbox?.identifier}
                            />
                          ),
                        )}
                      </DropdownItem>
                    </Menu>
                  </Dropdown>
                </Col>
              </Row>
            </>
          </TabContent>
          <TabContent active={activeTab === 'tab2'}>
            <>
              <AttachProducts prodId={prodId} />
            </>
          </TabContent>
        </ContentContainer>
      </Main>
      {isDelete && (
        <DeleteAttachmentModal close={() => setIsDelete(false)} id={prodId} />
      )}
    </div>
  );
};

export default AddAttachment;
