import React, { useCallback, useEffect, useState, memo } from 'react';
import ThumbnailIcon from '../../../../utils/icons/thumbnail.svg';
import { Button, Buttons } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { useNavigate, useParams } from 'react-router-dom';
import { LeftArrowIcon } from '../../../../utils/icons';
import { pageRoutes } from '../../../navigation/RouteConfig';
import styled from 'styled-components';
import {
  Field,
  FileList,
  File,
  FileUpload,
  Input,
  Label,
  Message,
  Toggle,
  Textarea,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../../themes/theme';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import AttachProducts from '../attachment/AttachProducts';
import TagsProduct from './TagsProduct';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { useTagContext } from '../../../../pages/catalog-service/Tags/TagsContext';
import { useDropzone } from 'react-dropzone';
import { KEYS } from '@zendeskgarden/container-utilities';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { Progress, Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';
import { useAuth } from '../../../providers/AuthProvider';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const ErrorMessage = styled.div`
  color: red;
  font-size: 12px;
  margin-top: 5px;
`;

const TopBar = styled.div`
  padding: 10px 10px;
  background-color: #fff;
  justify-content: space-between;
  display: flex;
`;
const ContentContainer = styled.div`
  margin-top: 20px;
`;

const Main = styled.div`
  margin: 10px 10px;
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
`;

const TabContainer = styled.div`
  display: flex;
`;
const TabButton = styled.button<{ active: boolean }>`
  padding: 10px 20px;
  border-top: 5px solid ${(props) => (props.active ? '#0A5F79' : '#BCBCBC')};
  background-color: ${(props) => (props.active ? '#FFF' : '#F6F6F6')};
  color: ${(props) => (props.active ? '#0A5F79' : '#000')};
  border-right: none;
  border-left: none;
  border-bottom: none;
  font-weight: 600;
  cursor: pointer;
`;
const Box = styled.div`
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  width: 300px;
`;
const LabelItem = styled.div<{ isSelected: boolean }>`
  display: flex;
  justify-content: space-between;
  padding: 15px;
  cursor: pointer;
  background-color: ${(props) => (props.isSelected ? '#ccc' : '#fff')};
`;

const InputField = styled(Input)`
  width: 200px;
`;

const StyledFileUpload = styled(FileUpload)`
  min-height: ${(p) => p.theme.space.base * 20}px;
  font-size: 14px;
  pointer-events: ${(props) => (props.disabled ? 'none' : 'auto')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
`;

interface TabContentProps {
  active: boolean;
}

const TabContent = styled.div<TabContentProps>`
  display: ${(props) => (props.active ? 'block' : 'none')};
  height: 100vh;
  padding: 20px 20px;
  line-height: 10px;
`;
interface AttachmentFilters {
  page: number;
  limit: number;
}

interface AttachmentData {
  id: number;
  status: boolean;
  name: string;
  unique_code: string;
  value: string;
  tag_type: string;
  position: string;
  description: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

const tabs = [
  { id: 'tab1', label: 'General Information' },
  { id: 'tab2', label: 'Products in Tags' },
];

const labels = [
  { id: 1, label: 'Expected Delivery Date', value: 'Value 1' },
  { id: 2, label: 'X + Y', value: 'Value 2' },
  { id: 3, label: 'Free Delivery', value: 'Value 3' },
];

const AddTags = () => {
  const { id } = useParams();
  const [prodId, setProdId] = useState<any>();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const [status, setStatus] = useState(false);
  const [description, setDescription] = useState('');
  const [tagName, setTagName] = useState('');
  const [identifier, setIdentifier] = useState('');
  const [filteredTabs, setFilteredTabs] = useState<any>([]);
  const [selectedLabel, setSelectedLabel] = useState<{
    id: number;
    value: string;
  } | null>(null);
  const [inputValue, setInputValue] = useState('');
  const [errors, setErrors] = useState({
    name: '',
    unique_code: '',
    description: '',
    position: '',
    tag_type: '',
    value: '',
  });

  const handleClick = (label: { id: number; value: string }) => {
    setSelectedLabel(label);
    setInputValue(label.value);
  };

  const { tagValue, setTagValue, newTagValue, setNewTagValue } =
    useTagContext();

  const [activeTab, setActiveTab] = useState<string>('tab1');
  const handleTabClick = (id: string) => {
    setActiveTab(id);
  };
  const [count, setCount] = useState(0);

  const axios = useAxios();
  const addToast = useToast();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['add-attachment-list'],
      queryFn: async (): Promise<any> => {
        return await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/list?id=${id}`,
          `/v1/catalog-admin/tags/list?id=${id}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'admin-identifier': `${localStorage.getItem('username')}`,
            },
          },
        );
      },
      enabled: id != undefined ? true : false,
      onError: (err) => {
        addToast('error', 'Error Occured');
        // setCurrentSearch('');
      },
      onSuccess: (data) => {
        setCount(data?.count);
        setTagValue(data?.tags[0]);
      },
    });

  const { mutate: addMutation, isLoading: addLoading } = useMutation(
    async () => {
      const payload = {
        name: newTagValue.name,
        unique_code: newTagValue.unique_code,
        value: newTagValue.value,
        tag_type: newTagValue.tag_type,
        status: newTagValue.status,
        description: newTagValue.description,
        position: newTagValue.position,
      };

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags`,
        `/v1/catalog-admin/tags`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err?.response?.data?.message}`);
      },
      onSuccess: () => {
        addToast('success', `Product Created Successfully.`);
        refetch();
        navigate(`${pageRoutes['GO_TO_CATALOGUE_TAG']}/`);
      },
    },
  );
  const { mutate: updateMutation, isLoading: updateLoading } = useMutation(
    async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
        `/v1/catalog-admin/tags/${prodId}/products`,
        {
          name: tagValue.name,
          unique_code: tagValue.unique_code,
          value: tagValue.value,
          tag_type: tagValue.tag_type,
          status: tagValue.status,
          description: tagValue.description,
          position: tagValue.position,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess() {
        addToast('success', 'Tag Updated Successfully.');
        refetch();
      },
      onError(error) {
        addToast('error', (error as any)?.message);
      },
    },
  );
  const [prodCount, setProdCount] = useState();
  const { data: productData, refetch: refetchAttachProd } = useQuery({
    queryKey: ['get-tag-products'],
    queryFn: async (): Promise<any> => {
      if (prodId) {
        return await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
          `/v1/catalog-admin/tags/${prodId}/products`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'admin-identifier': `${localStorage.getItem('username')}`,
            },
          },
        );
      }
      return {};
    },
    enabled: !!prodId,
    onError: (err: any) => {
      addToast('error', `${err?.message}`);
    },
    onSuccess: () => {
      if (data?.count) {
        setProdCount(data.count);
      }
    },
  });

  // File upload

  const [files, setFiles] = useState<any[]>([]);
  const { mutate: uploadMutation, isLoading: uploadLoading } = useMutation(
    async (selectedFile: string) => {
      const formData = new FormData();
      formData.append('file', selectedFile);
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/file/upload`,
        `${constants.CATALOG_URL}/return/api/v1/returns/file/upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );

      return response;
    },
    {
      onError: (err: any) => {
        addToast('success', 'Error in file upload.');
      },
      onSuccess: (data: any) => {
        addToast('success', 'file uploaded.');
        setTagValue({
          ...tagValue,
          value: data?.file_url,
        });
        setNewTagValue({
          ...newTagValue,
          value: data?.file_url,
        });
      },
    },
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        setFiles(acceptedFiles);
      }
    },

    [files],
  );

  useEffect(() => {
    if (files[0]) {
      uploadMutation(files[0]);
    }
  }, [files]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.gif', '.webp'] },
    onDrop,
  });

  // tag tye dropdown
  const [selectedType, setSelectedType] = useState<any>();
  const handleSelectType = (item: string) => {
    if (item === 'Image') {
      setTagValue({
        ...tagValue,
        tag_type: 'image',
      });
      setNewTagValue({
        ...newTagValue,
        tag_type: 'image',
      });
    } else if (item === 'Text') {
      setTagValue({
        ...tagValue,
        tag_type: 'text',
      });
      setNewTagValue({
        ...newTagValue,
        tag_type: 'text',
      });
    }
    setSelectedType(item);
  };

  useEffect(() => {
    if (tagValue.tag_type === 'text') {
      setSelectedType('Text');
    } else if (tagValue.tag_type === 'image') {
      setSelectedType('Image');
    } else if (tagValue.tag_type === 'property') {
      setSelectedType('Property');
    }
  }, [tagValue]);

  // position dropdown
  const [position, setPosition] = useState<any>();
  const [selectedItem, setSelectedItem] = useState<any>();
  const handleSelectPosition = (item: string) => {
    if (item === 'Top Left') {
      setTagValue({
        ...tagValue,
        position: 'top_left',
      });
      setNewTagValue({
        ...newTagValue,
        position: 'top_left',
      });
    } else {
      setTagValue({
        ...tagValue,
        position: 'bottom_right',
      });
      setNewTagValue({
        ...newTagValue,
        position: 'bottom_right',
      });
    }
    setErrors((prev) => ({
      ...prev,
      position: '',
    }));
    setSelectedItem(item);
  };

  // const validateForm = () => {
  //   const errors = [];

  //   if (!tagValue.tag_type) errors.push('Tag Type');
  //   if (!tagValue.name) errors.push('Tag Name');
  //   if (!tagValue.unique_code) errors.push('Identifier');
  //   if (!tagValue.description) errors.push('Tag Description');
  //   if (!tagValue.position) errors.push('Position');

  //   return errors;
  // };

  const validateForm = () => {
    const newErrors = {
      name: '',
      unique_code: '',
      description: '',
      position: '',
      tag_type: '',
      value: '',
    };

    let isValid = true;

    if (!tagValue.tag_type) {
      newErrors.tag_type = 'Please select a Tag Type';
      isValid = false;
    }
    if (!tagValue.name) {
      newErrors.name = 'Please add Name';
      isValid = false;
    }
    if (!tagValue.unique_code) {
      newErrors.unique_code = 'Please add Identifier';
      isValid = false;
    }
    if (!tagValue.description) {
      newErrors.description = 'Please add Description';
      isValid = false;
    }
    if (!tagValue.position) {
      newErrors.position = 'Please add Position';
      isValid = false;
    }
    if (!tagValue.value) {
      newErrors.value = 'Please add Tag Value';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = () => {
    const errors = validateForm();

    // if (errors.length > 0) {
    //   addToast(
    //     'error',
    //     `Please fill in the following required fields: ${errors.join(', ')}`,
    //   );
    //   return;
    // }

    const isValid = validateForm();

    if (!isValid) {
      addToast('error', 'Please correct the highlighted fields.');
      return;
    }

    if (prodId) {
      updateMutation();
    } else {
      addMutation();
    }
  };

  useEffect(() => {
    if (tagValue.position === 'top_left') {
      setSelectedItem('Top Left');
    } else if (tagValue.position === 'bottom_right') {
      setSelectedItem('Bottom Right');
    }
  }, [tagValue]);

  useEffect(() => {
    if (id) {
      setProdId(id);
      refetch();
    }

    if (prodId) {
      setFilteredTabs([
        { id: 'tab1', label: 'General Information' },
        { id: 'tab2', label: 'Products in Tags' },
      ]);
    } else {
      setFilteredTabs([{ id: 'tab1', label: 'Information' }]);
    }
  }, [id, prodId]);

  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: id ? 'Edit Tags' : 'Add New Tags',
      breadcrumbParent: '',
    });
    if (!prodId) {
      setFilteredTabs([{ id: 'tab1', label: 'General Information' }]);
    }
  }, []);
  return (
    <>
      <TopBar>
        <>
          <Col style={{ display: 'flex', gap: '10px' }}>
            {id ? (
              <>
                <img src={ThumbnailIcon} alt="thumbnail" />
                <div>
                  <div>ID - {tagValue.id}</div>
                </div>
              </>
            ) : (
              <></>
            )}
          </Col>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <Buttons
              style={{ margin: '0px 10px' }}
              onClick={() => {
                navigate(`${pageRoutes['GO_TO_CATALOGUE_TAG']}/`);
              }}
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
            <Button onClick={handleSave} isPrimary>
              {addLoading ? <Spinner /> : 'Save'}
            </Button>
          </Col>
        </>
      </TopBar>
      <Main>
        <TabContainer>
          {filteredTabs.map((tab: any) => (
            <TabButton
              key={tab.id}
              id={tab.id}
              active={activeTab === tab.id}
              onClick={() => handleTabClick(tab.id)}
            >
              {tab.label}
            </TabButton>
          ))}
        </TabContainer>
        <ContentContainer>
          <TabContent active={activeTab === 'tab1'}>
            <>
              <Row>
                <Col size={2}>
                  <Label>Enable Tags</Label>
                </Col>
                <Col size={5}>
                  <Field>
                    <Toggle
                      checked={tagValue.status}
                      onChange={() => {
                        const newStatus = !tagValue.status;
                        setTagValue({
                          ...tagValue,
                          status: newStatus,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          status: newStatus,
                        });
                        // setTagValue({
                        //   ...tagValue,
                        //   status: !status,
                        // });
                        // setNewTagValue({
                        //   ...newTagValue,
                        //   status: !status,
                        // });
                      }}
                      style={{
                        backgroundColor: `${baseTheme.colors.deepBlue}`,
                      }}
                    >
                      <Label hidden>Status</Label>
                    </Toggle>
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col size={2} style={{ marginTop: '8px' }}>
                  <Label>
                    Tag Type <span style={{ color: 'red' }}>*</span>
                  </Label>
                </Col>
                <Col size={4}>
                  <ProductDropdown
                    options={['Text', 'Image']}
                    selectedItem={selectedType}
                    inputValue={undefined}
                    onSelect={handleSelectType}
                    onInputValueChange={setSelectedType}
                  />
                </Col>
                {selectedType === 'Text' && (
                  <Col size={3}>
                    <Input
                      style={{ marginTop: '8px' }}
                      value={tagValue.value}
                      onChange={(e) => {
                        const val = e.target.value;
                        setTagValue({
                          ...tagValue,
                          value: val,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          value: val,
                        });
                        setErrors((prev) => ({
                          ...prev,
                          value: '',
                        }));
                      }}
                    />
                    {errors.value && (
                      <ErrorMessage>{errors.value}</ErrorMessage>
                    )}
                  </Col>
                )}
              </Row>
              {selectedType === 'Image' && (
                <Row>
                  <Col size={2}>
                    <Label>Uplaod</Label>
                  </Col>
                  <Col size={4}>
                    <StyledFileUpload
                      {...getRootProps()}
                      isDragging={isDragActive}
                    >
                      {isDragActive ? (
                        <span>Drop files here</span>
                      ) : (
                        <span>Drop an Image here</span>
                      )}
                      <Input {...getInputProps()} />
                    </StyledFileUpload>
                    <Message>Acceptable formats JPG, WEBP and PNG</Message>
                  </Col>
                  <Col>
                    <Input
                      value={tagValue.value}
                      onChange={(e) => {
                        setTagValue({
                          ...tagValue,
                          value: e.target.value,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          value: e.target.value,
                        });
                        setErrors((prev) => ({
                          ...prev,
                          value: '',
                        }));
                      }}
                    />
                    {errors.value && (
                      <ErrorMessage>{errors.value}</ErrorMessage>
                    )}
                  </Col>
                </Row>
              )}
              <Row>
                <Col size={2}>
                  <Label>
                    Tag Name <span style={{ color: 'red' }}>*</span>
                  </Label>
                </Col>
                <Col size={4}>
                  <Field>
                    <Input
                      value={tagValue.name}
                      validation={errors.name ? 'error' : undefined}
                      onChange={(e) => {
                        setTagValue({
                          ...tagValue,
                          name: e.target.value,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          name: e.target.value,
                        });
                        setErrors((prev) => ({
                          ...prev,
                          name: '',
                        }));
                      }}
                    />
                    {errors.name && <ErrorMessage>{errors.name}</ErrorMessage>}
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col size={2}>
                  <Label>
                    Identifier <span style={{ color: 'red' }}>*</span>
                  </Label>
                </Col>
                <Col size={4}>
                  <Field>
                    <Input
                      value={tagValue.unique_code}
                      onChange={(e) => {
                        setTagValue({
                          ...tagValue,
                          unique_code: e.target.value,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          unique_code: e.target.value,
                        });
                        setErrors((prev) => ({
                          ...prev,
                          unique_code: '',
                        }));
                      }}
                    />
                    {errors.unique_code && (
                      <ErrorMessage>{errors.unique_code}</ErrorMessage>
                    )}
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col size={2}>
                  <Label>
                    Tag Description <span style={{ color: 'red' }}>*</span>
                  </Label>
                </Col>
                <Col size={4}>
                  <Field>
                    <Textarea
                      rows={3}
                      value={tagValue.description}
                      onChange={(e) => {
                        setTagValue({
                          ...tagValue,
                          description: e.target.value,
                        });
                        setNewTagValue({
                          ...newTagValue,
                          description: e.target.value,
                        });
                        setErrors((prev) => ({
                          ...prev,
                          description: '',
                        }));
                      }}
                    />
                    {errors.description && (
                      <ErrorMessage>{errors.description}</ErrorMessage>
                    )}
                  </Field>
                </Col>
              </Row>
              <Row>
                <Col size={2}>
                  <Label>
                    Position
                    <span style={{ color: 'red' }}>*</span>
                  </Label>
                </Col>
                <Col size={4}>
                  <ProductDropdown
                    options={['Top Left', 'Bottom Right']}
                    selectedItem={selectedItem}
                    inputValue={undefined}
                    onSelect={handleSelectPosition}
                    onInputValueChange={setPosition}
                  />
                  {errors.position && (
                    <ErrorMessage>{errors.position}</ErrorMessage>
                  )}
                </Col>
              </Row>
            </>
          </TabContent>
          <TabContent active={activeTab === 'tab2'}>
            <>
              <TagsProduct prodId={prodId} />
            </>
          </TabContent>
        </ContentContainer>
      </Main>
    </>
  );
};
export default AddTags;
