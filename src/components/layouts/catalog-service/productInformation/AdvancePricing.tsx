import React, { useRef, useEffect, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';

import { Button as _Button } from '../../../UI-components/Button';
import { Field, Input, MediaInput, Label } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../../themes/theme';
import styled from 'styled-components';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import {
  CalenderIcon,
  CrossIcon,
  RemoveRedIcon,
} from '../../../../utils/icons';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import { useMutation } from '@tanstack/react-query';
import { ProductColumns } from '../../../table/product/Columns';
import { debounce } from 'lodash';
import routes from '../../../../constants/routes';
import { Spinner } from '@zendeskgarden/react-loaders';
import { Span } from '@zendeskgarden/react-typography';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 15px 0px;
`;

const Button = styled(_Button)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TableHead = styled.thead`
  background-color: ${baseTheme.colors.primaryHue};
  color: white;
  height: ${baseTheme.components.dimension.height.md};
  width: 100%;
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)};
`;
const TableRowHead = styled.th`
  padding: 10px;
  text-align: center;
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;
const TableData = styled.td`
  padding: 10px;
  text-align: center;
`;

const TableComponent = styled.div`
  display: flex;
  justify-content: center;
`;

const TableInput = styled(Input)`
  width: 90px;
`;
const TableMediaInput = styled(MediaInput)`
  /* width: 90px;
  max-width: 90px; */
`;

const PriceType = styled.div`
  border: 1px solid rgb(216, 220, 222);
  padding: calc(${baseTheme.paddings.one}*9) calc(${baseTheme.paddings.sm}*3);
  border-radius: calc(${baseTheme.paddings.one}*4);
  width: 60%;
`;

interface TableRow {
  price_group?: any;
  customer_group: any;
  price_type: any;
  qty: any;
  value: any;
}

interface TierPrice {
  qty: number;
  value: number;
  customer_group: string;
  price_type: string;
}

interface GroupTierPrice {
  price_group: number;
  tier_prices: TierPrice[];
  child_ids: number[];
}

interface GroupTierPriceCollection {
  [key: string]: GroupTierPrice;
}

interface AdvanceInventoryProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const AdvancePricing = () => {
  const {
    contextProdData,
    setContextProdData,
    setContextUpdateProdData,
    detectBrowser,
    refetchLog,
    setRefetchLog,
  } = useProductContext();

  const addToast = useToast();
  const axios = useAxios();
  const [tablePriceError, setTablePriceError] = useState<string | null>(null);
  const [sellingPriceError, setSellingPriceError] = useState<string | null>(
    null,
  );
  const [Itemlength, setItemlength] = useState<number>(0);
  const [sellingFrom, setSellingFrom] = useState<Date | undefined>(
    contextProdData.attributes_list?.special_from_date
      ? new Date(contextProdData.attributes_list?.special_from_date)
      : undefined,
  );
  const [sellingTo, setSellingTo] = useState<Date | undefined>(
    contextProdData.attributes_list?.special_to_date
      ? new Date(contextProdData.attributes_list?.special_to_date)
      : undefined,
  );

  const [childOptions, setChildOptions] = useState<any[]>([]);

  const [originalGroupTierPrice, setOriginalGroupTierPrice] =
    useState<GroupTierPriceCollection>({});
  const [changedGroupTierPrice, setChangedGroupTierPrice] =
    useState<GroupTierPriceCollection>({});

  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
  };

  const { mutate, isLoading } = useMutation(
    async (postData: GroupTierPriceCollection) => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${contextProdData.id}/grouped-tier-price`,
        `/v1/catalog-admin/group_tierprice_update/${contextProdData.id}`,
        { group_tier_price: postData },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `Error in adding tier price`);
      },
      onSuccess: () => {
        addToast('success', `Tier Price is added Successfully.`);
        setRefetchLog(!refetchLog);
        // setVisible(false);
      },
    },
  );

  const handleSellingPriceChange = (e: { target: { value: any } }) => {
    const value = e.target.value;
    if (value === '') {
      // setSellingPrice('');
      setSellingPriceError(null);
      updateContextData('special_price', null);
    } else {
      const parsedValue = parseFloat(value);
      const price = contextProdData?.attributes_list?.price;
      if (price === undefined || price === null) {
        setSellingPriceError('Please enter the product cost (MRP) first');
      } else if (parsedValue > Number(price)) {
        setSellingPriceError(
          'Selling price must be less than the product cost (MRP)',
        );
      } else if (parsedValue <= 0) {
        setSellingPriceError('Selling Price must be greater than zero');
      } else {
        // setSellingPrice(parsedValue);
        setSellingPriceError(null);
        updateContextData('special_price', parsedValue);
      }
    }
  };

  const [rows, setRows] = useState<TableRow[]>([]);
  const [errors, setErrors] = useState<
    {
      price_group?: boolean;
      customer_group?: boolean;
      price_type?: boolean;
      qty: boolean;
      value: boolean;
    }[]
  >([]);
  const [newRow, setNewRow] = useState<TableRow>({
    price_group: null,
    qty: null,
    value: null,
    customer_group: 'ALL GROUPS',
    price_type: 'Fixed',
  });

  const handleRowChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
    field: keyof TableRow,
  ) => {
    const updatedRows = [...rows];
    if (field == 'customer_group' || field == 'price_type') {
      updatedRows[index][field] = e;
    } else {
      updatedRows[index][field] = parseFloat(e.target.value);
    }
    setRows(updatedRows);

    const priceGroup = updatedRows[index].price_group;

    if (priceGroup !== undefined) {
      const updatedTierPrices = updatedRows.filter(
        (row) => row.price_group === priceGroup,
      );
      setChangedGroupTierPrice((prev) => ({
        ...prev,
        [priceGroup]: {
          price_group: priceGroup,
          tier_prices: updatedTierPrices.map((row) => ({
            qty: row.qty,
            value: row.value,
            customer_group: row.customer_group,
            price_type: row.price_group,
          })),
          child_ids: originalGroupTierPrice[priceGroup]?.child_ids || [],
        },
      }));
    }
  };

  const handleRemoveRow = (index: number) => {
    const updatedRows = rows.filter((row, i) => i !== index);
    setRows(updatedRows);

    const priceGroup = rows[index].price_group;
    if (priceGroup !== undefined) {
      const updatedTierPrices = updatedRows.filter(
        (row) => row.price_group === priceGroup,
      );
      if (updatedTierPrices.length === 0) {
        setChangedGroupTierPrice((prev) => {
          const updatedChanges = { ...prev };
          delete updatedChanges[priceGroup];
          return updatedChanges;
        });
      } else {
        setChangedGroupTierPrice((prev) => ({
          ...prev,
          [priceGroup]: {
            price_group: priceGroup,
            tier_prices: updatedTierPrices.map((row) => ({
              qty: row.qty,
              value: row.value,
              customer_group: row.customer_group,
              price_type: row.price_type,
            })),
            child_ids: originalGroupTierPrice[priceGroup]?.child_ids || [],
          },
        }));
      }
    }
  };

  const handleNewRowChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    field: keyof TableRow,
  ) => {
    if (field == 'customer_group' || field == 'price_type') {
      setNewRow((prev) => ({
        ...prev,
        [field]: event,
      }));
    } else {
      if (parseInt(event.target.value) === 0) {
      } else {
        setNewRow((prev) => ({
          ...prev,
          [field]: parseFloat(event.target.value),
        }));
      }
    }
  };

  const handleAddNewRow = () => {
    const { price_group, qty, value, customer_group, price_type } = newRow;
    setSelectedItem('');
    if (contextProdData.type_id === 'grouped') {
      if (
        price_group != 0 &&
        price_group != undefined &&
        price_group >= 0 &&
        qty >= 0 &&
        qty &&
        value >= 0 &&
        value &&
        tablePriceError === ''
      ) {
        setRows((prevRows) => [
          ...prevRows,
          { price_group, qty, value, customer_group, price_type },
        ]);
        setChangedGroupTierPrice((prevChanges) => ({
          ...prevChanges,
          [price_group]: {
            price_group,
            qty,
            value,
            customer_group,
            price_type,
          },
        }));
        setNewRow({
          price_group: price_group,
          qty: null,
          value: null,
          customer_group: 'ALL GROUPS',
          price_type: 'Fixed',
        });
      }
      // else {
      //   addToast('warning', 'Please add all fields value!');
      // }
    } else {
      if (qty && qty > 0 && value && value > 0) {
        setRows((prevRows) => [
          ...prevRows,
          { qty, value, customer_group, price_type },
        ]);
        setNewRow({
          qty: null,
          value: null,
          customer_group: 'ALL GROUPS',
          price_type: 'Fixed',
        });
      } else {
        addToast('warning', 'Please add valid quantity and price values!');
      }
    }
  };

  const createChildIdsMap = (
    contextProdData: ProductColumns,
  ): { [key: number]: number[] } => {
    const childIdsMap: { [key: number]: number[] } = {};

    Object.keys(contextProdData.group_tier_price).forEach((key) => {
      if (key !== 'NaN') {
        const priceGroup = parseInt(key);
        const groupTierPrice = contextProdData.group_tier_price[priceGroup];

        if (groupTierPrice && groupTierPrice.child_ids) {
          childIdsMap[priceGroup] = groupTierPrice.child_ids;
        }
      }
    });

    return childIdsMap;
  };

  const convertRowsToGroupTierPriceCollection = (
    rows: TableRow[],
    childIds: { [key: number]: number[] },
  ): GroupTierPriceCollection => {
    const collection: GroupTierPriceCollection = {};
    Object.keys(childIds).forEach((priceGroup) => {
      const groupId = parseInt(priceGroup, 10);
      collection[groupId] = {
        price_group: groupId,
        tier_prices: [],
        child_ids: childIds[groupId] || [],
      };
    });
    rows.forEach((row) => {
      if (row.price_group !== undefined) {
        collection[row.price_group].tier_prices.push({
          qty: row.qty,
          value: row.value,
          customer_group: row.customer_group,
          price_type: row.price_type,
        });
      }
    });

    return collection;
  };

  const handleSave = () => {
    if (contextProdData.type_id === 'grouped') {
      const childIdsMap = createChildIdsMap(contextProdData);

      const updatedRows = [...rows].filter(
        (row) =>
          row.price_group !== null &&
          !isNaN(row.price_group) &&
          row.price_group !== undefined &&
          row.price_group !== '',
      );
      const groupTierPriceCollection = convertRowsToGroupTierPriceCollection(
        updatedRows,
        childIdsMap,
      );

      const payload: GroupTierPriceCollection = { ...groupTierPriceCollection };

      if (Object.keys(contextProdData.group_tier_price).length === 0) {
        contextProdData.group_tier_price = groupTierPriceCollection;
      }

      setContextProdData((prevState) => ({
        ...prevState,
        group_tier_price: groupTierPriceCollection,
      }));
      if (tablePriceError === 'Wrong value!') {
        addToast('warning', 'Price value should be less than Price Group!');
      } else if (
        groupTierPriceCollection &&
        Object.keys(groupTierPriceCollection).length > 0
      ) {
        mutate(payload);
      }
      // setVisible(false)
    } else {
      if (rows.length > 0) {
        setContextProdData((prevState) => ({
          ...prevState,
          tier_prices: rows,
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          tier_prices: rows,
        }));
        addToast('success', 'Saved Tier Prices.');
        // setVisible(false);
      } else {
        setContextProdData((prevState) => ({
          ...prevState,
          tier_prices: [],
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          tier_prices: [],
        }));
        // setVisible(false);
      }
    }
  };

  useEffect(() => {
    if (rows.length > 0) {
      const minValue = Math.min(...rows.map((item) => item.value));
    }
  }, [rows]);

  useEffect(() => {
    if (
      newRow.price_group &&
      newRow.value &&
      newRow.price_group < newRow.value
    ) {
      setTablePriceError('Wrong value!');
    } else {
      setTablePriceError('');
    }
  }, [newRow]);

  useEffect(() => {
    if (contextProdData) {
      setOriginalGroupTierPrice(contextProdData.group_tier_price);
    }
    if(contextProdData?.attributes_list?.special_from_date){
      setSellingFrom(new Date(contextProdData?.attributes_list?.special_from_date));
    }
    if(contextProdData?.attributes_list?.special_to_date){
      setSellingTo(new Date(contextProdData?.attributes_list?.special_to_date));
    }
  }, [contextProdData]);

  useEffect(() => {
    if (contextProdData?.type_id === 'grouped') {
      if (
        contextProdData?.group_tier_price &&
        typeof contextProdData?.group_tier_price === 'object'
      ) {
        let count = 0;
        setItemlength(count);
        const childArray = Object.values(contextProdData?.group_tier_price).map(
          (item: any, index) => {
            if (item !== null && item !== 'NaN') {
              setItemlength((prev) => prev + (item?.tier_prices?.length || 0));
              return item?.price_group?.toString() || '';
            }
          },
        );
        setChildOptions(childArray.filter((e) => e !== ''));

        const initialRows: TableRow[] = [];
        Object.keys(contextProdData?.group_tier_price).forEach((priceGroup) => {
          contextProdData?.group_tier_price[priceGroup]?.tier_prices.forEach(
            (tierPrice) => {
              initialRows.push({
                price_group: parseInt(priceGroup, 10),
                qty: tierPrice.qty,
                value: tierPrice.value,
                customer_group: tierPrice.customer_group,
                price_type: tierPrice.price_type,
              });
            },
          );
        });
        setRows(initialRows);
      } else {
        setChildOptions([]);
        setRows([]);
      }
    } else {
      setItemlength(
        contextProdData?.tier_prices
          ? Object.keys(contextProdData?.tier_prices).length
          : 0,
      );
      setRows(contextProdData?.tier_prices || []);
    }
  }, [contextProdData.group_tier_price, contextProdData.tier_prices]);

  const formatDateString = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const [selectedItem, setSelectedItem] = useState<any>();
  const [inputValue, setInputValue] = useState('');
  const [matchingOptions, setMatchingOptions] = useState(childOptions);

  const handleSelectChildIds = (item: any) => {
    setSelectedItem(item);
    setNewRow((prev) => ({ ...prev, price_group: item }));
  };
  const filterMatchingOptionsRef = useRef(
    debounce((value: string) => {
      setMatchingOptions(matchingOptions);
    }, 300),
  );

  useEffect(() => {
    filterMatchingOptionsRef.current(inputValue);
  }, [inputValue]);

  // Customer Group
  const [selectedGroupItem, setSelectedGroupItem] = useState('ALL GROUPS');
  const [inputGroupValue, setInputGroupValue] = useState();

  useEffect(() => {
    setMatchingOptions(childOptions);
  }, [childOptions, matchingOptions]);

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);
  return (
    <div style={{ padding: '20px', width: 'auto' }}>
      <Row>
        <Col size={6}>
          <Field>
            <Label
              style={{
                display: 'flex',
                alignItems: 'start',
                justifyContent: 'start',
              }}
            >
              Selling Price
            </Label>
            <Input
              type="number"
              value={contextProdData?.attributes_list?.special_price ?? ''}
              onChange={handleSellingPriceChange}
              disabled
            />
          </Field>
        </Col>
        <Col size={6}>
          <Label
            style={{
              display: 'flex',
              alignItems: 'start',
              justifyContent: 'start',
            }}
          >
            Request Price
          </Label>
          <Input
            type="number"
            value={contextProdData.attributes_list?.msrp || ''}
            onChange={(e) => {
              let value = e.target.value;
              if (parseInt(value) == 0) {
                setContextProdData((prevState) => ({
                  ...prevState,
                  attributes_list: {
                    ...prevState.attributes_list,
                    msrp: null,
                  },
                }));
                setContextUpdateProdData((prevState) => ({
                  ...prevState,
                  attributes_list: {
                    ...prevState.attributes_list,
                    msrp: null,
                  },
                }));
              } else {
                setContextProdData((prevState) => ({
                  ...prevState,
                  attributes_list: {
                    ...prevState.attributes_list,
                    msrp: value,
                  },
                }));
                setContextUpdateProdData((prevState) => ({
                  ...prevState,
                  attributes_list: {
                    ...prevState.attributes_list,
                    msrp: value,
                  },
                }));
              }
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col style={{ display: 'flex' }}>
          <Field>
            <Label
              style={{
                display: 'flex',
                alignItems: 'start',
                justifyContent: 'start',
              }}
            >
              Selling Price From
            </Label>
            <Datepicker
              value={sellingFrom}
              onChange={(date) => {
                if (date instanceof Date) {
                  const dateString = formatDateString(date);
                  setContextProdData((prevState) => ({
                    ...prevState,
                    attributes_list: {
                      ...prevState.attributes_list,
                      special_from_date: dateString,
                    },
                  }));
                  setContextUpdateProdData((prevState) => ({
                    ...prevState,
                    attributes_list: {
                      ...prevState.attributes_list,
                      special_from_date: dateString,
                    },
                  }));
                  setSellingFrom(date);
                }
              }}
            >
              <MediaInput
                end={
                  <div
                    style={{
                      alignItems: 'center',
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    {sellingFrom ? (
                      <CrossIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setSellingFrom(undefined);
                          setContextProdData((prevState) => ({
                            ...prevState,
                            attributes_list: {
                              ...prevState.attributes_list,
                              special_from_date: undefined,
                            },
                          }));
                          setContextUpdateProdData((prevState) => ({
                            ...prevState,
                            attributes_list: {
                              ...prevState.attributes_list,
                              special_from_date: undefined,
                            },
                          }));
                        }}
                      />
                    ) : (
                      <CalenderIcon style={{ cursor: 'pointer' }} />
                    )}
                  </div>
                }
              />
            </Datepicker>
          </Field>

          <div
            style={{
              alignItems: 'center',
              textAlign: 'center',
              display: 'flex',
              margin: 'auto 10%',
              fontWeight: 'bold',
            }}
          >
            {'to'}
          </div>
          <Field>
            <Label
              style={{
                display: 'flex',
                alignItems: 'start',
                justifyContent: 'start',
              }}
            >
              Selling Price to
            </Label>
            <Datepicker
              value={sellingTo}
              minValue={sellingFrom ? new Date(sellingFrom) : undefined}
              onChange={(date) => {
                if (date instanceof Date) {
                  const dateString = formatDateString(date);
                  setContextProdData((prevState) => ({
                    ...prevState,
                    attributes_list: {
                      ...prevState.attributes_list,
                      special_to_date: dateString,
                    },
                  }));
                  setContextUpdateProdData((prevState) => ({
                    ...prevState,
                    attributes_list: {
                      ...prevState.attributes_list,
                      special_to_date: dateString,
                    },
                  }));
                  setSellingTo(date);
                }
              }}
            >
              <MediaInput
                end={
                  <div
                    style={{
                      alignItems: 'center',
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    {sellingTo ? (
                      <CrossIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setSellingTo(undefined);
                          setContextProdData((prevState) => ({
                            ...prevState,
                            attributes_list: {
                              ...prevState.attributes_list,
                              special_to_date: undefined,
                            },
                          }));
                          setContextUpdateProdData((prevState) => ({
                            ...prevState,
                            attributes_list: {
                              ...prevState.attributes_list,
                              special_to_date: undefined,
                            },
                          }));
                        }}
                      />
                    ) : (
                      <CalenderIcon style={{ cursor: 'pointer' }} />
                    )}
                  </div>
                }
              />
            </Datepicker>
          </Field>
        </Col>
      </Row>
      <Row>
        <Col>
          <Label
            style={{
              margin: '20px 0px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '16px',
            }}
          >
            Customer Price Group
          </Label>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <TableHead>
              <tr>
                <TableRowHead>Price Group</TableRowHead>
                <TableRowHead>Customer Group</TableRowHead>
                <TableRowHead>Quantity</TableRowHead>
                <TableRowHead>Price Type</TableRowHead>
                <TableRowHead>Price</TableRowHead>
                <TableRowHead>Actions</TableRowHead>
              </tr>
            </TableHead>
            <tbody>
              {rows.map((row, index) => (
                <tr key={index}>
                  <TableData>
                    <TableComponent>
                      {contextProdData.type_id === 'grouped' ? (
                        <TableInput
                          value={row.price_group}
                          onChange={(e) =>
                            handleRowChange(e, index, 'price_group')
                          }
                          disabled
                        />
                      ) : (
                        <TableInput disabled />
                      )}
                    </TableComponent>
                  </TableData>
                  <TableData>
                    <TableComponent>
                      <ProductDropdown
                        width="150px"
                        options={[
                          'ALL GROUPS',
                          'NOT LOGGED IN',
                          'General',
                          'DentlKart VIP',
                        ]}
                        selectedItem={row.customer_group}
                        inputValue={inputGroupValue}
                        onSelect={(item: any) => {
                          handleRowChange(item, index, 'customer_group');
                        }}
                        onInputValueChange={() => {}}
                      />
                    </TableComponent>
                  </TableData>
                  <TableData>
                    <TableComponent>
                      <div style={{ width: '90px' }}>
                        <TableMediaInput
                          type="number"
                          value={row.qty}
                          onChange={(e) => handleRowChange(e, index, 'qty')}
                        />
                      </div>
                    </TableComponent>
                  </TableData>
                  <TableData>
                    <TableComponent>
                      {/* <ProductDropdown
                          width='150px'
                              options = {['Fixed', 'Discount']}
                              selectedItem = {row.price_type}
                              inputValue={inputPriceValue}
                              onSelect={(item:any)=>{
                                handleRowChange(item, index, 'price_type');
                              }}
                              onInputValueChange={()=>{}}
                            /> */}
                      <PriceType>Fixed</PriceType>
                    </TableComponent>
                  </TableData>
                  <TableData>
                    <TableComponent>
                      <div style={{ width: '100px' }}>
                        <TableMediaInput
                          type="number"
                          value={row.value}
                          start={<>{row.price_type === 'Fixed' ? '₹' : '%'} </>}
                          onChange={(e) => {
                            const inputValue = parseFloat(e.target.value);

                            if (row.price_type === 'Discount') {
                              if (inputValue <= 50) {
                                handleRowChange(e, index, 'value');
                              }
                            } else if (row.price_type === 'Fixed') {
                              handleRowChange(e, index, 'value');
                            }
                          }}
                        />
                      </div>
                    </TableComponent>
                  </TableData>
                  <TableData>
                    <TableComponent>
                      <RemoveRedIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => handleRemoveRow(index)}
                      />
                    </TableComponent>
                  </TableData>
                </tr>
              ))}
              <tr>
                <TableData>
                  <TableComponent>
                    {contextProdData.type_id === 'grouped' &&
                    childOptions.length > 0 ? (
                      <>
                        <ProductDropdown
                          width="150px"
                          options={childOptions}
                          selectedItem={selectedItem}
                          inputValue={inputValue}
                          onSelect={handleSelectChildIds}
                          onInputValueChange={() => {}}
                        />
                      </>
                    ) : (
                      <TableInput disabled />
                    )}
                  </TableComponent>
                </TableData>
                <TableData>
                  <TableComponent>
                    <ProductDropdown
                      width="150px"
                      options={[
                        'ALL GROUPS',
                        'NOT LOGGED IN',
                        'General',
                        'DentlKart VIP',
                      ]}
                      selectedItem={selectedGroupItem}
                      inputValue={inputGroupValue}
                      onSelect={(item: any) => {
                        setSelectedGroupItem(item);
                        handleNewRowChange(item, 'customer_group');
                      }}
                      onInputValueChange={() => {}}
                    />
                  </TableComponent>
                </TableData>
                <TableData>
                  <TableComponent>
                    <div style={{ width: '90px' }}>
                      <TableMediaInput
                        type="number"
                        value={newRow.qty ?? ''}
                        onChange={(event) => handleNewRowChange(event, 'qty')}
                      />
                    </div>
                  </TableComponent>
                </TableData>
                <TableData>
                  <TableComponent style={{ alignItems: 'center' }}>
                    {/* <ProductDropdown
                              width='150px'
                              options = {['Fixed', 'Discount']}
                              selectedItem = {selectedPriceItem}
                              inputValue={inputPriceValue}
                              onSelect={(item:any)=>{
                                setSelectedPriceItem(item);
                                handleNewRowChange(item, 'price_type');
                              }}
                              onInputValueChange={()=>{}}
                            /> */}
                    <PriceType>Fixed</PriceType>
                  </TableComponent>
                </TableData>
                <TableData>
                  <TableComponent>
                    <div style={{ width: '100px' }}>
                      <TableMediaInput
                        type="number"
                        start={
                          <>{newRow.price_type === 'Discount' ? '%' : '₹'} </>
                        }
                        value={newRow.value ?? ''}
                        onChange={(event) => {
                          const value = event.target.value;

                          if (value === '') {
                            handleNewRowChange(event, 'value');
                            return;
                          }

                          const numericValue = parseInt(value, 10);

                          if (contextProdData.type_id === 'grouped') {
                            if (
                              (newRow.price_type === 'Discount' &&
                                numericValue <= 50) ||
                              newRow.price_type === 'Fixed'
                            ) {
                              handleNewRowChange(event, 'value');
                            }
                          } else if (
                            contextProdData?.attributes_list?.special_price &&
                            numericValue <=
                              Number(
                                contextProdData.attributes_list.special_price,
                              )
                          ) {
                            if (
                              (newRow.price_type === 'Discount' &&
                                numericValue <= 50) ||
                              newRow.price_type === 'Fixed'
                            ) {
                              handleNewRowChange(event, 'value');
                            }
                          }
                        }}
                      />
                      {tablePriceError && (
                        <Span style={{ color: 'red', fontSize: '10px' }}>
                          {tablePriceError}
                        </Span>
                      )}
                    </div>
                  </TableComponent>
                </TableData>
                <TableData>
                  <TableComponent>
                    <Button isPrimary isOrange onClick={handleAddNewRow}>
                      Add
                    </Button>
                  </TableComponent>
                </TableData>
              </tr>
            </tbody>
          </table>
        </Col>
      </Row>
      {contextProdData?.type_id === 'grouped' && (
        <Row justifyContent="end">
          <Button
            onClick={handleSave}
            isPrimary
            isOrange
            disabled={rows.length === Itemlength}
          >
            {isLoading ? <Spinner /> : 'Save'}
          </Button>
        </Row>
      )}
    </div>
  );
};

export default AdvancePricing;
