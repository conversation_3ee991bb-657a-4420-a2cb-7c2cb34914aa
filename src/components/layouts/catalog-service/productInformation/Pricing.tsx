import {
  Field,
  Label as _Label,
  Input,
  MediaInput,
} from '@zendeskgarden/react-forms';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import React, { useEffect, useState } from 'react';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { baseTheme } from '../../../../themes/theme';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Span } from '@zendeskgarden/react-typography';
import AdvancePricing from './AdvancePricing';
import styled from 'styled-components';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const Pricing = ({ id }: { id: any }) => {
  const {
    contextProdData,
    contextUpdateProdData,
    setContextProdData,
    setContextUpdateProdData,
    taxClassData,
    taxclassList,
    productErrors,
    setProductErrors,
  } = useProductContext();

  const [productCost, setProductCost] = useState<any>(
    contextProdData.attributes_list?.price,
  );
  const [sellingPrice, setSellingPrice] = useState<any>(
    contextProdData?.attributes_list?.special_price,
  );
  const [priceIsOpen, setPriceIsOpen] = useState(false);
  const [priceError, setPriceError] = useState<string | null>(null);
  const [hsnCode, setHsnCode] = useState<any>(
    contextProdData?.attributes_list?.hsn_code ?? '',
  );
  const [hsnCodeError, setHsnCodeError] = useState<string | null>(null);
  const [dentalCustomFee, setDentalCustomFee] = useState<any>(
    contextProdData?.attributes_list?.dentalkart_custom_fee,
  );
  const [dentalCustomFeeError, setDentalCustomFeeError] = useState<
    string | null
  >(null);
  const [earnedReward, setEarnedReward] = useState<any>(
    contextProdData?.attributes_list?.reward_point_product,
  );
  const [earnedRewardError, setEarnedRewardError] = useState<string | null>(
    null,
  );

  // Tax Class tax_class_id
  const [tax, setTax] = useState<string | undefined>();
  const [inputTaxClass, setInputTaxClass] = useState('');
  const handleSelectTaxClass = (item: any) => {
    const selectedTaxClass = taxClassData?.find((val) => val.value === item);
    const taxClassValue = selectedTaxClass?.id;
    setTax(item);
    if (taxClassValue) {
      setContextProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          tax_class_id: item,
        },
      }));
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          tax_class_id: taxClassValue,
        },
      }));
    }
  };
  const handleInputTaxClassChange = (value: any) => {
    setInputTaxClass(value);
  };

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    const numericPattern = /^[0-9]*\.?[0-9]*$/;

    if (value === '') {
      setProductCost('');
      setPriceError(null);
      setProductErrors({
        ...productErrors,
        price: 'Price cannot be empty',
      });
      updateContextData('price', null);
    } else if (!numericPattern.test(value)) {
      setPriceError('Price must be a valid number');
    } else if (sellingPrice && Number(value) < Number(sellingPrice)) {
      setProductErrors({
        ...productErrors,
        price: 'Product cost must be greater than Selling price',
      });
      updateContextData('price', value);
    } else if (sellingPrice && Number(value) > Number(sellingPrice)) {
      setProductErrors({
        ...productErrors,
        selling_price: '',
        price: '',
      });
      updateContextData('price', value);
    } else {
      const parsedValue = parseFloat(value);
      if (parsedValue <= 0) {
        setPriceError('Price must be greater than zero');
        setProductErrors({
          ...productErrors,
          price: 'Price must be greater than zero',
        });
      } else {
        setProductCost(parsedValue);
        setPriceError(null);
        setProductErrors({
          ...productErrors,
          price: '',
        });
        updateContextData('price', parsedValue);
      }
    }
  };

  const handleSellingPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    const numericPattern = /^[0-9]*\.?[0-9]*$/;

    if (value === '') {
      setSellingPrice('');
      setProductErrors({
        ...productErrors,
        selling_price: 'Selling Price cannot be empty',
      });
      updateContextData('special_price', null);
    } else if (!numericPattern.test(value)) {
      setProductErrors({
        ...productErrors,
        selling_price: 'Selling Price should be a valid number',
      });
      updateContextData('special_price', null);
    } else {
      const parsedValue = parseFloat(value);
      if (parsedValue === 0) {
        setProductErrors({
          ...productErrors,
          selling_price: 'Selling Price should not be zero',
        });
        updateContextData('special_price', null);
      } else {
        const price = contextProdData?.attributes_list?.price;

        if (price === undefined || price === null) {
          setProductErrors({
            ...productErrors,
            selling_price: 'Please enter the product cost (MRP) first',
          });
        } else if (parsedValue > Number(price)) {
          setSellingPrice(parsedValue);
          setProductErrors({
            ...productErrors,
            selling_price:
              'Selling price must be less than the product cost (MRP)',
          });
          updateContextData('special_price', parsedValue);
        } else if (parsedValue < Number(price)) {
          setSellingPrice(parsedValue);
          setProductErrors({
            ...productErrors,
            selling_price: '',
            price: '',
          });
          updateContextData('special_price', parsedValue);
        } else if (parsedValue <= 0) {
          setProductErrors({
            ...productErrors,
            selling_price: 'Selling Price must be greater than zero',
          });
        } else {
          setSellingPrice(parsedValue);
          setProductErrors({
            ...productErrors,
            selling_price: '',
          });
          updateContextData('special_price', parsedValue);
        }
      }
    }
  };

  const handleHsnCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    const parsedValue = value != '' ? value : null;
    if (parseInt(value) <= 0) {
      setHsnCodeError('Hsn code must be greater than zero');
    } else {
      setHsnCode(value);
      updateContextData('hsn_code', parsedValue);
    }
  };

  const handleDentalCustomFeeChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = e.target.value.trim();
    const numericRegex = /^[0-9]*\.?[0-9]*$/;
    if (!numericRegex.test(value)) {
      setDentalCustomFeeError('Dental Custom Fee must be a numeric value');
      return;
    }

    const parsedValue = parseFloat(value);

    if (parsedValue <= 0) {
      setDentalCustomFeeError('Dental Custom Fee must be greater than zero');
    } else {
      setDentalCustomFee(parsedValue);
      updateContextData('dentalkart_custom_fee', parsedValue);
      setDentalCustomFeeError(null);
    }
  };

  const handleEarnedRewardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    const numericRegex = /^[0-9]*$/;

    if (!numericRegex.test(value)) {
      setEarnedRewardError('Please enter a valid numeric value');
      return;
    }

    const reward = value ? parseInt(value, 10) : null;
    const parsedValue = parseFloat(value);

    if (parsedValue <= 0) {
      setEarnedRewardError('Reward Point must be greater than zero');
    } else {
      setEarnedReward(parsedValue);
      updateContextData('reward_point_product', reward);
      setEarnedRewardError(null);
    }
  };

  useEffect(() => {
    if (contextUpdateProdData?.attributes_list?.tax_class_id) {
      const selectedTaxClass = taxClassData?.find(
        (val) =>
          val.id === contextUpdateProdData?.attributes_list?.tax_class_id,
      );
      const taxClassvalue = selectedTaxClass?.value;
      setTax(taxClassvalue);
    } else if (contextProdData?.attributes_list?.tax_class_id) {
      setTax(contextProdData?.attributes_list?.tax_class_id);
    }
    if (contextUpdateProdData?.attributes_list?.price) {
      setProductCost(contextUpdateProdData?.attributes_list?.price);
    } else if (contextProdData?.attributes_list?.price) {
      setProductCost(contextProdData?.attributes_list?.price);
    }
    if (contextUpdateProdData?.attributes_list?.special_price) {
      setSellingPrice(contextUpdateProdData?.attributes_list?.special_price);
    } else if (contextProdData?.attributes_list?.special_price) {
      setSellingPrice(contextProdData?.attributes_list?.special_price);
    }
    if (contextUpdateProdData?.attributes_list?.hsn_code) {
      setHsnCode(contextUpdateProdData?.attributes_list?.hsn_code);
    } else if (contextProdData?.attributes_list?.hsn_code) {
      setHsnCode(contextProdData?.attributes_list?.hsn_code);
    }
    if (contextUpdateProdData?.attributes_list?.dentalkart_custom_fee) {
      setDentalCustomFee(
        contextUpdateProdData?.attributes_list?.dentalkart_custom_fee,
      );
    } else if (contextProdData?.attributes_list?.dentalkart_custom_fee) {
      setDentalCustomFee(
        contextProdData?.attributes_list?.dentalkart_custom_fee,
      );
    }
    if (contextUpdateProdData?.attributes_list?.reward_point_product) {
      setEarnedReward(
        contextUpdateProdData?.attributes_list?.reward_point_product,
      );
    } else if (contextProdData?.attributes_list?.reward_point_product) {
      setEarnedReward(contextProdData?.attributes_list?.reward_point_product);
    }
  }, [contextProdData]);

  useEffect(() => {
    if (!id) {
      setTax('IGST 12%');
      setContextProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          tax_class_id: 'IGST 12%',
        },
      }));
      const taxClass = taxClassData?.find((val) => val.value === 'IGST 12%');
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          tax_class_id: taxClass?.id,
        },
      }));
      setHsnCode('90184900');
      updateContextData('hsn_code', '90184900');
    }
  }, []);

  return (
    <div>
      <Row>
        <Col>
          <Field>
            <Label>
              Product Cost (MRP) <span style={{ color: 'red' }}>*</span>
            </Label>
            <MediaInput
              value={productCost}
              onChange={handlePriceChange}
              disabled={contextProdData.type_id === 'grouped'}
            />
          </Field>
          {productErrors.price && (
            <div style={{ color: 'red', marginTop: '5px' }}>
              {productErrors.price}
            </div>
          )}
        </Col>
        <Col>
          <Field>
            <Label>Selling Price</Label>
            <Input
              // type="number"
              value={sellingPrice}
              onChange={handleSellingPriceChange}
              disabled={
                !contextProdData.attributes_list?.price ||
                contextProdData.type_id === 'grouped'
              }
            />
          </Field>
          {productErrors.selling_price && (
            <div style={{ color: 'red', marginTop: '5px' }}>
              {productErrors.selling_price}
            </div>
          )}
        </Col>
      </Row>
      <Row>
        <Col>
          {contextProdData.type_id === 'grouped' ? (
            <Field>
              <Label>Tax Class</Label>
              <Input value={''} disabled />
            </Field>
          ) : (
            <ProductDropdown
              label="Tax Class"
              options={
                taxclassList.length > 0 ? [...taxclassList] : ['Loading....']
              }
              selectedItem={tax}
              inputValue={inputTaxClass}
              onSelect={handleSelectTaxClass}
              onInputValueChange={handleInputTaxClassChange}
            />
          )}
        </Col>
        <Col>
          {contextProdData.type_id === 'grouped' ? (
            <Field>
              <Label>HSN Code</Label>
              <Input value={''} disabled />
            </Field>
          ) : (
            <>
              <Field>
                <Label>HSN Code</Label>
                <Input value={hsnCode} onChange={handleHsnCodeChange} />
              </Field>
              {hsnCodeError && (
                <div style={{ color: 'red', marginTop: '5px' }}>
                  {hsnCodeError}
                </div>
              )}
            </>
          )}
        </Col>
      </Row>
      <Row>
        <Col size={6}>
          <Label>Dentalkart Delivery Fee</Label>
          <Input
            type="number"
            value={dentalCustomFee}
            onChange={handleDentalCustomFeeChange}
          />
          {dentalCustomFeeError && (
            <div style={{ color: 'red', marginTop: '5px' }}>
              {dentalCustomFeeError}
            </div>
          )}
        </Col>
      </Row>
      <Row>
        <Col size={6}>
          {contextProdData.type_id === 'grouped' ? (
            <Field>
              <Label>Earned Reward Coins</Label>
              <Input value={''} disabled />
            </Field>
          ) : (
            <>
              <Label>Earned Reward Coins</Label>
              <Input
                type="number"
                value={earnedReward}
                onChange={handleEarnedRewardChange}
              />
              {earnedRewardError && (
                <div style={{ color: 'red', marginTop: '5px' }}>
                  {earnedRewardError}
                </div>
              )}
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default Pricing;
