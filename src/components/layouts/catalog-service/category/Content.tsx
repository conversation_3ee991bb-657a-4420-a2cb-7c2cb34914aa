import React, { useEffect, useMemo, useRef, useState } from 'react';

import { Field, Input, Label, Toggle } from '@zendeskgarden/react-forms';

import {
  Autocomplete,
  Field as DropField,
  Dropdown,
  Item,
  Menu,
} from '@zendeskgarden/react-dropdowns';
import { debounce } from 'lodash';
import { Button } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import { CategoryObj } from '../../../../pages/catalog-service/category/Category';
import { IconButton } from '../../../UI-components/IconButton';
import { DeleteIcon, SearchIcon } from '../../../../utils/icons';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import { Spinner } from '@zendeskgarden/react-loaders';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { baseTheme } from '../../../../themes/theme';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';
import { useCategoryContext } from '../../../../pages/catalog-service/category/CategoryContext';
import JoditEditor, { Jodit } from 'jodit-react';
import { JoditEditorWrapper } from '../product/Description';
import DeleteImageModal from '../../../modal/catalog-service/DeleteImageModal';
const Row = styled(_Row)`
  margin: 20px auto;
`;

const CategoryImage = styled.img`
  height: 100px;
`;

const options = ['Dental Brand', 'Endodontist', 'Implontology'];

const isValidString = (str: string) => {
  const regex = /^[a-zA-Z0-9\s()]+$/;
  return str.trim().length > 0 && regex.test(str);
};

const Content = ({
  categoryObj,
  setCategoryObj,
  setChangeValue,
  setSelectedValue,
  changeValue,
  parentCategories,
  refetch,
}: {
  categoryObj: CategoryObj;
  setCategoryObj: (cat: CategoryObj) => void;
  setChangeValue: (cat: any) => void;
  setSelectedValue: (item: MenuItem) => void;
  changeValue: any;
  parentCategories: { name: string; id: number }[];
  refetch: () => any;
}) => {
  const editor = useRef<any>(null);
  const [inputValue, setInputValue] = useState('');
  const [matchingOptions, setMatchingOptions] = useState(options);
  const fileInputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const { titleError, setTitleError } = useCategoryContext();
  const wordLimit = 40;
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const toast = useToast();
  const config = useMemo<any>(
    () => ({
      readonly: false,
      toolbarAdaptive: false,
      height: 300,
      placeholder: '',
      buttons: [
        'undo',
        'redo',
        'paragraph',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'ul',
        'ol',
        'outdent',
        'indent',
        '|',
        'font',
        'fontsize',
        '|',
        'link',
        'unlink',
        '|',
        'align',
        'hr',
        '|',
      ],
      events: {
        beforePaste: (e: any, html: any) => html,
        afterInit: (editor: any) => {
          const statusbar = document.createElement('div');
          statusbar.style.backgroundColor = '#f8f8f8';
          statusbar.style.color = 'red';
          statusbar.style.fontSize = '11px';
          statusbar.style.padding = '1px 4px';

          function calcStat() {
            const text = editor.editor.innerText.trim();
            const wordCount = text
              .split(/[\s\n\r\t]+/)
              .filter((value: any) => value).length;
            const charCount = text.replace(/[\s\n\r\t]+/, '').length;

            statusbar.innerText = `Words: ${wordCount} Chars: ${charCount}`;
          }

          editor.events
            .on('change afterInit', editor.async.debounce(calcStat, 100))
            .on('afterInit', () => {
              editor.container.appendChild(statusbar);
            });
        },
      },
    }),
    [],
  );

  const filterMatchingOptionsRef = useRef(
    debounce((value: string) => {
      const matchedOptions = options.filter(
        (option) =>
          option.trim().toLowerCase().indexOf(value.trim().toLowerCase()) !==
          -1,
      );

      setMatchingOptions(matchedOptions);
    }, 300),
  );

  useEffect(() => {
    filterMatchingOptionsRef.current(inputValue);
  }, [inputValue]);
  const handleButtonClick = () => {
    (fileInputRef?.current as any)?.click();
  };

  const handleFileChange = (e: any) => {
    setSelectedFile(e.target.files[0]);
    setCategoryObj({
      ...categoryObj,
      uploadedFile: e.target.files[0],
    });
    setChangeValue({
      ...changeValue,
      uploadedFile: e.target.files[0],
    });
  };
  const addToast = useToast();
  const axios = useAxios();
  const [queryEnabled, setQueryEnabled] = useState(false);
  const [categName, setCategName] = useState('');
  const [categoryId] = useState(categoryObj.id);
  const [categoryData, setCategoryData] = useState<
    { name: string; id: number }[]
  >([]);
  const [selectedCategory, setSelectedCategory] = useState<{
    name: string;
    id: number;
  }>();

  const fetchCatagoryList = async ({ pageParam = 1, search = '' }) => {
    try {
      const response = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories?page_no=${pageParam}&category_name=${search}`,
        `/v1/catalog-admin/category?page_no=${pageParam}&category_name=${search}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response;
    } catch (error) {
      throw error;
    }
  };

  const {
    data,
    isSuccess,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    error,
  } = useInfiniteQuery({
    queryKey: ['category-list', categName],
    queryFn: ({ pageParam = 1 }) =>
      fetchCatagoryList({ pageParam, search: categName }),
    getNextPageParam: (lastPage: any) => {
      const currentPage = lastPage?.page_no;
      const totalPages = Math.ceil(lastPage?.item_count / lastPage?.page_size);
      if (currentPage < totalPages) {
        return currentPage + 1;
      } else {
        return undefined;
      }
    },
  });
  useEffect(() => {
    if (data) {
      const value = data.pages.flatMap((page: any) =>
        page?.items.map((d: MenuItem) => ({ name: d.name, id: d.id })),
      );
      setCategoryData(value);
    }
  }, [data]);

  const { mutate: deleteCategory, isLoading: isDeleting } = useMutation(
    async (categoryObj: CategoryObj) => {
      const response = await axios.delete(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/images/${categoryObj?.id}`,
        `/v1/catalog-admin/category/images/${categoryObj?.id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'Content-Type': 'multipart/form-data;',
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess(data) {
        addToast('success', 'category image deleted successfully');
        refetch();
        setSelectedFile(null);
      },
      onError(error) {
        addToast('error', (error as any)?.message);
      },
    },
  );

  useEffect(() => {
    console.log('categoryObj: ', categoryObj);
    if (categoryObj?.parent?.name) {
      setSelectedCategory({
        id: categoryObj?.parent?.id,
        name: categoryObj?.parent?.name,
      });
    }
  }, [categoryObj]);

  useEffect(() => {
    setTitleError('');
  }, []);
  return (
    <div>
      <Field>
        <Row>
          <Col size={3}>
            <Label>Enable in Category</Label>
          </Col>
          <Col>
            <Toggle
              checked={categoryObj.status}
              onChange={() => {
                const value = !categoryObj.status;
                setCategoryObj({
                  ...categoryObj,
                  status: value,
                });
                setChangeValue({
                  ...changeValue,
                  status: value,
                });
              }}
            >
              <Label hidden>Enable in Category</Label>
            </Toggle>
          </Col>
        </Row>
      </Field>
      <Field>
        <Row>
          <Col size={3}>
            <Label>Include in Menu</Label>
          </Col>
          <Col>
            <Toggle
              checked={categoryObj.include_in_menu}
              onChange={() => {
                const value: boolean = !categoryObj.include_in_menu;
                setCategoryObj({
                  ...categoryObj,
                  include_in_menu: value,
                });

                setChangeValue((prevState: any) => ({
                  ...prevState,
                  include_in_menu: value,
                }));
              }}
            >
              <Label hidden>Include in Menu</Label>
            </Toggle>
          </Col>
        </Row>
      </Field>
      <Row>
        <Col size={3}>
          <Label>Parent Category</Label>
        </Col>
        <Col sm={5}>
          <Dropdown
            inputValue={categName}
            selectedItem={selectedCategory}
            onSelect={(i) => {
              setSelectedCategory(i);
              setCategoryObj({
                ...categoryObj,
                parent: i,
              });
              setChangeValue({
                ...changeValue,
                parent: i?.id,
              });
            }}
            onInputValueChange={(value) => setCategName(value)}
            downshiftProps={{
              itemToString: (item: { name: string; id: number }) => {
                return item;
              },
            }}
          >
            <DropField>
              <Label hidden>Parent Category</Label>
              <Autocomplete start={<SearchIcon />}>
                {selectedCategory?.name}
              </Autocomplete>
            </DropField>
            <Menu>
              {categoryData.length ? (
                categoryData.map((option) => (
                  <Item key={option?.id} value={option}>
                    <span>
                      {option?.name} (ID:{option?.id})
                    </span>
                  </Item>
                ))
              ) : (
                <Item disabled>No matches found</Item>
              )}
              {(isFetching || isLoading) && (
                <Row justifyContent="center">
                  <Spinner />
                </Row>
              )}
              {hasNextPage && !(isFetching || isLoading) && (
                <Row justifyContent="center">
                  <Button
                    isBasic
                    onClick={() => {
                      fetchNextPage();
                    }}
                    style={{ marginBottom: `${baseTheme.paddings.base}` }}
                  >
                    Load More
                  </Button>
                </Row>
              )}
            </Menu>
          </Dropdown>
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Category Image</Label>
        </Col>
        <Col size={5}>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleFileChange}
            accept="image/jpg, image/jpeg, image/png, image/webp"
          />
          {selectedFile ? (
            <CategoryImage
              src={URL.createObjectURL(selectedFile)}
              alt="Category Img"
            />
          ) : categoryObj.image ? (
            <CategoryImage
              src={
                categoryObj.image.startsWith('https://')
                  ? categoryObj.image
                  : `https://images1.dentalkart.com/${categoryObj.image}`
              }
              alt={categoryObj.name}
            />
          ) : (
            <></>
          )}
          <div>
            {categoryObj.image ? (
              <Tooltip content="Delete Category">
                <IconButton
                  onClick={() => {
                    setIsDeleteModalVisible(true);
                    // if (categoryObj.image) {
                    //   deleteCategory(categoryObj);
                    // } else {
                    //   setSelectedFile(null);
                    //   setChangeValue({
                    //     ...changeValue,
                    //     uploadedFile: undefined,
                    //   });
                    // }
                  }}
                  style={{ marginRight: `${baseTheme.paddings.md}` }}
                  isDanger
                >
                  {isDeleting ? <Spinner /> : <DeleteIcon />}
                </IconButton>
              </Tooltip>
            ) : (
              <></>
            )}
            <Button onClick={handleButtonClick}>
              {selectedFile || categoryObj.image ? 'Change' : 'Add'}
            </Button>
          </div>
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Category Name</Label>
        </Col>
        <Col size={5}>
          <Input
            value={categoryObj.name}
            onChange={(e) => {
              const newValue = e.target.value;
              const onlyNumbersRegex = /^\d+$/;
              if (onlyNumbersRegex.test(newValue)) {
                setTitleError('Category Name should not be an integer!');
              } else if (!isValidString(newValue)) {
                setTitleError(
                  'Category Name contains invalid characters or empty!',
                );
              } else if (newValue.length > wordLimit) {
                setTitleError(
                  'Category Name should not be more than 25 words!',
                );
              } else {
                setTitleError('');
              }
              if (newValue.length <= wordLimit) {
                setCategoryObj({
                  ...categoryObj,
                  name: newValue,
                });
                setChangeValue({
                  ...changeValue,
                  name: newValue,
                });
              }
            }}
          />
          {titleError && <div style={{ color: 'red' }}>{titleError}</div>}
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Description</Label>
        </Col>
        <Col size={8}>
          <JoditEditorWrapper>
            <JoditEditor
              value={categoryObj?.description ? categoryObj.description : ''}
              onBlur={(newContent) => {
                // if (newContent.length <= 225) {
                setCategoryObj({
                  ...categoryObj,
                  description: newContent,
                });
                setChangeValue({
                  ...changeValue,
                  description: newContent,
                });
                // }
              }}
              config={config}
            />
          </JoditEditorWrapper>
        </Col>
      </Row>
      {isDeleteModalVisible && (
        <DeleteImageModal
          isOpen={isDeleteModalVisible}
          onClose={() => setIsDeleteModalVisible(false)}
          onDelete={() => {
            deleteCategory(categoryObj);
            setIsDeleteModalVisible(false);
          }}
        />
      )}
    </div>
  );
};

export default Content;
