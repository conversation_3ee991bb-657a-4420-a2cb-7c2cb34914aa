import React, { useEffect, useState } from 'react';
import { Field, Input, Label, Select } from '@zendeskgarden/react-forms';

import { Button } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import { CategoryObj } from '../../../../pages/catalog-service/category/Category';
import { SM, LG } from '@zendeskgarden/react-typography';
import {
  Table,
  Head,
  HeaderRow,
  HeaderCell,
  Body,
  Row as TableRow,
  Cell,
} from '@zendeskgarden/react-tables';
import { IconButton } from '../../../UI-components/IconButton';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { useDropzone } from 'react-dropzone';

import useToast from '../../../../hooks/useToast';
import { Spinner } from '@zendeskgarden/react-loaders';
import { ArrowLeftIcon, PlusIcon, UploadIcon } from 'lucide-react';
import { PenIcon, RemoveIcon } from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import constants from '../../../../constants';

import axios from 'axios';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
// Interfaces
interface CategoryBanner {
  id: string;
  title: string;
  landing_page_type: 'category' | 'product' | 'external' | 'sale';
  landing_page_url: string;
  web_url: string;
  mobile_url: string;
  tab_url: string;
  icon_url: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ImageUploadProps {
  label: string;
  imageUrl?: string;
  onImageUpload: (file: File) => void;
  onImageDelete?: () => void;
  isUploading?: boolean;
  maxSize?: string;
  dimensions?: string;
}

const Row = styled(_Row)`
  margin: 20px auto;
`;

const TableContainer = styled.div`
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
`;

const ImageUploadContainer = styled.div`
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;

  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  &.uploading {
    border-color: #3b82f6;
    background: #eff6ff;
  }
`;

const ImagePreview = styled.div`
  position: relative;
  width: 150px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const DeleteButton = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(239, 68, 68, 1);
    transform: scale(1.1);
  }
`;

const ChangeButton = styled.button`
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(59, 130, 246, 1);
  }
`;

const FormSection = styled.div`
  background: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
`;

const BackButton = styled(Button)`
  margin-right: 1rem;
`;

enum ViewMode {
  LIST = 'list',
  CREATE = 'create',
  EDIT = 'edit',
}
// Image Upload Component
const ImageUploadComponent: React.FC<ImageUploadProps> = ({
  label,
  imageUrl,
  onImageUpload,
  onImageDelete,
  isUploading = false,
  maxSize = '200KB',
  dimensions = 'Recommended: 1200x400px',
}) => {
  const onDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      onImageUpload(acceptedFiles[0]);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB,
    noClick: !!imageUrl, // Disable click when image exists
  });

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onImageDelete) {
      onImageDelete();
    }
  };

  const handleChange = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Trigger file input click
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        onImageUpload(file);
      }
    };
    input.click();
  };

  return (
    <div>
      <Label style={{ marginBottom: '8px', display: 'block' }}>{label}</Label>
      {imageUrl ? (
        <div style={{ position: 'relative', display: 'inline-block' }}>
          <ImagePreview>
            <img src={imageUrl} alt={label} />
            <DeleteButton onClick={handleDelete} title="Delete image">
              ×
            </DeleteButton>
            <ChangeButton onClick={handleChange} title="Change image">
              Change
            </ChangeButton>
          </ImagePreview>
        </div>
      ) : (
        <ImageUploadContainer
          {...getRootProps()}
          className={isDragActive || isUploading ? 'uploading' : ''}
        >
          <input {...getInputProps()} />
          {isUploading ? (
            <div>
              <Spinner size="24px" />
              <SM style={{ marginTop: '8px' }}>Uploading...</SM>
            </div>
          ) : (
            <div>
              <UploadIcon style={{ fontSize: '24px', marginBottom: '8px' }} />
              <SM style={{ marginBottom: '4px' }}>
                {isDragActive ? 'Drop image here' : 'Click or drag image here'}
              </SM>
              <SM style={{ color: '#6b7280', fontSize: '12px' }}>
                Max Size: {maxSize} | {dimensions}
              </SM>
              <SM style={{ color: '#6b7280', fontSize: '12px' }}>
                Format: JPEG/PNG/JPG/GIF
              </SM>
            </div>
          )}
        </ImageUploadContainer>
      )}
    </div>
  );
};

const DisplaySettings = ({
  categoryObj,
  setCategoryObj,
  setChangeValue,
  changeValue,
}: {
  categoryObj: CategoryObj;
  setCategoryObj: (cat: CategoryObj) => void;
  setChangeValue: (cat: any) => void;
  changeValue: any;
}) => {
  // State Management
  const [currentView, setCurrentView] = useState<ViewMode>(ViewMode.LIST);
  const [selectedBanner, setSelectedBanner] = useState<CategoryBanner | null>(
    null,
  );
  const [banners, setBanners] = useState<CategoryBanner[]>([]);

  const [formData, setFormData] = useState<{
    title: string;
    landing_page_type: 'category' | 'product' | 'external' | 'sale';
    landing_page_url: string;
    web_url: string | undefined;
    mobile_url: string | undefined;
    tab_url: string | undefined;
    icon_url: string | undefined;
  }>({
    title: '',
    landing_page_type: 'category',
    landing_page_url: '',
    web_url: undefined,
    mobile_url: undefined,
    tab_url: undefined,
    icon_url: undefined,
  });
  const [uploadingImages, setUploadingImages] = useState({
    web_url: false,
    mobile_url: false,
    tab_url: false,
    icon_url: false,
  });

  // Hooks
  const addToast = useToast();
  const queryClient = useQueryClient();

  // Fetch banners data with React Query
  const { isLoading: isLoadingBanners, refetch } = useQuery({
    queryKey: ['category-banners'],
    queryFn: async () => {
      const response = await axios.get(
        `/v1/catalog-admin/category/banners?category_id=${categoryObj.id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            x_api_key: `${constants.CATALOG_KEY}`,
          },
        },
      );
      // console.log('banner response', response);
      return response.data.data;
    },
    onSuccess: (data) => {
      // console.log('data', data);
      setBanners(data || []);
    },
    onError: (error) => {
      console.error('Error fetching banners:', error);
      addToast('error', 'Failed to fetch banners');
    },
    enabled: false,
  });

  // Image Upload Function
  const uploadImageToS3 = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('files', file);

    try {
      const response = await axios.post(
        'https://interface-prod.dentalkart.com/interface/admin-api/v1/media-upload',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
          },
        },
      );
      console.log('response', response);
      return response.data.uploaded_url as string;
    } catch (error) {
      console.error('Upload failed:', error);
      throw new Error('Failed to upload image');
    }
  };

  // Handle Image Upload
  const handleImageUpload = async (
    imageType: keyof typeof uploadingImages,
    file: File,
  ) => {
    setUploadingImages((prev) => ({ ...prev, [imageType]: true }));

    try {
      const imageUrl = await uploadImageToS3(file);
      console.log('imageUrl', imageUrl);
      setFormData((prev) => ({
        ...prev,
        [imageType]: imageUrl,
      }));
      addToast(
        'success',
        `${imageType.replace('_', ' ')} image uploaded successfully`,
      );
    } catch (error) {
      addToast(
        'error',
        `Failed to upload ${imageType.replace('_', ' ')} image`,
      );
    } finally {
      setUploadingImages((prev) => ({ ...prev, [imageType]: false }));
    }
  };

  // Handle Image Delete
  const handleImageDelete = (imageType: keyof typeof uploadingImages) => {
    setFormData((prev) => ({
      ...prev,
      [imageType]: null,
    }));
    addToast('success', `${imageType.replace('_', ' ')} image removed`);
  };

  // Reset form to initial state
  const resetForm = () => {
    setFormData({
      title: '',
      landing_page_type: 'category',
      landing_page_url: '',
      web_url: undefined,
      mobile_url: undefined,
      tab_url: undefined,
      icon_url: undefined,
    });
    setSelectedBanner(null);
  };

  // Create/Update Banner Mutation
  const { mutate: saveBanner, isLoading: isSaving } = useMutation(
    async (payload: any) => {
      if (selectedBanner) {
        // Update existing banner
        return await axios.patch(
          `/v1/catalog-admin/category/banners/${selectedBanner.id}`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'admin-identifier': `${localStorage.getItem('username')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
            },
          },
        );
      } else {
        // Create new banner
        return await axios.post(`/v1/catalog-admin/category/banners`, payload, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        });
      }
    },
    {
      onSuccess: () => {
        addToast(
          'success',
          selectedBanner
            ? 'Banner updated successfully'
            : 'Banner created successfully',
        );

        // Invalidate and refetch banners
        queryClient.invalidateQueries(['category-banners', categoryObj.id]);

        // Reset form and go back to list
        resetForm();
        setCurrentView(ViewMode.LIST);
      },
      onError: (error: any) => {
        console.error('Save banner error:', error);
        addToast('error', 'Failed to save banner');
      },
    },
  );

  // Form validation
  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.title.trim()) {
      errors.push('Banner title is required');
    }

    if (!formData.landing_page_url.trim()) {
      errors.push('Landing page URL is required');
    }

    // Validate URL format
    if (
      formData.landing_page_url.trim() &&
      !formData.landing_page_url.startsWith('/')
    ) {
      errors.push('Landing page URL should start with "/"');
    }

    // Check if at least one image is uploaded
    const hasImages =
      formData.web_url ||
      formData.mobile_url ||
      formData.tab_url ||
      formData.icon_url;
    if (!hasImages) {
      errors.push('At least one banner image is required');
    }

    return errors;
  };

  // Handle Form Submit
  const handleSubmit = () => {
    const validationErrors = validateForm();

    if (validationErrors.length > 0) {
      validationErrors.forEach((error) => addToast('error', error));
      return;
    }

    const payload = {
      ...formData,
      category_id: (categoryObj as any).entity_id || categoryObj.id, // Link to current category
    };
    console.log('payload', payload);
    saveBanner(payload);
  };

  // Handle Edit Banner
  const handleEditBanner = (banner: CategoryBanner) => {
    setSelectedBanner(banner);
    setFormData({
      title: banner.title || '',
      landing_page_type: banner.landing_page_type || 'category',
      landing_page_url: banner.landing_page_url || '',
      web_url: banner.web_url || undefined,
      mobile_url: banner.mobile_url || undefined,
      tab_url: banner.tab_url || undefined,
      icon_url: banner.icon_url || undefined,
    });
    setCurrentView(ViewMode.EDIT);
  };

  // Delete Banner Mutation
  const { mutate: deleteBanner } = useMutation(
    async (bannerId: string) => {
      return await axios.delete(
        `/v1/catalog-admin/category/banners/${bannerId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
    },
    {
      onSuccess: () => {
        addToast('success', 'Banner deleted successfully');
        // Invalidate and refetch banners
        queryClient.invalidateQueries(['category-banners', categoryObj.id]);
      },
      onError: (error: any) => {
        console.error('Delete banner error:', error);
        addToast('error', 'Failed to delete banner');
      },
    },
  );

  // Handle Delete Banner
  const handleDeleteBanner = (bannerId: string) => {
    if (window.confirm('Are you sure you want to delete this banner?')) {
      deleteBanner(bannerId);
    }
  };

  useEffect(() => {
    if (categoryObj.id) {
      refetch();
    }
  }, [categoryObj.id]);

  // Render Banner List View
  const renderBannerList = () => (
    <div>
      <ActionBar>
        <div>
          <LG style={{ fontWeight: 'bold', color: '#1f2937' }}>
            Category Banners
          </LG>
          <SM style={{ color: '#6b7280', marginTop: '4px' }}>
            Manage banners for {categoryObj.name || 'this category'}
          </SM>
        </div>
        <Button isPrimary onClick={() => setCurrentView(ViewMode.CREATE)}>
          <Button.StartIcon>
            <PlusIcon />
          </Button.StartIcon>
          Add New Banner
        </Button>
      </ActionBar>

      <TableContainer>
        {isLoadingBanners ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '3rem',
            }}
          >
            <Spinner size="32px" />
            <SM style={{ marginLeft: '12px' }}>Loading banners...</SM>
          </div>
        ) : (
          <Table>
            <Head>
              <HeaderRow
                style={{
                  background: `${baseTheme.colors.primaryHue}`,
                  color: 'white',
                }}
              >
                <HeaderCell>Title</HeaderCell>
                <HeaderCell>Landing Page Type</HeaderCell>
                <HeaderCell>Landing Page URL</HeaderCell>
                <HeaderCell>Images</HeaderCell>
                {/* <HeaderCell>Status</HeaderCell> */}
                <HeaderCell width="120px">Actions</HeaderCell>
              </HeaderRow>
            </Head>
            <Body>
              {banners.map((banner) => (
                <TableRow key={banner.id}>
                  <Cell>
                    <div>
                      <div style={{ fontWeight: '500' }}>{banner.title}</div>
                      <SM style={{ color: '#6b7280' }}>
                        Created:{' '}
                        {new Date(banner.created_at).toLocaleDateString()}
                      </SM>
                    </div>
                  </Cell>
                  <Cell>
                    <span
                      style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        backgroundColor: '#f3f4f6',
                        fontSize: '12px',
                        textTransform: 'capitalize',
                      }}
                    >
                      {banner.landing_page_type}
                    </span>
                  </Cell>
                  <Cell>
                    <div
                      style={{
                        maxWidth: '200px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {banner.landing_page_url}
                    </div>
                  </Cell>
                  <Cell>
                    <div style={{ display: 'flex', gap: '4px' }}>
                      {(
                        [
                          'web_url',
                          'mobile_url',
                          'tab_url',
                          'icon_url',
                        ] as const
                      ).map((type) => (
                        <div
                          key={type}
                          style={{
                            width: '24px',
                            height: '24px',
                            borderRadius: '4px',
                            backgroundColor: banner[type]
                              ? '#10b981'
                              : '#e5e7eb',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <span
                            style={{
                              fontSize: '10px',
                              color: banner[type] ? 'white' : '#6b7280',
                              fontWeight: 'bold',
                            }}
                          >
                            {type.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </Cell>
                  {/* <Cell>
                    <span
                      style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        backgroundColor: banner.is_active
                          ? '#dcfce7'
                          : '#fee2e2',
                        color: banner.is_active ? '#166534' : '#dc2626',
                        fontSize: '12px',
                        fontWeight: '500',
                      }}
                    >
                      {banner.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </Cell> */}
                  <Cell>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <Tooltip content="Edit Banner">
                        <IconButton
                          size="small"
                          onClick={() => handleEditBanner(banner)}
                        >
                          <PenIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip content="Delete Banner">
                        <IconButton
                          size="small"
                          isDanger
                          onClick={() => handleDeleteBanner(banner.id)}
                        >
                          <RemoveIcon />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </Cell>
                </TableRow>
              ))}
            </Body>
          </Table>
        )}

        {!isLoadingBanners && banners.length === 0 && (
          <div
            style={{
              padding: '3rem',
              textAlign: 'center',
              color: '#6b7280',
            }}
          >
            <div style={{ marginBottom: '1rem' }}>
              <UploadIcon style={{ fontSize: '48px', opacity: 0.5 }} />
            </div>
            <div style={{ marginBottom: '0.5rem', fontWeight: '500' }}>
              No banners found
            </div>
            <div style={{ fontSize: '14px' }}>
              Create your first banner to get started
            </div>
          </div>
        )}
      </TableContainer>
    </div>
  );

  // Render Banner Form View (Create/Edit)
  const renderBannerForm = () => (
    <div>
      <ActionBar>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <BackButton
            onClick={() => {
              resetForm();
              setCurrentView(ViewMode.LIST);
            }}
          >
            <Button.StartIcon>
              <ArrowLeftIcon />
            </Button.StartIcon>
            Back
          </BackButton>
          <div>
            <LG style={{ fontWeight: 'bold', color: '#1f2937' }}>
              {selectedBanner ? 'Edit Banner' : 'Create New Banner'}
            </LG>
            <SM style={{ color: '#6b7280', marginTop: '4px' }}>
              {selectedBanner
                ? 'Update banner details'
                : 'Add a new banner for this category'}
            </SM>
          </div>
        </div>
        <Button
          isPrimary
          onClick={handleSubmit}
          disabled={Object.values(uploadingImages).some(Boolean) || isSaving}
        >
          {isSaving ? (
            <>
              <Spinner size="16px" style={{ marginRight: '8px' }} />
              {selectedBanner ? 'Updating...' : 'Saving...'}
            </>
          ) : selectedBanner ? (
            'Update Banner'
          ) : (
            'Save Banner'
          )}
        </Button>
      </ActionBar>

      <FormSection>
        <Row mb="lg">
          <Col lg={6}>
            <Field>
              <Label>Banner Title *</Label>
              <Input
                placeholder="Enter banner title"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
              />
            </Field>
          </Col>
          <Col lg={6}>
            <Label>Landing Page Type *</Label>
            <ProductDropdown
              options={['category', 'product']}
              selectedItem={formData.landing_page_type}
              inputValue={undefined}
              onSelect={(item: string) => {
                setFormData((prev) => ({
                  ...prev,
                  landing_page_type: item as 'category' | 'product',
                }));
              }}
              onInputValueChange={() => {}}
            />
          </Col>
        </Row>

        <Row mb="lg">
          <Col lg={12}>
            <Field>
              <Label>Landing Page URL *</Label>
              <Input
                placeholder="Enter landing page URL (e.g., /category/dental-tools)"
                value={formData.landing_page_url}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    landing_page_url: e.target.value,
                  }))
                }
              />
            </Field>
          </Col>
        </Row>

        <Row mb="lg">
          <Col lg={12}>
            <Label
              style={{
                marginBottom: '16px',
                display: 'block',
                fontSize: '16px',
                fontWeight: '600',
              }}
            >
              Banner Images
            </Label>
            <SM style={{ color: '#6b7280', marginBottom: '24px' }}>
              Upload images for different devices. All images are required for
              optimal display across devices.
            </SM>
          </Col>
        </Row>

        <Row mb="lg">
          <Col lg={6}>
            <ImageUploadComponent
              label="Web Banner"
              imageUrl={formData.web_url}
              onImageUpload={(file) => handleImageUpload('web_url', file)}
              onImageDelete={() => handleImageDelete('web_url')}
              isUploading={uploadingImages.web_url}
              maxSize="500KB"
              dimensions="Recommended: 1200x400px"
            />
          </Col>
          <Col lg={6}>
            <ImageUploadComponent
              label="Mobile Banner"
              imageUrl={formData.mobile_url}
              onImageUpload={(file) => handleImageUpload('mobile_url', file)}
              onImageDelete={() => handleImageDelete('mobile_url')}
              isUploading={uploadingImages.mobile_url}
              maxSize="300KB"
              dimensions="Recommended: 800x600px"
            />
          </Col>
        </Row>

        <Row mb="lg">
          <Col lg={6}>
            <ImageUploadComponent
              label="Tablet Banner"
              imageUrl={formData.tab_url}
              onImageUpload={(file) => handleImageUpload('tab_url', file)}
              onImageDelete={() => handleImageDelete('tab_url')}
              isUploading={uploadingImages.tab_url}
              maxSize="400KB"
              dimensions="Recommended: 1024x768px"
            />
          </Col>
          <Col lg={6}>
            <ImageUploadComponent
              label="Icon/Thumbnail"
              imageUrl={formData.icon_url}
              onImageUpload={(file) => handleImageUpload('icon_url', file)}
              onImageDelete={() => handleImageDelete('icon_url')}
              isUploading={uploadingImages.icon_url}
              maxSize="100KB"
              dimensions="Recommended: 200x200px"
            />
          </Col>
        </Row>
      </FormSection>
    </div>
  );

  // Main Render
  return (
    <>
      {currentView === ViewMode.LIST && renderBannerList()}
      {(currentView === ViewMode.CREATE || currentView === ViewMode.EDIT) &&
        renderBannerForm()}
    </>
  );
};

export default DisplaySettings;
