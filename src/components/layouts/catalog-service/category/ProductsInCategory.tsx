import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { baseTheme } from '../../../../themes/theme';
import {
  Checkbox,
  Field,
  Input,
  InputGroup,
  Label,
} from '@zendeskgarden/react-forms';
import { Button } from '../../../UI-components/Button';
import {
  FilterIcon,
  ProdImageIcon,
  SearchIcon,
  DeleteIcon,
} from '../../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import ProductImg from '../../../../utils/icons/product.png';
import styled from 'styled-components';
import { Tag } from '@zendeskgarden/react-tags';
import ProductInCategoryFilter from '../../../modal/catalog-service/ProductInCategoryFilter';
import { IconButton } from '../../../UI-components/IconButton';
import CategorySearchFilter from '../../../modal/catalog-service/CategorySearchModal';
import useAxios from '../../../../hooks/useAxios';
import { useMutation, useQuery } from '@tanstack/react-query';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import { Skeleton } from '@zendeskgarden/react-loaders';
import DeleteProductModal from '../../../modal/catalog-service/DeleteProductModal';
import Product from '../../../../pages/catalog-service/product/Product';
import useToast from '../../../../hooks/useToast';
import routes from '../../../../constants/routes';
import NothingToshow from '../../../UI-components/NothingToShow';
import { Pagination } from '@zendeskgarden/react-pagination';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';
import UpdatePositionModal from '../../../modal/catalog-service/UpdatePositionModal';

const HeadCard = styled.div`
  justify-content: center;
  color: ${baseTheme.colors.primaryHue};
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: Lexend;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;
const Banner = styled.div`
  border-radius: 7px;
  padding: ${(_) => _.theme.space.xs};
  height: 80px;
  width: 80px;
  background: ${baseTheme.colors.white};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
`;

const ProductRow = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  margin: 12px 0px;
  padding: 12px 0px;
  border-radius: 5px;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  background-color: white;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0; /* Add this line to prevent shrinking */
  min-height: fit-content; /* Add this line to maintain minimum height */

  &:hover {
    background-color: #f8f8f8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &:active {
    cursor: grabbing;
  }
`;

const ProductDetails = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  padding-left: 16px;
`;

const ProductProperty = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
`;

const PropertyLabel = styled.span`
  color: #666;
  min-width: 70px;
`;

const PropertyValue = styled.span`
  color: #333;
  font-weight: 500;
`;

const PositionBadge = styled.div`
  position: absolute;
  top: 12px;
  right: 12px;
  border: 1px solid ${baseTheme.colors.lightBlack};
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 12px;
  font-weight: 600;
`;

const SpanItem = styled.span`
  color: ${baseTheme.colors.grey};
  margin-right: 5px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration-line: line-through;
`;

const StyledInputGroup = styled(Row)`
  color: white;
  border: 1px solid white;
  border-radius: 2px;
  cursor: pointer;
`;

export interface Product {
  id: number;
  sku: string;
  status: boolean;
  type_id: string;
  stock_status: boolean | null;
  international_status: boolean;
  name: string;
  position: number;
  thumbnail: string;
  visibility: boolean | null;
  price: string;
  selling_price: string;
}

export enum ProductTypes {
  SIMPLE = 'simple',
  GROUPED = 'grouped',
  VIRTUAL = 'virtual',
}

export enum ProductStatus {
  ENABLED = 'Enabled',
  DISABLED = 'Disabled',
}

export enum ProductStockStatus {
  IN_STOCK = 'In Stock',
  OUT_OF_STOCK = 'Out of Stock',
}
export interface ProductFilters {
  status?: ProductStatus;
  type_id?: ProductTypes;
  stock_status?: ProductStockStatus;
  international_active?: boolean;
  name?: string;
}

const ProductsInCategory = ({
  selectedCategory,
}: {
  selectedCategory: MenuItem;
}) => {
  const [isFilter, setIsFilter] = useState(false);
  const [isSearch, setIsSearch] = useState(false);
  const [searchProduct, setSearchProduct] = useState<string>('');
  const [filters, setfilters] = useState<ProductFilters>({
    status: undefined,
    type_id: undefined,
    stock_status: undefined,
    international_active: undefined,
    name: undefined,
  });
  const [positionUpdateProduct, setPositionUpdateProduct] = useState<
    Product | undefined
  >();
  const [selectedProduct, setselectedProduct] = useState<Product>();
  const [currentPage, setcurrentPage] = useState(1);
  // const [draggedItem, setDraggedItem] = useState<Product | null>(null);
  const axios = useAxios();
  const addToast = useToast();
  const {
    data: productList,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['product-list', filters, currentPage],
    queryFn: async () => {
      const res = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/${selectedCategory.id}/products/list`,
        `/v1/catalog-admin/category/category-products/${selectedCategory?.id}`,
        {
          filters: {
            name: filters?.name || undefined,
            status:
              filters?.status === undefined
                ? undefined
                : filters.status === ProductStatus.ENABLED
                ? true
                : false,
            type_id:
              filters?.type_id === undefined ? undefined : filters.type_id,
            stock_status:
              filters?.stock_status === undefined
                ? undefined
                : filters.stock_status === ProductStockStatus.IN_STOCK
                ? true
                : false,
            international_active:
              filters?.international_active === undefined
                ? undefined
                : filters.international_active,
          },
          pagination: {
            page: currentPage,
            size: 20,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            admin_identifier: `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return res as any;
    },
  });

  const { mutate, isLoading: isAdding } = useMutation(
    async (products: Product[]) => {
      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/products`,
        `/v1/catalog-admin/category/add-products`,
        {
          categoryId: selectedCategory.id,
          product_ids: products.map((prod, idx) => ({
            product_id: prod.id,
            position: productList?.item_count + idx + 1,
          })),
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess(data) {
        addToast('success', 'product added successfully');
        refetch();
        setIsSearch(false);
      },
      onError(error) {
        addToast('error', (error as any)?.message);
        setIsSearch(false);
      },
    },
  );
  useEffect(() => {
    refetch();
  }, [filters]);
  return (
    <div style={{ padding: '0.5rem', marginBottom: '20px' }}>
      <Row
        style={{
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          padding: '0.75rem 1rem',
          width: '100%',
          borderTopLeftRadius: '10px',
          borderTopRightRadius: '10px',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '12px',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flex: '1',
          }}
        >
          <Button
            style={{
              minWidth: '50px',
              height: '40px',
              color: 'white',
              border: '1px solid white',
              borderRadius: '5px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={() => {
              setIsSearch(true);
            }}
          >
            Add Products
          </Button>

          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              borderRadius: '4px',
              border: '1px solid white',
              color: 'white',
              cursor: 'pointer',
              flex: '1',
              maxWidth: '300px',
              height: '40px',
              overflow: 'hidden',
            }}
          >
            <IconButton>
              <SearchIcon style={{ color: 'white' }} />
            </IconButton>
            <input
              style={{
                color: 'white',
                width: '100%',
                fontSize: '14px',
                backgroundColor: 'transparent',
                border: 'none',
                outline: 'none',
                padding: '0 8px',
                height: '100%',
              }}
              value={searchProduct}
              onChange={(e) => setSearchProduct(e.currentTarget.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setfilters({
                    ...filters,
                    name: e.currentTarget.value.trim(),
                  });
                  setcurrentPage(1);
                }
              }}
              placeholder="Search by Name"
            />
          </div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <span
            style={{
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              padding: '6px 12px',
              borderRadius: '4px',
              fontWeight: '500',
            }}
          >
            Count: {productList?.item_count || 0}
          </span>
          {(filters.status ||
            filters.type_id ||
            filters.stock_status ||
            filters.international_active ||
            filters.name) && (
            <Button
              isBasic
              style={{
                color: 'white',
                padding: '8px 12px',
                height: '40px',
              }}
              onClick={() => {
                setSearchProduct('');
                setfilters({
                  status: undefined,
                  type_id: undefined,
                  stock_status: undefined,
                  international_active: undefined,
                  name: undefined,
                });
                addToast('success', 'Filters cleared');
              }}
            >
              Reset Filters
            </Button>
          )}

          <Button
            isBasic
            style={{
              color: 'white',
              border: '1px solid white',
              borderRadius: '4px',
              padding: '8px 12px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
            onClick={() => setIsFilter(!isFilter)}
          >
            <FilterIcon />
            <Span>Filter</Span>
          </Button>
        </div>
      </Row>

      <Row
        style={{
          padding: '0.5rem',
          width: '100%',
          alignItems: 'flex-start',
          alignContent: 'flex-start',
        }}
      >
        {isLoading &&
          new Array(5).fill(1).map((_, ind) => (
            <ProductRow key={ind}>
              <Skeleton style={{ height: '80px', width: '80px' }}></Skeleton>
              <div style={{ marginLeft: '16px', width: '100%' }}>
                <Skeleton
                  style={{ height: '20px', width: '50%', marginBottom: '8px' }}
                ></Skeleton>
                <Skeleton
                  style={{ height: '14px', width: '70%', marginBottom: '6px' }}
                ></Skeleton>
                <Skeleton style={{ height: '14px', width: '30%' }}></Skeleton>
              </div>
            </ProductRow>
          ))}

        {!isLoading && productList && productList.items.length == 0 && (
          <Row style={{ width: '100%' }} justifyContent="center">
            <NothingToshow />
          </Row>
        )}

        {!isLoading && productList && (
          <div
            style={{
              width: '100%',
              height: '60vh',
              display: 'flex',
              flexDirection: 'column',
              gap: '12px',
              padding: '0 0.5rem',
              overflowY: 'auto',
            }}
          >
            {productList?.items.map((prod: Product, ind: number) => (
              <ProductRow key={ind}>
                <div
                  style={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    // margin: '0px 10px',
                  }}
                >
                  <Banner>
                    <img
                      style={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain',
                      }}
                      src={
                        prod.thumbnail ||
                        'https://www.dentalkart.com/media/catalog/product/no_selection'
                      }
                      alt="prod"
                    />
                  </Banner>
                </div>

                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    width: '100%',
                    padding: '0px 10px',
                    alignItems: 'center',
                  }}
                >
                  <div style={{ width: '300px' }}>
                    <ProductProperty>
                      <PropertyLabel>Name:</PropertyLabel>
                      <PropertyValue>{prod.name}</PropertyValue>
                    </ProductProperty>
                    <ProductProperty>
                      <PropertyLabel>ID:</PropertyLabel>
                      <PropertyValue>{prod.id}</PropertyValue>
                    </ProductProperty>

                    <ProductProperty>
                      <PropertyLabel>SKU:</PropertyLabel>
                      <PropertyValue>{prod?.sku}</PropertyValue>
                    </ProductProperty>
                    <ProductProperty>
                      <PropertyLabel>Status:</PropertyLabel>
                      <Tag
                        isRegular
                        style={{
                          color: prod?.status
                            ? `${baseTheme.colors.veryLightGreen}`
                            : `${baseTheme.colors.solidRed}`,
                          backgroundColor: prod.status
                            ? `${baseTheme.colors.veryDarkGreen}`
                            : `${baseTheme.colors.veryDarkRed}`,
                        }}
                      >
                        <Span isBold>
                          {prod.status ? 'Enabled' : 'Disabled'}
                        </Span>
                      </Tag>
                    </ProductProperty>
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'start',
                      alignItems: 'start',
                    }}
                  >
                    <ProductProperty>
                      <PropertyLabel>Price:</PropertyLabel>
                      <PropertyValue>
                        {prod?.price && <SpanItem>{prod?.price}</SpanItem>}
                        {prod?.selling_price || 0}
                      </PropertyValue>
                    </ProductProperty>
                    <ProductProperty>
                      <PropertyLabel>Type:</PropertyLabel>
                      <PropertyValue>{prod?.type_id}</PropertyValue>
                    </ProductProperty>

                    <ProductProperty>
                      <PropertyLabel>Visibility:</PropertyLabel>
                      <PropertyValue>
                        {prod?.visibility ? prod.visibility : 'NVI'}
                      </PropertyValue>
                    </ProductProperty>
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <div>
                      <input
                        style={{
                          textAlign: 'center',
                          width: `${Math.max(
                            prod?.position?.toString()?.length * 10,
                            30,
                          )}px`,
                          padding: '4px',
                          border: '1px solid #ccc',
                          borderRadius: '4px',
                          cursor: 'pointer',
                        }}
                        value={prod?.position}
                        readOnly
                        onClick={() => setPositionUpdateProduct(prod)}
                      />
                    </div>
                    <div>Position</div>
                  </div>
                  <div>
                    <IconButton
                      onClick={() => setselectedProduct(prod)}
                      isDanger
                    >
                      <DeleteIcon />
                    </IconButton>
                  </div>
                </div>
              </ProductRow>
            ))}
          </div>
        )}

        {productList?.items.length > 0 && (
          <Row style={{ width: '100%' }} justifyContent="center">
            <Pagination
              currentPage={currentPage}
              totalPages={productList?.pages_count}
              onChange={(currentPage: number) => {
                setcurrentPage(currentPage);
              }}
            />
          </Row>
        )}
      </Row>
      {/* <Row justifyContent="center">
        <Pagination
          currentPage={currentPage}
          totalPages={productList?.pages_count}
          onChange={(currentPage: number) => {
            setcurrentPage(currentPage);
          }}
        />
      </Row> */}

      {selectedProduct && (
        <DeleteProductModal
          close={() => {
            setselectedProduct(undefined);
          }}
          selectedProduct={selectedProduct}
          selectedCategory={selectedCategory}
          refetch={refetch}
        />
      )}

      {positionUpdateProduct && (
        <UpdatePositionModal
          close={() => setPositionUpdateProduct(undefined)}
          product={positionUpdateProduct}
          categoryId={selectedCategory.id}
          onSuccess={refetch}
        />
      )}

      {isFilter && (
        <ProductInCategoryFilter
          filters={filters}
          setCurrentPage={setcurrentPage}
          setFilter={(filters) => {
            setfilters(filters);
          }}
          close={() => {
            setIsFilter(false);
          }}
        />
      )}
      {isSearch && (
        <CategorySearchFilter
          close={() => {
            setIsSearch(false);
          }}
          loading={isAdding}
          setProducts={(products) => {
            if (products && products.length > 0)
              mutate(products as any as Product[]);
          }}
        />
      )}
    </div>
  );
};

export default ProductsInCategory;
