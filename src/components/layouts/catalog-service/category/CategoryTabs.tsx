import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { MenuItem } from '../../../../pages/catalog-service/CategoryDropDown';
import DisplaySettings from './DisplaySettings';
import SEO from './SEO';
import ProductsInCategory from './ProductsInCategory';
import useAxios from '../../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import { CatalogCategory } from '../../../../types/types';
import Content from './Content';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';
import { useCategoryContext } from '../../../../pages/catalog-service/category/CategoryContext';

const TabContainer = styled.div`
  display: flex;
  width: 100%;
`;

interface TabButtonProps {
  active: boolean;
}

const TabButton = styled.button<TabButtonProps>`
  flex: 1;
  margin: 5px 0px;
  padding: 10px 20px;
  border-top: 5px solid ${(props) => (props.active ? '#0A5F79' : '#BCBCBC')};
  background-color: ${(props) => (props.active ? '#FFF' : '#F6F6F6')};
  color: ${(props) => (props.active ? '#0A5F79' : '#000')};
  border-right: none;
  border-left: none;
  border-bottom: none;
  cursor: pointer;
`;

const ContentContainer = styled.div`
  margin-top: 20px;
`;

interface TabContentProps {
  active: boolean;
}

const TabContent = styled.div<TabContentProps>`
  display: ${(props) => (props.active ? 'block' : 'none')};
  height: 100%;
`;
enum menuTypes {
  CONTENT = 'content',
  DISPLAY_SETTINGS = 'display-settings',
  SEARCH_ENGINE_OPTIMIZATION = 'search-engine-optimization',
  PRODUCT_IN_CATEGORY = 'product-in-category',
}
export interface CategoryObj extends CatalogCategory {
  parent: CatalogCategory;
  uploadedFile?: any;
}

const CategoryTabs = ({
  selectedCategory,
  parentCategories,
  categoryObj,
  categoryValue,
  setSelectedCategory,
  setCategoryValue,
  setCategoryObj,
  urlKey,
  setUrlKey,
  refetchTree,
  toggleCat,
  setToggleCat,
  prodId,
}: {
  selectedCategory: MenuItem | undefined;
  setSelectedCategory: any;
  parentCategories: { name: string; id: number }[];
  categoryObj: CategoryObj;
  categoryValue: any;
  setCategoryValue: (cat: any) => void;
  setCategoryObj: (cat: CategoryObj) => void;
  urlKey: string;
  setUrlKey: (value: string) => void;
  refetchTree: () => void;
  toggleCat: boolean;
  setToggleCat: (i: boolean) => void;
  prodId: number | undefined;
}) => {
  const [activeTab, setActiveTab] = useState<menuTypes>(menuTypes.CONTENT);
  const { setOriginalUrlKey } = useCategoryContext();

  const axios = useAxios();
  const {
    data: categoryData,
    isLoading: isCategoryDataLoading,
    refetch,
  } = useQuery({
    queryKey: ['category-data', prodId],
    queryFn: async () => {
      const categoryData = (await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/search-categories`,
        `/v1/catalog-admin/category/search-categories`,
        {
          category_ids: [prodId ?? selectedCategory?.id],
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
        // )) as { search_categories: CatalogCategory[] };
      )) as CatalogCategory[];

      const parentData = (await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/fetch-parent/${
        //   prodId ?? selectedCategory?.id
        // }`,
        `/v1/catalog-admin/category/fetch-parent/${
          prodId ?? selectedCategory?.id
        }`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      )) as { searched_category: number; parent_category: CatalogCategory };

      return {
        // category: categoryData?.search_categories[0],
        category: categoryData[0],
        parent: parentData?.parent_category,
      };
    },
    onSuccess(data) {
      setOriginalUrlKey('');
      setCategoryObj({
        ...data.category,
        parent: data.parent,
      });
      setSelectedCategory({
        ...selectedCategory,
        id: data.category.id,
        name: data.category.name,
      });
      setCategoryValue({});
      refetchTree();
    },
  });

  useEffect(() => {
    refetch();
  }, [prodId]);

  useEffect(() => {
    refetch();
  }, [toggleCat]);

  useEffect(() => {
    setActiveTab(menuTypes.CONTENT);
  }, [selectedCategory]);

  return categoryData && !isCategoryDataLoading ? (
    <>
      <TabContainer>
        <TabButton
          active={activeTab === menuTypes.CONTENT}
          onClick={() => setActiveTab(menuTypes.CONTENT)}
        >
          Content
        </TabButton>
        <TabButton
          active={activeTab === menuTypes.DISPLAY_SETTINGS}
          onClick={() => setActiveTab(menuTypes.DISPLAY_SETTINGS)}
        >
          Banners
        </TabButton>
        <TabButton
          active={activeTab === menuTypes.SEARCH_ENGINE_OPTIMIZATION}
          onClick={() => setActiveTab(menuTypes.SEARCH_ENGINE_OPTIMIZATION)}
        >
          Search Engine Optimization
        </TabButton>
        {selectedCategory && (
          <TabButton
            active={activeTab === menuTypes.PRODUCT_IN_CATEGORY}
            onClick={() => setActiveTab(menuTypes.PRODUCT_IN_CATEGORY)}
          >
            Products in Category
          </TabButton>
        )}
      </TabContainer>
      <ContentContainer>
        <TabContent active={activeTab === menuTypes.CONTENT}>
          <Content
            categoryObj={categoryObj}
            setCategoryObj={(cat) => {
              setCategoryObj(cat);
              // setCatObjectValue(cat);
            }}
            setSelectedValue={setSelectedCategory}
            setChangeValue={setCategoryValue}
            changeValue={categoryValue}
            parentCategories={parentCategories}
            refetch={refetch}
          />
        </TabContent>
        <TabContent active={activeTab === menuTypes.DISPLAY_SETTINGS}>
          <DisplaySettings
            categoryObj={categoryObj}
            setCategoryObj={(cat) => {
              setCategoryObj(cat);
              // setCatObjectValue(cat);
            }}
            setChangeValue={setCategoryValue}
            changeValue={categoryValue}
          />
        </TabContent>
        <TabContent active={activeTab === menuTypes.SEARCH_ENGINE_OPTIMIZATION}>
          <SEO
            categoryObj={categoryObj}
            setCategoryObj={(cat) => {
              setCategoryObj(cat);
              // setCatObjectValue(cat);
            }}
            setChangeValue={setCategoryValue}
            changeValue={categoryValue}
            urlKey={urlKey}
            setUrlKey={setUrlKey}
          />
        </TabContent>
        {selectedCategory && (
          <TabContent active={activeTab === menuTypes.PRODUCT_IN_CATEGORY}>
            <ProductsInCategory selectedCategory={selectedCategory} />
          </TabContent>
        )}
      </ContentContainer>
    </>
  ) : (
    <></>
  );
};

export default CategoryTabs;
