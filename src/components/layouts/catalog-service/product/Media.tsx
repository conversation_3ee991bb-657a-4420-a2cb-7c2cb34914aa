import React, { useCallback, useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { Field, FileUpload } from '@zendeskgarden/react-forms';
import { useDropzone } from 'react-dropzone';
import styled from 'styled-components';
import {
  CloseEye,
  DropIcon,
  OpenEye,
  VideoPlayIcon,
} from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import MediaFileDrawer from '../../../drawer/catalog-service/MediaFileDrawer';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { Button } from '../../../UI-components/Button';
import { useLocation, useParams } from 'react-router-dom';
import useAxios from '../../../../hooks/useAxios';
import { useMutation } from '@tanstack/react-query';
import useToast from '../../../../hooks/useToast';
import MediaUpload from '../../../modal/catalog-service/MediaUpload';
import MediaVideoDrawer from '../../../drawer/catalog-service/MediaVideoDrawer';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

const Main = styled.div`
  padding: 10px 20px;
  background-color: #fff;
  height: 100%;
`;

const Image = styled.img`
  width: ${baseTheme.components.dimension.height.base * 20}px;
  height: ${baseTheme.components.dimension.height.base * 20}px;
  object-fit: contain;
  border-radius: 5px;
`;

const Video = styled.div`
  width: ${baseTheme.components.dimension.height.base * 20}px;
  height: ${baseTheme.components.dimension.height.base * 20}px;
  object-fit: contain;
  border-radius: 5px;
`;

const Tags = styled.span`
  background-color: ${baseTheme.colors.deepBlue};
  border-radius: 4px;
  color: white;
  padding: 0 ${baseTheme.paddings.base};
  margin: ${baseTheme.paddings.base} ${baseTheme.paddings.base};
`;

const File = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: ${baseTheme.components.dimension.width.base}px;
  border: ${baseTheme.borders.sm} ${baseTheme.colors.greyBorderColor};
  background-color: ${baseTheme.colors.white};
  padding: ${baseTheme.space.md};
  margin: ${baseTheme.space.md};
  height: ${baseTheme.components.dimension.width.base200};
  cursor: pointer;
`;
const StyledFileUpload = styled(FileUpload)`
  min-height: ${(p) => p.theme.space.base * 20}px;
  font-size: 14px;
  pointer-events: ${(props) => (props.disabled ? 'none' : 'auto')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
`;

interface MediaGallery {
  id: number;
  value: string;
  is_disabled?: boolean;
  position?: number;
  url?: string;
  image_tags: any;
  title?: string;
  description?: string;
}

interface FileData {
  files: File;
  value?: string;
  position?: number;
  image_tags?: string[];
  title?: string;
  is_disabled?: boolean;
  description?: string;
  url?: string;
  delete?: boolean;
}

const Media = ({ refetch }: { refetch: () => void }) => {
  const { contextProdData, setImageTags, detectBrowser, setSelectedFileData } =
    useProductContext();
  const addToast = useToast();

  const [media_gallery, setMediaGallery] = useState<MediaGallery[]>([]);

  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });

  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const { id } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const axios = useAxios();

  const [files, setFiles] = useState<FileData[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isVideoOpen, setIsVideoOpen] = useState(false);
  const [isUpload, setIsUpload] = useState(false);
  const [isSaveEnabled, setIsSaveEnabled] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const filteredFiles = acceptedFiles.filter(
        (file) =>
          (file.type.startsWith('image/') &&
            /\.(png|jpe?g)$/i.test(file.name)) ||
          (file.type === 'video/mp4' && /\.mp4$/i.test(file.name)),
      );

      const remainingSpace = 5 - files.length;
      const newFiles = filteredFiles.slice(0, remainingSpace).map((file) => ({
        files: file,
        value: '',
        position: 1,
        image_tags: ['base'],
        title: '',
        is_disabled: false,
        description: '',
        url: '',
      }));
      setFiles([...files, ...newFiles]);
      setIsSaveEnabled(true);
    },
    [files],
  );
  const removeFile = useCallback(
    (fileIndex: any) => {
      const updatedFiles = [...files];
      updatedFiles.splice(fileIndex, 1);
      setFiles(updatedFiles);
    },
    [files],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.webp'] },
    onDrop,
  });

  const handleFileClick = (fileData: any) => {
    setSelectedFileData(fileData);
    if (fileData.url) {
      setIsVideoOpen(!isVideoOpen);
    } else {
      setIsOpen(!isOpen);
    }
  };

  const updateMediaGallery = async (updatedMediaGallery: any) => {
    try {
      const formData = new FormData();
      let mediaData = JSON.stringify(updatedMediaGallery);
      formData.append('mediaData', mediaData);
      await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/handle-media/${prodId}`,
        `/v1/catalog-admin/handle-media/${prodId}`,
        formData,

        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      refetch();
      addToast('success', 'Positions updated successfully');
    } catch (error) {
      addToast('error', 'Failed to update positions');
    }
  };

  type ReorderedFilesObject = {
    [key: string]: (typeof media_gallery)[0];
  };
  const moveItem = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const updatedFiles = [...media_gallery];

      const dragItem = updatedFiles[dragIndex];
      const hoverItem = updatedFiles[hoverIndex];

      const tempPosition = dragItem.position;
      dragItem.position = hoverItem.position;
      hoverItem.position = tempPosition;

      updatedFiles[dragIndex] = dragItem;
      updatedFiles[hoverIndex] = hoverItem;

      const reorderedFiles = updatedFiles.map((file) => ({
        ...file,
        ...(file.url
          ? { isVideo: true }
          : {
              image_tags:
                typeof file.image_tags === 'string' && file.image_tags !== ''
                  ? file.image_tags
                      .split(',')
                      .map((item: string) => item.trim())
                  : [],
            }),
      }));

      const filteredFiles = reorderedFiles.filter((_, index) =>
        [dragIndex, hoverIndex].includes(index),
      );

      const reorderedFilesAsObject: ReorderedFilesObject = filteredFiles.reduce(
        (acc: ReorderedFilesObject, file) => {
          acc[file.id] = file;
          return acc;
        },
        {},
      );

      setMediaGallery(updatedFiles);
      updateMediaGallery(reorderedFilesAsObject);
    },
    [media_gallery],
  );

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDrop = (index: number) => {
    if (draggedIndex !== null && draggedIndex !== index) {
      moveItem(draggedIndex, index);
    }
    setDraggedIndex(null);
  };

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);

  useEffect(() => {
    setMediaGallery(contextProdData?.media_gallery_entries || []);
    const imageItem = contextProdData.media_gallery_entries
      ? contextProdData.media_gallery_entries
          .map((item) => {
            const images = Array.isArray(item?.image_tags)
              ? item.image_tags.flat()
              : item.image_tags;
            return images;
          })
          .flat()
          .filter((item) => item != null)
      : [];

    setImageTags(imageItem.flatMap((tag) => tag.split(', ')));
  }, [contextProdData]);

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);

  return (
    <Main>
      <Row>
        <Col size={3} style={{ height: '100vh' }}>
          <Field>
            <StyledFileUpload>
              {isDragActive ? (
                <span>Drop files here</span>
              ) : (
                <div>
                  <DropIcon />
                  <div>
                    <span style={{ color: `${baseTheme.colors.veryDarkGray}` }}>
                      Upload file here
                    </span>
                  </div>
                  <div style={{ marginTop: `${baseTheme.paddings.md}` }}>
                    <Button
                      style={{ cursor: 'pointer' }}
                      isPrimary
                      onClick={() => {
                        setIsUpload(true);
                      }}
                    >
                      Upload Image
                    </Button>
                  </div>
                </div>
              )}
            </StyledFileUpload>
          </Field>

          <Field style={{ marginTop: '20px' }}>
            <StyledFileUpload>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  margin: '10px 10px',
                }}
              >
                <Button
                  isPrimary
                  onClick={() => {
                    setIsVideoOpen(!isVideoOpen);
                  }}
                >
                  Upload Video
                </Button>
              </div>
            </StyledFileUpload>
          </Field>
        </Col>
        <Col>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
              gap: `${baseTheme.space.md}`,
            }}
          >
            {media_gallery &&
              media_gallery.map((item, index) => {
                return (
                  <File
                    key={index}
                    draggable
                    onDragStart={() => handleDragStart(index)}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={() => handleDrop(index)}
                    onClick={() => {
                      handleFileClick(item);
                    }}
                  >
                    {item.value.toLowerCase().endsWith('.mp4') ||
                    item.url ||
                    item.url === '' ? (
                      // <Video >
                      //   <source src={item.value} type="video/mp4" />
                      // </Video>
                      <Video>
                        <Image src={item.value} alt={item.title} />
                        {item.url && (
                          <VideoPlayIcon
                            style={{
                              position: 'absolute',
                              top: '45%',
                              left: '42%',
                              width: baseTheme.space.lg,
                              height: baseTheme.space.lg,
                              display: 'flex',
                              alignItems: 'center',
                              alignContent: 'center',
                              justifyContent: 'center',
                              justifyItems: 'center',
                              zIndex: 10,
                            }}
                          />
                        )}
                        <div
                          style={{
                            position: 'absolute',
                            top: baseTheme.space.sm,
                            left: baseTheme.space.md,
                            border: `${baseTheme.borders.sm} ${baseTheme.colors.lightBlack}`,
                            borderRadius: baseTheme.borderRadii.xxxl,
                            width: baseTheme.space.md,
                            height: baseTheme.space.md,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            zIndex: 10,
                          }}
                          // onClick={() => handleDragStart(index)}
                        >
                          {item.position}
                        </div>
                      </Video>
                    ) : (
                      <div>
                        <Image src={item.value} alt={item.title} />
                        <div
                          style={{
                            position: 'absolute',
                            bottom: `${baseTheme.space.lg}`,
                          }}
                        >
                          {item?.image_tags &&
                          typeof item.image_tags === 'string' ? (
                            item.image_tags
                              .split(',')
                              .map((tag: any, index: number) => (
                                <Tags key={index}>{tag.trim()}</Tags>
                              ))
                          ) : (
                            <></>
                          )}
                        </div>

                        <div
                          style={{
                            position: 'absolute',
                            top: baseTheme.space.sm,
                            left: baseTheme.space.md,
                            border: `${baseTheme.borders.sm} ${baseTheme.colors.lightBlack}`,
                            borderRadius: baseTheme.borderRadii.xxxl,
                            width: baseTheme.space.md,
                            height: baseTheme.space.md,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            zIndex: 10,
                          }}
                          // onClick={() => handleDragStart(index)}
                        >
                          {item.position}
                        </div>
                        <div
                          style={{
                            position: 'absolute',
                            top: baseTheme.space.sm,
                            right: baseTheme.space.md,
                            width: baseTheme.space.lg,
                            height: baseTheme.space.lg,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            zIndex: 10,
                          }}
                          // onClick={() => handleDragStart(index)}
                        >
                          {item.is_disabled ? <CloseEye /> : <OpenEye />}
                        </div>
                      </div>
                    )}
                  </File>
                );
              })}
          </div>
        </Col>
      </Row>
      {isVideoOpen && (
        <MediaVideoDrawer
          media={media_gallery}
          refetch={refetch}
          isOpen={isVideoOpen}
          setIsOpen={setIsVideoOpen}
        />
      )}
      {isOpen && media_gallery && (
        <MediaFileDrawer
          refetch={refetch}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
        />
      )}
      {isUpload && (
        <MediaUpload
          media={media_gallery}
          refetch={refetch}
          visible={isUpload}
          setVisible={setIsUpload}
        />
      )}
    </Main>
  );
};

export default Media;
