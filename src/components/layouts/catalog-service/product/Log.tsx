import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../../themes/theme';
import {
  LogTableColumns as columns,
  LogColumns,
} from '../../../table/product/Columns';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { TableContainer, TableHolder } from '../../../UI-components/Table';
import NothingToshow from '../../../UI-components/NothingToShow';
import { DataTable } from '../../../table/product/DataTable';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import { Pagination } from '../../../UI-components/Pagination';
import { Col, Row } from '@zendeskgarden/react-grid';
import { Button } from '../../../UI-components/Button';
import {
  AddIconWhiteBG,
  CrossIcon,
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  RefetchIcon,
  RightArrowIcon,
} from '../../../../utils/icons';
import {
  Dropdown,
  Field as _Field,
  Menu,
  Trigger,
  Item,
} from '@zendeskgarden/react-dropdowns';
import { Span } from '@zendeskgarden/react-typography';
import routes from '../../../../constants/routes';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import constants from '../../../../constants';
import krakendPaths from '../../../../constants/krakendPaths';

interface Change {
  old_value: any;
  new_value: any;
}

interface Changes {
  [key: string]: Change | { [subKey: string]: Change };
}

interface Data {
  id: number;
  created_at: string;
  updated_at: string;
  admin_identifier: string;
  activity_type: string;
  revert: boolean;
  revert_log_id: number | null;
  user_meta_info: string | null;
  old_value: string;
  new_value: string;
  changes: Changes;
}

interface LogResult {
  item_count: number;
  pages_count: number;
  page_no: string;
  page_size: string;
  data: Data[];
}

interface LogProps {
  fetchdata: () => void;
  id: any;
}

const Log: React.FC<LogProps> = ({ fetchdata, id }) => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(20);
  const [logData, setLogData] = useState<Data[]>([]);
  const [count, setCount] = useState(0);
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const addToast = useToast();
  const axios = useAxios();
  const { detectBrowser, contextProdData, isUpdate, isRevert, setIsRevert } =
    useProductContext();

  const [rotated, setRotated] = useState<boolean | undefined>();
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const { data: LogData, refetch } = useQuery({
    queryKey: ['get-product-logs'],
    queryFn: async (): Promise<any> => {
      try {
        if (prodId) {
          const response = await axios.get(
            // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}/activity-logs?page=${page}&size=${size}`,
            `/v1/catalog-admin/products/${prodId}/activity-logs?page=${page}&size=${size}`,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                'x-api-key': `${constants.CATALOG_KEY}`,
                'Content-Type': 'application/json',
                admin_identifier: `${localStorage.getItem('username')}`,
                platform: `${userData.device}`,
                user_agent: `${userData.browser}(${userData.os})`,
              },
            },
          );
          return response;
        }
        return [];
      } catch (error) {
        throw new Error('Failed to fetch data');
      }
    },
    onError: (err) => {
      addToast('error', `Error found in fetching Product Logs: ${err}`);
    },
    onSuccess: (data: any) => {
      setLogData(data.data);
      setCount(data.item_count);
    },
  });
  const table = useReactTable({
    data: logData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    // setPage(1);
    table.setPageSize(pageSize);
  };

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
    table.setPageSize(20);
  }, []);

  useEffect(() => {
    refetch();
  }, [isUpdate]);

  useEffect(() => {
    if (isRevert) {
      refetch();
      fetchdata();
      setIsRevert(false);
    }
  }, [isRevert]);

  useEffect(() => {
    if (id) {
      setProdId(id);
    }
    refetch();
  }, [prodId, id, page]);

  return (
    <div style={{ padding: '10px 10px' }}>
      <TableContainer>
        <TableHolder>
          {count > 0 ? (
            <DataTable table={table} columns={columns} data={logData} />
          ) : (
            <NothingToshow divHeight="55vh" />
          )}
        </TableHolder>
        {count > 0 ? (
          <div style={{ overflowX: 'clip' }}>
            <Row
              style={{
                height: `${baseTheme.components.dimension.width.base * 5}px`,
                marginTop: baseTheme.space.sm,
                backgroundColor: baseTheme.colors.white,
                paddingLeft: baseTheme.space.lg,
                paddingRight: baseTheme.space.lg,
              }}
              justifyContent="center"
              alignItems="center"
            >
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="center">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={page <= 1 ? true : false}
                    onClick={() => {
                      setPage(page - 1);
                    }}
                  >
                    <Button.StartIcon>
                      <LeftArrowIcon />
                    </Button.StartIcon>
                    Previous
                  </Button>
                </Row>
              </Col>
              <Col textAlign="center" lg={2} md={2}>
                <Dropdown
                  onSelect={(item) => handleRowPerPage(item)}
                  onStateChange={(options) =>
                    Object.hasOwn(options, 'isOpen') &&
                    setRotated(options.isOpen)
                  }
                >
                  <Trigger>
                    <Button size="medium" isAction>
                      Row Per Page:
                      <Span style={{ paddingLeft: baseTheme.space.sm }}>
                        {table.getState().pagination.pageSize}
                      </Span>
                      <Button.EndIcon
                        isRotated={rotated}
                        style={{ marginLeft: 0 }}
                      >
                        <DownIcon />
                      </Button.EndIcon>
                    </Button>
                  </Trigger>
                  <Menu>
                    <Item value={20}>20</Item>
                    <Item value={50}>50</Item>
                    <Item value={100}>100</Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col lg={5} md={5}>
                <Row justifyContent="center" alignItems="center">
                  <Pagination
                    color={baseTheme.colors.deepBlue}
                    totalPages={Math.ceil(
                      count / table.getState().pagination.pageSize,
                    )}
                    pagePadding={2}
                    currentPage={page}
                    onChange={(e) => {
                      // setFilters((prev) => ({ ...prev, page: e }));
                      setPage(e);
                    }}
                  />
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="end">
                  <Button size="medium" isAction style={{ cursor: 'default' }}>
                    {(page - 1) * table.getState().pagination.pageSize + 1}-
                    {count < page * table.getState().pagination.pageSize
                      ? count
                      : page * table.getState().pagination.pageSize}{' '}
                    of {count}
                  </Button>
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="end" alignItems="end">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={
                      page >=
                      Math.ceil(count / table.getState().pagination.pageSize)
                        ? true
                        : false
                    }
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                    <Button.EndIcon>
                      <RightArrowIcon />
                    </Button.EndIcon>
                  </Button>
                </Row>
              </Col>
            </Row>
          </div>
        ) : (
          <>{''}</>
        )}
      </TableContainer>
    </div>
  );
};

export default Log;
