import { Label as _Label } from '@zendeskgarden/react-forms';
import { Col, Row as _Row } from '@zendeskgarden/react-grid';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Button } from '../../../UI-components/Button';
import {
  DollarIcon,
  DollarOnIcon,
  GeneralIcon,
  GeneralOnIcon,
  ShoppingIcon,
  ShoppingOnIcon,
  TruckIcon,
  TruckOnIcon,
} from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import General from '../productInformation/General';
import Shipping from '../productInformation/Shipping';
import Pricing from '../productInformation/Pricing';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import Inventory from '../productInformation/Inventory';
import AdvancePricing from '../productInformation/AdvancePricing';

const Main = styled.div`
  display: flex;
  gap: 20px;
  height: 100%;
  background-color: #fff;
`;
const TabContainer = styled.div`
  display: flex;
  flex-direction: column;
  border-right: 1px solid #bcbcbc;
  width: 300px;
  margin-top: 20px;
`;
const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;
interface TabButtonProps {
  active: boolean;
}

const TabButton = styled.button<TabButtonProps>`
  padding: 10px 20px;
  background-color: #fff;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: start;
  align-items: center;
`;

const IconButton = styled(Button)<TabButtonProps>``;

const ContentContainer = styled.div`
  margin: 20px 20px;
  width: 100%;
`;

interface TabContentProps {
  active: boolean;
}

const TabContent = styled.div<TabContentProps>`
  display: ${(props) => (props.active ? 'block' : 'none')};
  width: 100%;
`;

const ProductInformation = ({ id }: { id: any }) => {
  const [activeTab, setActiveTab] = useState<string>('tab1');

  const handleTabClick: React.MouseEventHandler<HTMLButtonElement> = (
    event,
  ) => {
    setActiveTab(event.currentTarget.id);
  };

  useEffect(() => {
    setTimeout(() => {
      baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
    }, 1000);
  }, []);

  return (
    <div style={{ backgroundColor: '#fff', height: '100%' }}>
      <Main>
        <TabContainer>
          <TabButton
            id="tab1"
            active={activeTab === 'tab1'}
            onClick={handleTabClick}
          >
            <IconButton.StartIcon>
              {activeTab === 'tab1' ? <GeneralOnIcon /> : <GeneralIcon />}
            </IconButton.StartIcon>
            General
          </TabButton>
          <TabButton
            id="tab2"
            active={activeTab === 'tab2'}
            onClick={handleTabClick}
          >
            <IconButton.StartIcon>
              {activeTab === 'tab2' ? <TruckOnIcon /> : <TruckIcon />}
            </IconButton.StartIcon>
            Shipping
          </TabButton>
          <TabButton
            id="tab3"
            active={activeTab === 'tab3'}
            onClick={handleTabClick}
          >
            <IconButton.StartIcon>
              {activeTab === 'tab3' ? <DollarOnIcon /> : <DollarIcon />}
            </IconButton.StartIcon>
            Pricing
          </TabButton>
          {id && (
            <TabButton
              id="tab4"
              active={activeTab === 'tab4'}
              onClick={handleTabClick}
            >
              <IconButton.StartIcon>
                {activeTab === 'tab4' ? <DollarOnIcon /> : <DollarIcon />}
              </IconButton.StartIcon>
              Advanced Pricing
            </TabButton>
          )}
          <TabButton
            id="tab5"
            active={activeTab === 'tab5'}
            onClick={handleTabClick}
          >
            <IconButton.StartIcon>
              {activeTab === 'tab5' ? <ShoppingOnIcon /> : <ShoppingIcon />}
            </IconButton.StartIcon>
            Inventory
          </TabButton>
        </TabContainer>
        <ContentContainer>
          <TabContent active={activeTab === 'tab1'}>
            <>
              <General id={id} />
            </>
          </TabContent>
          <TabContent active={activeTab === 'tab2'}>
            <>
              <Shipping id={id} />
            </>
          </TabContent>
          <TabContent active={activeTab === 'tab3'}>
            <>
              <Pricing id={id} />
            </>
          </TabContent>
          {/* Only show Advanced Pricing content when id exists */}
          {id && (
            <TabContent active={activeTab === 'tab4'}>
              <>
                <AdvancePricing id={id} />
              </>
            </TabContent>
          )}
          <TabContent active={activeTab === 'tab5'}>
            <>
              <Inventory id={id} />
            </>
          </TabContent>
        </ContentContainer>
      </Main>
    </div>
  );
};

export default ProductInformation;
