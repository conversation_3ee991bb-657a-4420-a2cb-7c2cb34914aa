import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import styled from 'styled-components';
import {
  Field as SField,
  Label,
  InputGroup,
  Input,
  Hint,
  Message,
} from '@zendeskgarden/react-forms';
import { baseTheme, colors } from '../../../themes/theme';
import { SearchIcon } from '../../../utils/icons';
import ProductTable from '../../table/new-order/ProductTable';
import { Accordion as _Accordion } from '../../UI-components/Accordion';
import GeneralAccordion from '../../accordion/GeneralAccordion';
import AddressDetailAccordion from '../../accordion/new-order/AddressDetailAccordion';
import PaymentMethodAccordion from '../../accordion/new-order/PaymentMethodAccordion';
import ShippingMethodAccordion from '../../accordion/new-order/ShippingMethodAccordion';
import { Card } from '../../UI-components/Card';
import { LG, SmallHeading, XMD } from '../../UI-components/Typography';
import SearchProductModal from '../../modal/new-order/SearchProductModal';
import { useNewOrderContext } from '../../../pages/order/NewOrderContext';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useCommonAxios';
import { useParams } from 'react-router-dom';
import useToast from '../../../hooks/useToast';
import { Button } from '../../UI-components/Button';
import { Span } from '@zendeskgarden/react-typography';
import { Skeleton, Spinner } from '@zendeskgarden/react-loaders';
import CustomerContextProvider from '../../../pages/customer/CustomerContext';
import { useQuery as useApolloQuery } from '@apollo/client';
import { GetMagentoOrdersDocument } from '../../../gql/graphql';
import krakendPaths from '../../../constants/krakendPaths';

interface RowData {
  quantity: number;
  sku: string;
}

interface CartBody {
  cart_id: string;
  cart_items: { data: RowData }[];
}

const Spacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.sm};
`;

const SmallSpacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.xsm};
`;

const Accordion = styled(_Accordion)`
  margin-top: 0;
`;

const OrderProduct = () => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const [clickedProduct, setClickedProduct] = useState<any>();
  const [customRewardPoints, setCustomRewardPoints] = useState<number>(0);

  // Handle reward points input with validation
  const handleRewardPointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const value = parseInt(inputValue, 10);

    // Handle empty input
    if (inputValue === '' || isNaN(value)) {
      setCustomRewardPoints(0);
      setRewardError(undefined);
      return;
    }

    setCustomRewardPoints(value);

    // Clear error when user starts typing
    if (rewardError) {
      setRewardError(undefined);
    }

    // Real-time validation
    if (value > (applicableRewards?.max_applied_points || 0)) {
      setRewardError(
        `Maximum allowed is ${applicableRewards?.max_applied_points} points.`,
      );
    } else if (value < 0) {
      setRewardError('Reward points cannot be negative.');
    }
  };
  const axios = useAxios();
  const addToast = useToast();
  const [totalDiscount, setTotalDiscount] = useState<number>(0);

  const { customerId } = useParams();

  const [coupontDiscount, setCouponDiscount] = useState<number | undefined>(
    undefined,
  );

  const {
    cartId,
    shippingData,
    isRewardsLoading,
    applicableRewards,
    paymentInfo,
    refetchProductData,
    paymentInfoLoading,
    paymentRefetching,
    setPaymentMethod,
    refecthPaymentInfo,
    availableCartData,
    paymentFetching,
  } = useNewOrderContext();

  const [productInfoArray, setProductInfoArray] = useState<any[] | undefined>(
    undefined,
  );

  const currentReturnOrderId = sessionStorage.getItem('orderId');
  // No change in below
  const {
    loading: queryLoading,
    data: queryData,
    refetch: refetchSku,
  } = useApolloQuery(GetMagentoOrdersDocument, {
    fetchPolicy: 'network-only',
    variables: {
      order_id: currentReturnOrderId,
    },
    skip: currentReturnOrderId === undefined || currentReturnOrderId === null,
  });

  console.log();

  const FetchGetMagento = (queryData: any) => {
    // console.log('getmagentoorders: ',queryData)
    const items = queryData?.getMagentoOrders?.result?.[0]?.items;
    if (items && items.length > 0) {
      console.log('Items', items);
      const productInfo = items.map((item: any) => ({
        sku: item.sku,
        qty: item.qty_ordered,
      }));
      // console.log('productInfo: ', productInfo);
      setProductInfoArray(productInfo);
    } else {
      // console.log('No items found in the order.');
    }
  };

  useEffect(() => {
    // console.log('running....');
    if (currentReturnOrderId) {
      refetchSku();
    }

    if (productInfoArray !== undefined && productInfoArray.length > 0) {
      const cartItems = productInfoArray.map((product) => ({
        data: {
          quantity: product.qty,
          sku: product.sku,
        },
      }));

      const CartBody = {
        cart_id: cartId,
        cart_items: cartItems,
      };
      mutateCart(CartBody);
    }
  }, [productInfoArray, cartId]);

  useEffect(() => {
    // console.log('queryLoading: ', queryLoading )
  }, [queryLoading, cartId]);

  useEffect(() => {
    FetchGetMagento(queryData);
  }, [queryData]);

  const { mutate: mutateCart, isLoading: isAdding } = useMutation(
    async (cartBody: any) => {
      const response = await axios.post(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/items`,
        { ...cartBody },
        {
          headers: {
            Authorization: localStorage.getItem('api-token'),
          },
        },
      );
      return response;
    },
    {
      onError: (err) => {
        addToast('error', `Error occured in adding to cart `);
        console.log('Error occured in adding to cart', err);
      },
      onSuccess: (data) => {
        refetchProductData();
        addToast('success', 'Item added successfully');
        sessionStorage.clear();
        close();
      },
    },
  );

  useEffect(() => {
    if (availableCartData) {
      const couponDiscountObj: any = availableCartData?.prices?.discounts.find(
        (item: any) => item?.label === 'coupon_discount',
      );
      setCouponDiscount(couponDiscountObj?.amount?.value);
    }
  }, [availableCartData]);

  useEffect(() => {
    setTimeout(() => {
      baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
    }, 700);
  }, []);

  useEffect(() => {
    if (shippingData) {
      let totalDiscount = 0;

      shippingData.forEach((row) => {
        const discountObj = row?.prices?.discounts.find(
          (item: any) => item.label === 'Custom discount',
        );

        const discount = parseFloat(discountObj?.amount?.value);

        totalDiscount += discount;
      });

      setTotalDiscount(totalDiscount);
    }
  }, [shippingData]);

  const [searchProduct, setSearchProduct] = useState(false);

  const handleSearchButtonClick = () => {
    setSearchProduct(true);
  };

  const [coupon, setCoupon] = useState<string>('');
  const [couponActive, setCouponActive] = useState<boolean>(false);

  const { mutate, isLoading: isApplying } = useMutation(
    async (couponBody: any) => {
      const response = await axios.put(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/apply-coupon`,
        { ...couponBody },
        {
          headers: {
            Authorization: localStorage.getItem('api-token'),
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        setError(err.message);
        addToast('error', `${err.message}`);
      },
      onSuccess: (data: any) => {
        addToast('success', 'Coupon applied');
        setCouponActive(true);
        setError(undefined);
        refetchProductData();
      },
    },
  );

  const { mutate: mutateCoupon, isLoading } = useMutation(
    async (couponBody: any) => {
      const response = await axios.put(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/remove-coupon`,
        { ...couponBody },
        {
          headers: {
            Authorization: localStorage.getItem('api-token'),
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', err.message);
        setError(err.message);
      },
      onSuccess: (data: any) => {
        setCouponActive(false);
        setCoupon('');
        refetchProductData();
        setError(undefined);
        addToast('success', 'Coupon removed');
      },
    },
  );

  const { mutate: applyRewardCoins, isLoading: isApplyingReward } = useMutation(
    async (reward: number) => {
      const response = await axios.post(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/apply-reward`,
        { reward_points: reward },
        {
          headers: {
            Authorization: localStorage.getItem('api-token'),
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', err.message);
      },
      onSuccess: (data: any) => {
        refetchProductData();
        setRewardError(undefined); // Clear any reward errors on success
      },
    },
  );

  const [error, setError] = useState<string | undefined>();
  const [rewardError, setRewardError] = useState<string | undefined>();

  const validateCoupon = () => {
    if (coupon != '' && coupon != undefined) {
      const couponBody = {
        cart_id: cartId,
        coupon_code: coupon,
      };
      mutate(couponBody);
    }
  };

  const handleRemoveCoupon = () => {
    const cartBody = {
      cart_id: cartId,
    };
    mutateCoupon(cartBody);
  };

  const [checked, setIsChecked] = useState<boolean>(false);
  const [baseCurrency, setCurrency] = useState();

  useEffect(() => {
    if (paymentInfo) {
      setCurrency(paymentInfo?.totals?.base_currency_code);
      if (
        paymentInfo.totals.total_segments[3]?.value != null &&
        paymentInfo.totals.total_segments[3]?.value != 0
      ) {
        setIsChecked(true);
      }

      if (
        paymentInfo?.totals?.coupon_code != null &&
        paymentInfo?.totals?.coupon_code != ''
      ) {
        setCouponActive(true);
        setCoupon(paymentInfo?.totals?.coupon_code);
      }

      if (paymentInfo?.payment_methods.length != 0) {
        paymentInfo?.payment_methods.map((item: any) => {
          setPaymentMethod(item.code);
        });
      }
    }
  }, [paymentInfo]);

  const applyRewards = (customPoints: number) => {
    // Clear any previous reward errors
    setRewardError(undefined);

    // Validate if points exceed maximum allowed
    if (customPoints > (applicableRewards?.max_applied_points || 0)) {
      const errorMessage = `Cannot apply ${customPoints} points. Maximum allowed is ${applicableRewards?.max_applied_points} points.`;
      setRewardError(errorMessage);
      addToast('error', errorMessage);
      return;
    }

    // Validate if points are negative
    if (customPoints < 0) {
      const errorMessage = 'Reward points cannot be negative.';
      setRewardError(errorMessage);
      addToast('error', errorMessage);
      return;
    }

    // Apply the reward points if validation passes
    if (customPoints > 0) {
      applyRewardCoins(customPoints);
    } else {
      applyRewardCoins(0);
    }
  };

  return (
    <>
      <Spacer />
      <Row justifyContent="center">
        <Col size={11.5}>
          <Row justifyContent="start">
            <Col size={5}>
              <Button
                isPrimary
                onClick={handleSearchButtonClick}
                size="large"

                // style={{
                //   width: '100%',
                //   display: 'flex',
                //   alignItems: 'center',
                //   justifyContent: 'space-between',
                //   padding: `${baseTheme.space.sm} ${baseTheme.space.md}`,
                // }}
              >
                {/* <span> */}
                Add Products
                {/* </span> */}
                {/* <SearchIcon /> */}
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>
      <SmallSpacer />
      <Row justifyContent="center">
        <Col size={11.5}>
          <ProductTable />
        </Col>
      </Row>
      <Row>
        <Col size={11.6}>
          <Row alignItems="center">
            <Col offset={0.3} size={3.5}>
              <SField>
                <InputGroup>
                  <Input
                    onChange={(e: any) => {
                      const val: string = e.target.value;
                      setCoupon(val.toUpperCase());
                    }}
                    value={coupon}
                    placeholder="e.g. DENT100"
                    disabled={couponActive}
                  />
                  <Button
                    onClick={() => {
                      if (couponActive) {
                        addToast('error', 'Coupon is already active !!');
                        return;
                      }
                      validateCoupon();
                    }}
                    isPrimary
                    focusInset
                    isNeutral
                  >
                    {isApplying ? <Spinner /> : ` Apply coupon`}
                  </Button>
                </InputGroup>
              </SField>
            </Col>
            <Col size={4}>
              {/* {error && <Message validation="error">{error}</Message>} */}
            </Col>
            <Col>
              <Row alignItems="center">
                <Col size={12}>
                  {isRewardsLoading ? (
                    <>
                      <Skeleton
                        style={{
                          width: '100%',
                          height: baseTheme.components.dimension.height.md,
                        }}
                      />
                    </>
                  ) : (
                    <>
                      <Card style={{ padding: baseTheme.space.md }} bg="white">
                        <Col size={12}>
                          <XMD>
                            <SField>
                              <Label>Apply Rewards</Label>
                              <Hint style={{ marginTop: baseTheme.space.xxs }}>
                                <Span>
                                  (You can apply upto{' '}
                                  {applicableRewards?.max_applied_points} points
                                  for this order)
                                </Span>
                              </Hint>
                              <Row>
                                <Col
                                  style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    gap: '10px',
                                  }}
                                >
                                  <Input
                                    type="number"
                                    placeholder="Enter custom reward points"
                                    onChange={handleRewardPointsChange}
                                    value={customRewardPoints || ''}
                                    validation={
                                      rewardError ? 'error' : undefined
                                    }
                                    min="0"
                                    max={
                                      applicableRewards?.max_applied_points || 0
                                    }
                                  />
                                  <Button
                                    onClick={() =>
                                      applyRewards(customRewardPoints)
                                    }
                                    isPrimary
                                    disabled={!!rewardError || isApplyingReward}
                                  >
                                    {isApplyingReward ? <Spinner /> : 'Apply'}
                                  </Button>
                                </Col>
                              </Row>
                              {rewardError && (
                                <Row>
                                  <Col>
                                    <Message validation="error">
                                      {rewardError}
                                    </Message>
                                  </Col>
                                </Row>
                              )}
                            </SField>
                          </XMD>
                        </Col>
                      </Card>
                      {checked && customRewardPoints > 0 && (
                        <Row justifyContent="center">
                          <Col size={12}>
                            <XMD>
                              {`Custom Reward Points Applied: ${customRewardPoints}`}
                            </XMD>
                          </Col>
                        </Row>
                      )}
                    </>
                  )}
                </Col>
              </Row>
            </Col>
          </Row>
          <Spacer />
        </Col>
      </Row>
      <Row justifyContent="center" alignItems="center">
        <Col size={11.5}>
          <Row>
            <Col size={7.5}>
              <Accordion
                level={4}
                isBare
                isAnimated
                expandedSections={expandedSections}
                onChange={(index) => {
                  if (expandedSections.includes(index)) {
                    setExpandedSections(
                      expandedSections.filter((n) => n !== index),
                    );
                  } else {
                    setExpandedSections([...expandedSections, index]);
                  }
                }}
              >
                <GeneralAccordion
                  inAction={true}
                  indexing={0}
                  title={'Address Details'}
                  children={
                    <>
                      <CustomerContextProvider>
                        <AddressDetailAccordion
                          isOpen={expandedSections.includes(0)}
                        />
                      </CustomerContextProvider>
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
                <GeneralAccordion
                  inAction={true}
                  indexing={1}
                  title={'Payment Method'}
                  children={
                    <PaymentMethodAccordion
                      isOpen={expandedSections.includes(0)}
                    />
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
                <GeneralAccordion
                  inAction={true}
                  indexing={2}
                  title={'Shipping Method'}
                  children={<ShippingMethodAccordion />}
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </Accordion>
            </Col>
            <Col size={4.5}>
              <Card bg="white" style={{ width: '100%' }}>
                <Row style={{ height: '100%' }} justifyContent="center">
                  <Col size={10}>
                    <Spacer />
                    <Row>
                      <SmallHeading isBold>Order Summary</SmallHeading>
                    </Row>
                    <SmallSpacer />
                    <SmallSpacer />
                    {paymentInfoLoading ||
                    paymentFetching ||
                    paymentRefetching ? (
                      <>
                        <Skeleton
                          style={{
                            width: '100%',
                            height:
                              baseTheme.components.dimension.height.base400,
                          }}
                        />
                      </>
                    ) : (
                      <>
                        {!paymentInfoLoading &&
                          paymentInfo &&
                          paymentInfo?.totals?.total_segments
                            .sort((a: any, b: any) => {
                              if (a.title === 'Grand Total') return 1;
                              if (b.title === 'Grand Total') return -1;
                              return 0;
                            })
                            .map((segment: any, index: number) => (
                              <>
                                {couponActive && index == 5 && (
                                  <>
                                    <Row justifyContent="between">
                                      <Col size={9}>
                                        <XMD>
                                          {`Coupon code activated ${coupon}`}
                                          <Span
                                            onClick={() => {
                                              handleRemoveCoupon();
                                            }}
                                            style={{
                                              cursor: 'pointer',
                                              color: baseTheme.colors.dangerHue,
                                            }}
                                          >
                                            &nbsp;&nbsp;<u>Remove</u>
                                          </Span>
                                        </XMD>
                                      </Col>
                                      <Col>
                                        <XMD>
                                          {baseCurrency} {coupontDiscount}
                                        </XMD>
                                      </Col>
                                    </Row>
                                    <Spacer />
                                  </>
                                )}
                                {index == 5 && (
                                  <>
                                    <Row justifyContent="between">
                                      <Col size={9}>
                                        <XMD>Custom discount</XMD>
                                      </Col>
                                      <Col>
                                        <XMD hue="secondary">
                                          - {baseCurrency} {totalDiscount}
                                        </XMD>
                                      </Col>
                                    </Row>
                                    <Spacer />
                                  </>
                                )}
                                <Row key={index} justifyContent="between">
                                  {segment.code === 'grand_total' ? (
                                    <>
                                      <Col
                                        style={{ marginTop: '28px' }}
                                        size={9}
                                      >
                                        <LG>{segment.title}</LG>
                                      </Col>
                                      <Col style={{ marginTop: '28px' }}>
                                        {paymentInfoLoading ||
                                        isApplyingReward ? (
                                          <LG>
                                            <Skeleton width="100%" />
                                          </LG>
                                        ) : (
                                          <LG>
                                            {baseCurrency} {segment.value}
                                          </LG>
                                        )}
                                      </Col>
                                    </>
                                  ) : (
                                    <>
                                      <Col size={9}>
                                        <XMD>{segment.title}</XMD>
                                      </Col>
                                      <Col>
                                        {paymentInfoLoading ||
                                        isApplyingReward ? (
                                          <XMD>
                                            <Skeleton width="100%" />
                                          </XMD>
                                        ) : (
                                          <XMD>
                                            {baseCurrency} {segment.value}
                                          </XMD>
                                        )}
                                      </Col>
                                    </>
                                  )}
                                </Row>
                                <SmallSpacer />
                              </>
                            ))}
                      </>
                    )}

                    <SmallSpacer />
                    <Spacer />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
      <SmallSpacer />
      <SmallSpacer />
      <SmallSpacer />
      <SmallSpacer />
      {searchProduct && (
        <SearchProductModal
          searched=""
          close={() => {
            setSearchProduct(false);
            setTimeout(() => {
              refecthPaymentInfo();
            }, 1000);
          }}
          setCurrentProduct={setClickedProduct}
        />
      )}
    </>
  );
};

export default OrderProduct;
