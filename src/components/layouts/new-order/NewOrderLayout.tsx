import { useState } from 'react';
import { Card } from '../../UI-components/Card';
import { baseTheme } from '../../../themes/theme';
import { Col, Row } from '../../UI-components/Grid';
import { Button, Buttons } from '../../UI-components/Button';
import { useNavigate } from 'react-router-dom';
import { LeftArrowIcon } from '../../../utils/icons';
import { pageRoutes } from '../../navigation/RouteConfig';
import styled from 'styled-components';
import OrderProduct from './OrderProduct';
import { useNewOrderContext } from '../../../pages/order/NewOrderContext';
import { useParams } from 'react-router-dom';
import useToast from '../../../hooks/useToast';
import CreateOrderModal from '../../modal/new-order/CreateOrderModal';

const OrderContainer = styled.div`
  height: 80vh;
  width: 100%;
`;

const SRow = styled(Row)`
  height: 100%;
`;

const NewOrderLayout = () => {
  const navigate = useNavigate();

  const {
    setCartId,
    cartId,
    paymentMethod,
    paymentReference,
    setOrderData,
    isOrderHaveAddress,
    shippingData,
  } = useNewOrderContext();

  const { customerId } = useParams();
  const addToast = useToast();

  const [createOrderVisible, setCreateOrderVisible] = useState<boolean>(false);

  const handleCreate = () => {
    if (customerId) {
      const orderData = {
        cart_id: cartId,
        customer_id: parseInt(customerId),
        payment_method: paymentMethod,
        payment_reference: paymentReference,
      };

      if (orderData.payment_method == 'prepaid') {
        if (
          orderData.payment_reference == undefined ||
          orderData.payment_reference == ''
        ) {
          addToast('error', 'Please enter payment reference id !!');
          return;
        }
      }

      if (!isOrderHaveAddress) {
        addToast('error', 'Please select an address !!');
        return;
      }

      setOrderData(orderData);

      setCreateOrderVisible(true);
    }
  };

  return (
    <>
      <Card bg="white" style={{ border: 'none', padding: baseTheme.space.sm }}>
        <Row alignItems="center">
          <Col offset={9.5} size={1}>
            <Buttons
              onClick={() => {
                navigate(`${pageRoutes['GO_TO_ORDERS']}`);
                setCartId(undefined);
              }}
              isAction
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
          </Col>
          <Col size={1}>
            <Button
              onClick={() => {
                handleCreate();
              }}
              isPrimary
              disabled={shippingData?.length === 0}
            >
              Create Order
            </Button>
          </Col>
        </Row>
      </Card>
      <OrderContainer>
        <SRow mt="md">
          <Col>
            <SRow justifyContent="center">
              <Col size={12}>
                <Card bg="white" style={{ border: 'none', height: '100%' }}>
                  <OrderProduct />
                </Card>
              </Col>
            </SRow>
          </Col>
        </SRow>
      </OrderContainer>

      {createOrderVisible && (
        <>
          <CreateOrderModal close={() => setCreateOrderVisible(false)} />
        </>
      )}
    </>
  );
};

export default NewOrderLayout;
