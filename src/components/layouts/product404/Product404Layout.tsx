import { useState } from 'react';
import Product404Table from '../../table/product404/Product404Table';

const Product404Layout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: any[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>();

    return (
        <>
            <Product404Table
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default Product404Layout;
