import { useEffect, useState } from 'react';
import ExtraMaterialReceivedTable from '../../table/new-return-modules/ExtraMaterialReceivedTable';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import LazyLoading from '../../UI-components/LazyLoading';
import NothingToshow from '../../UI-components/NothingToShow';

export type ExtraMaterialReceivedFilters = {
  return_id?: string;
  status?: string[];
  current_status?: string[];
  date_from?: string;
  date_to?: string;
  page: number;
  size: number;
};

export enum ExtraMaterailReceivedCurrentStatus {
  INVENTORY = 'inventory',
  SERVICING = 'repair_dk',
  REDISPATCH = 'redispatch',
  TO_HR = 'to_hr',
}

const ExtraMterialReceivedLayout = () => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  const [filters, setFilters] = useState<ExtraMaterialReceivedFilters>({
    page: 1,
    size: 20,
  });
  const addToast = useToast();
  const axios = useAxios();

  const { data, isLoading, refetch, isFetching, isRefetching } = useQuery({
    queryKey: ['extraMaterialReceived'],
    queryFn: async () => {
      const response = await axios.get(
        `${constants.RETURN_URL}/api/v1/admin/extra-material-received`,
        {
          params: filters,
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
          },
        },
      );

      return response;
    },
    onSuccess: (data: any) => {},
    onError: (error: any) => {
      addToast('error', error?.message || 'Error occured !!');
    },
  });

  const handleReset = () => {
    setFilters({
      page: 1,
      size: 20,
    });
    setSearchContent(undefined);
  };

  const handleSearch = () => {
    setFilters({
      ...filters,
      page: 1,
      size: 20,
      return_id: searchContent?.trim(),
    });
  };

  useEffect(() => {
    refetch();
  }, [filters]);

  if (isLoading || isFetching || isRefetching) {
    return <LazyLoading />;
  }
  if (data)
    return (
      <ExtraMaterialReceivedTable
        data={data?.data || []}
        count={data?.total || 1}
        totalPages={data?.totalPages || 1}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
        handleReset={handleReset}
        handleSearch={handleSearch}
      />
    );
  else return <NothingToshow />;
};

export default ExtraMterialReceivedLayout;
