import {  useState } from 'react';
import ReturnDTOTable from '../../table/new-return-modules/ReturnDTO';

const ReturnDTOLayout = () => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <ReturnDTOTable
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        // data={returnDTOtestData}
      />
    </>
  );
};

export default ReturnDTOLayout;
