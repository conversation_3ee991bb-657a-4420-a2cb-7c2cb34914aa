import React, { useEffect, useState } from 'react';
import GenericAccordion from '../../accordion/GenericAccordion';
import ReasonTable from '../../table/new-return-modules/ReasonTable';
import ActionReimbursement from '../../table/new-return-modules/ActionReimbursement';
import TransporterSelection from '../../accordion/new-return-module/return-config/TransportSelection';
import DefaultDelivery from '../../accordion/new-return-module/return-config/DefaultDelivery';
import { Accordion } from '../../UI-components/Accordion';
import StatusTable from '../../table/new-return-modules/StatusTable';
import TagTable from '../../table/new-return-modules/TagTable';
import SLATable from '../../table/new-return-modules/SLATable';
import InspectionRemarkTable from '../../table/new-return-modules/InspectionRemarkTable';
import ResolutionTable from '../../table/new-return-modules/ResolutionTable';
import ReturnDTOStatusTable from '../../table/new-return-modules/DTOStatusTable';
import AssigneeTable from '../../table/new-return-modules/AssigneeTable';
import CustomerRemarkTable from '../../table/new-return-modules/CustomerRemark';
import AdminRemarkTable from '../../table/new-return-modules/AdminRemarkTable';
import ReturnDTOStageTable from '../../table/new-return-modules/ReturnDTOStage';
import AdminActionReasonTable from '../../table/new-return-modules/AdminActionReasonTable';

export const ReturnConfigLayout = () => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  return (
    <>
      <div style={{ padding: '20px', marginBottom: '20px' }}>
        <Accordion
          level={4}
          isBare
          isAnimated
          expandedSections={expandedSections}
          onChange={(index) => {
            if (expandedSections.includes(index)) {
              setExpandedSections(expandedSections.filter((n) => n !== index));
            } else {
              setExpandedSections([...expandedSections, index]);
            }
          }}
        >
          <GenericAccordion
            indexing={0}
            title="Transporter"
            children={<TransporterSelection />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={1}
            title="Delivery Location"
            children={<DefaultDelivery />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={2}
            title="Action Reimbursement"
            children={<ActionReimbursement />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={3}
            title="Reason"
            children={<ReasonTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={4}
            title="Status"
            children={<StatusTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={5}
            title="Tag"
            children={<TagTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={6}
            title="SLA Hours"
            children={<SLATable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={7}
            title="Inspection Remark"
            children={<InspectionRemarkTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          {/* <GenericAccordion
            indexing={8}
            title="Resolution"
            children={<ResolutionTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          /> */}
          <GenericAccordion
            indexing={8}
            title="Return DTO Status"
            children={<ReturnDTOStatusTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={9}
            title="Return Assignee"
            children={<AssigneeTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          {/* <GenericAccordion
            indexing={11}
            title="Customer Remark"
            children={<CustomerRemarkTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
          <GenericAccordion
            indexing={12}
            title="Admin Remark"
            children={<AdminRemarkTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          /> */}

          {/* <GenericAccordion
            indexing={13}
            title="Return DTO Stage"
            children={<ReturnDTOStageTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          /> */}
          <GenericAccordion
            indexing={10}
            title="Admin Action Reason"
            children={<AdminActionReasonTable />}
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />
        </Accordion>
      </div>
    </>
  );
};

// export default ReturnConfigLayout;
