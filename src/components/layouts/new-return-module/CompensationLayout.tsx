import { createContext, useContext, useState } from 'react';
import CompensationTable from '../../table/new-return-modules/Compensation';
import { CompensationTableColumn } from '../../table/new-return-modules/columns';
import EditCompensation from '../../modal/new-return-module/compensation/EditCompensation';

export type CompensationContextType = {
  selectedCompensation: CompensationTableColumn | undefined;
  setselectedCompensation: React.Dispatch<
    React.SetStateAction<CompensationTableColumn | undefined>
  >;
};

const CompensationContext = createContext<CompensationContextType | undefined>(
  undefined,
);

const CompensationLayout = () => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  const [selectedCompensation, setselectedCompensation] =
    useState<CompensationTableColumn>();

  return (
    <CompensationContext.Provider
      value={{
        selectedCompensation,
        setselectedCompensation,
      }}
    >
      <CompensationTable
        searchContent={searchContent}
        setSearchContent={setSearchContent}
      />

      {selectedCompensation && (
        <EditCompensation
          close={() => {
            setselectedCompensation(undefined);
          }}
        />
      )}
    </CompensationContext.Provider>
  );
};

export const useCompensation = () => useContext(CompensationContext);

export default CompensationLayout;
