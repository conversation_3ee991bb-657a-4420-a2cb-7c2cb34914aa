import { Button } from '@zendeskgarden/react-buttons';
import { mediaQuery } from '@zendeskgarden/react-theming';
import React, { useEffect, useState } from 'react';
import { baseTheme, colors } from '../../../../themes/theme';
import styled from 'styled-components';
import { useScreenSize } from '../../../../hooks/useScreenSize';
import { Col, Row } from '../../../UI-components/Grid';
import { useNavigate, useParams } from 'react-router-dom';
import General from './General';
import ReturnItem from './ReturnItem';
import Order from './Order';
import ReturnDTO from './ReturnDTO';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import { pageRoutes } from '../../../navigation/RouteConfig';
import { useGetReturnItem } from '../../../../hooks/useReturn';
import { useAuth } from '../../../providers/AuthProvider';

const Tab = styled(Button)<{ isTab: boolean }>`
  border-top: 4px solid ${(p) => (p.isTab ? p.theme.colors.primaryHue : 'none')};
  padding: ${baseTheme.space.md};
  background-color: ${(p) => (p.isTab ? 'white' : colors.tabGrey)};
  ${(p) => mediaQuery('down', 'md', p.theme)} {
    padding: ${baseTheme.space.sm};
    font-size: ${baseTheme.fontSizes.xs};
  }
  color: ${(p) => (p.isTab ? baseTheme.colors.deepBlue : colors.heavyGrey)};
  border-radius: 0;
  font-size: 15px;
  font-weight: 600;
  height: 50px;
  padding: 15px 20px;
`;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  background-color: white;
  margin: 16px;
`;

export default ({
  menu,
  orderId,
}: {
  menu: 'general' | 'returnItem' | 'order' | 'returnDto';
  orderId?: string | null;
}) => {
  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Return Item Requests',
      breadcrumbParent: '',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const navigate = useNavigate();
  const [returnOrder, setReturnOrder] = useState<any[]>([]);
  const { returnId: returnItemID } = useParams();
  const [returnItems, setreturnItems] = useState<any[]>([]);
  const [returnId, setReturnId] = useState<any>(returnItemID);
  const [orderID, setOrderID] = useState<string>();

  const {
    data,
    isLoading: isItemLoading,
    refetch,
  } = useGetReturnItem({ returnId: returnItemID });

  // const {
  //   data: requestDataByOrder,
  //   isLoading: isOrderLoading,
  //   refetch: reloadRequestThroughOrderID,
  // } = useGetReturnRequestByOrderId({ orderId: orderID });

  useEffect(() => {
    if (data && data.return_order) {
      setReturnOrder(data.return_order.items);
      const { order_id } = data.return_order;
      setOrderID(order_id);
      setreturnItems(data?.return_order?.returnItems);
    }
  }, [data]);

  return (
    <>
      <Container>
        <Row>
          <Col>
            <Row>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_NEW_RETURN_ITEM_GENERAL']}/${returnId}`,
                  );
                }}
                isTab={menu === 'general'}
                isNeutral={menu !== 'general'}
                isBasic
              >
                General
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_NEW_RETURN_ITEM']}/${returnId}`,
                  );
                }}
                isTab={menu === 'returnItem'}
                isNeutral={menu !== 'returnItem'}
                isBasic
              >
                Return Items
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_NEW_RETURN_ITEM_ORDER']}/${returnId}`,
                  );
                }}
                isTab={menu === 'order'}
                isNeutral={menu !== 'order'}
                isBasic
              >
                Order
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_NEW_RETURN_ITEM_DTO']}/${returnId}`,
                  );
                }}
                isTab={menu === 'returnDto'}
                isNeutral={menu !== 'returnDto'}
                isBasic
              >
                Return DTO
              </Tab>
            </Row>
          </Col>
        </Row>
        {menu === 'general' && (
          <General
            requestDataByOrder={data?.duplicate_returns}
            returnItemRequests={data?.return_order}
            reloadRequestThroughOrderID={refetch}
            isOrderLoading={isItemLoading}
          />
        )}
        {menu === 'returnItem' && (
          <ReturnItem
            items={returnItems}
            orderID={orderID}
            isOrderLoading={isItemLoading}
            refetchReturnItem={refetch}
          />
        )}
        {menu === 'order' && orderID && <Order orderId={orderID} />}
        {menu === 'returnDto' && orderID && (
          <ReturnDTO
            isOrderLoading={isItemLoading}
            returnOrder={returnOrder}
            orderId={orderID}
          />
        )}
      </Container>
    </>
  );
};
