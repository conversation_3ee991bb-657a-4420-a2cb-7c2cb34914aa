import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { IStatusRes } from '../../../../types/new-return-types';

import useAxios from '../../../../hooks/useAxios';
import { useMutation, useQuery } from '@tanstack/react-query';
import routes from '../../../../constants/routes';
import { Col, SROW, Row as _Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import TransportDropdown from '../../../dropdown/new-return-module/TransportDropdown';
import LocationDropdown from '../../../dropdown/new-return-module/LocationDropdown';
import { ColumnDef } from '@tanstack/react-table';
import { OrderContext } from '../../../../hooks/useContext';
import { _TableContainer } from '../../../UI-components/Table';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { DataTable } from '../../../table/new-return-modules/DataTable';
import AddCustomerRemark from '../../../modal/new-return-module/return-item-request/AddCustomerRemark';
import AddAdminRemark from '../../../modal/new-return-module/return-item-request/AddAdminRemark';
import { Tag } from '@zendeskgarden/react-tags';
import { Span } from '@zendeskgarden/react-typography';
import { baseTheme, colors } from '../../../../themes/theme';
import ItemsForReturnModal from '../../../modal/new-return-module/return-item-request/ItemsForReturn';
import { Button } from '@zendeskgarden/react-buttons';
import { Checkbox, Field, Label, Textarea } from '@zendeskgarden/react-forms';
import {
  Dropdown,
  Item,
  Menu,
  Select,
  Field as DField,
  Label as DLabel,
} from '@zendeskgarden/react-dropdowns';
import StatusDropdown from '../../../dropdown/new-return-module/StatusDropdown';
import {
  useAction,
  useGetDelivery,
  useGetTransporter,
  useReason,
  useReturnStatus,
} from '../../../../hooks/useReturn';
import ReturnStatusDropdown from '../../../dropdown/new-return-module/return-item/ReturnStatusDropdown';
import ReturnActionDropdown from '../../../dropdown/new-return-module/return-item/ReturnActionDropdown';
import { RowData } from '@tanstack/react-table';
import useToast from '../../../../hooks/useToast';
import LazyLoading from '../../../UI-components/LazyLoading';
import { useParams } from 'react-router-dom';
import { Avatar } from '@zendeskgarden/react-avatars';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import {
  CrossIcon,
  DentalkartIcon,
  RemoveIcon,
  VideoIcon,
} from '../../../../utils/icons';
import {
  Body,
  Footer,
  FooterItem,
  Header,
  Modal,
} from '@zendeskgarden/react-modals';
import Attachments from '../../../uploads/return-module/Attachments';
import { HeaderDiv } from '../../../UI-components/HeaderDiv';
import { convertToSentenceCase } from '../../../../utils/convertToSentenceCase';
import { formatDateIntoDDMMYYYYAndTime } from '../../../../helpers/helper';
import {
  IDeliveryLocation,
  ITransporter,
} from '../../../../types/new-return-types';
import { IReturnAction } from '../../../../types/types';
import constants from '../../../../constants';
import AdminActionDropdown from '../../../dropdown/new-return-module/return-item/ReturnAdminActionDropdown';
import AdminActionReasonDropdown from '../../../dropdown/new-return-module/return-item/ReturnAdminActionReasonDropdown';
import krakendPaths from '../../../../constants/krakendPaths';

interface ApproveReturnItem {
  id: number;
  return_action: IReturnAction | undefined;
  admin_action_code: string | undefined;
  admin_action_reason: string | undefined;
  actioned_qty: number | undefined;
  status: IStatusRes | undefined;
  action_detail: string | undefined;
  sku: string;
  selected: boolean;
}

const Container = styled.div`
  padding: 25px 27px;
`;

const Row = styled(_Row)`
  margin-bottom: 30px;
`;

const SRow = styled(_Row)``;

const TableConatiner = styled(_TableContainer)``;

// declare module '@tanstack/react-table' {
//   interface TableMeta<TData extends RowData> {
//     updateData: (rowIndex: number, columnId: string, value: unknown) => void;
//   }
// }

const isValidDate = (date: any) => {
  return date && !isNaN(new Date(date).getTime());
};
const ReturnItem = ({
  items,
  isOrderLoading,
  orderID,
  refetchReturnItem,
}: {
  items: { [key: string]: any }[];
  isOrderLoading: boolean;
  orderID: string | undefined;
  refetchReturnItem: () => void;
}) => {
  const { returnId: returnItemID } = useParams();
  const [transporters, setTransporters] = useState<ITransporter[]>([]);
  const [locations, setLocations] = useState<IDeliveryLocation[]>([]);
  const [selectedTransporter, setSelectedTrans] = useState<
    ITransporter | undefined
  >();
  const [seletedLocation, setSelectedLocation] = useState<
    IDeliveryLocation | undefined
  >();
  const [returnOrder, setReturnOrder] = useState<any[]>([]);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [visibleSubmitModal, setVisibleSubmitModal] = useState<boolean>(false);
  const [reason, setReason] = useState<any>([]);
  const { reasonResponse } = useReason(true);
  const { actionResponse } = useAction();
  const addToast = useToast();
  const [image, setImage] = useState<string>();
  const [showImage, setShowImage] = useState(false);
  const [addedImage, setaddedImage] = useState({
    showImage: false,
    image: {
      file: '',
      name: '',
      key: '',
    },
  });

  const handleImageClick = (e: any) => {
    setImage(e);
    setShowImage(true);
  };
  // useEffect(() => {
  //   if (items) {
  //     setReturnItems(items);
  //   }
  // }, [items]);
  //API CALLLS

  const { isLoading, data: transporterData, refetch } = useGetTransporter();
  const { data: returnStatus, refetch: returnStatusRefetch } =
    useReturnStatus();
  // const { data: reasonResponse, refetch: returnActionRefetch } =
  //   useReturnAction();
  const {
    data: deliveryData,
    refetch: reloadDelivery,
    isLoading: isDeliveryLoading,
  } = useGetDelivery();
  // const { returnItemsData, refetchReturnItem } = useReturnItems({
  //   id: returnItemID,
  // });

  //API CALLS
  useEffect(() => {
    if (transporterData) {
      setTransporters(transporterData);
      transporterData.map((transporter, i) => {
        transporter.enable &&
          transporter.default &&
          setSelectedTrans(transporter);
      });
    }
    if (deliveryData) {
      setLocations(deliveryData);
      deliveryData.map((location, i) => {
        location.default && location.enable && setSelectedLocation(location);
      });
    }

    // returnItemsData && setReturnOrder(returnItemsData);
  }, [
    transporterData,
    // returnItemsData,
    deliveryData,
  ]);

  const [approvedData, setApprovedData] = React.useState<{
    items: {
      item_id: number;
      sku: string;
      actioned_qty: number;
      status: string;
      action_detail: string;
    }[];
    transporter_code: number | undefined;
    location_code: string | undefined;
    return_id: number;
    acted_by: string;
  }>({
    items: [],
    transporter_code: Number(selectedTransporter?.code),
    location_code: seletedLocation?.code,
    return_id: Number(returnItemID),
    acted_by: localStorage.getItem('username') || 'unknown',
  });

  const clearReturnItems = () => {
    setApprovedData((prevData) => ({
      ...prevData,
      return_items: [],
    }));
    // setReturnItems([]);
  };

  useEffect(() => {
    setApprovedData((prevData) => ({
      ...prevData,
      transporter_code: selectedTransporter?.code,
      location_code: seletedLocation?.code,
    }));
  }, [selectedTransporter, seletedLocation]);

  const [returnItemMap, setreturnItemMap] = useState<{
    [key: number]: ApproveReturnItem;
  }>({});

  const axios = useAxios();
  const { mutate: editReturnItem, isLoading: loading } = useMutation(
    async (body: any) => {
      const response = await axios.patch(
        `${krakendPaths.RETURN_URL}/admin-api/v1/returns/items`,
        {
          ...body,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': constants.RETURN_API_KEY,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', err?.message);
      },
      onSuccess: (data) => {
        addToast('success', 'Updated successfully');
        refetchReturnItem();
        refetch();
      },
    },
  );

  const {
    isLoading: isAdminActionLoading,
    isError,
    error,
    data: adminActions,
    isRefetching,
    isFetching,
    refetch: refetchAdminAction,
  } = useQuery({
    queryKey: ['get-admin-actions'],
    queryFn: async (): Promise<any> => {
      const response: any = await axios.get(
        `${constants.RETURN_URL}/api/v1/admin/admin-actions`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response.adminActions;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
      console.log(err);
    },
    onSuccess: (data) => {},
  });

  const {
    isLoading: isAdminActionReasonsLoading,
    data: adminActionReasons,
    refetch: refetchAdminActionReasons,
  } = useQuery({
    queryKey: ['get-admin-action-reasons'],
    queryFn: async (): Promise<any> => {
      const response: any = await axios.get(
        `${constants.RETURN_URL}/api/v1/admin/admin-action-reasons`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response.adminActionReasons;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
      console.log(err);
    },
    onSuccess: (data) => {},
  });

  const [btnSaveDisable, setButtonSaveDisable] = useState<boolean>(false);

  useEffect(() => {
    const isEmpty = Object.keys(returnItemMap).length === 0;

    const hasSelectedTrue = Object.values(returnItemMap).some(
      (item) => item.selected,
    );

    const shouldProceed = isEmpty || !hasSelectedTrue;
    setButtonSaveDisable(shouldProceed);
  }, [returnItemMap]);

  //   async (approvData: any) => {
  //     const response = await axios.post(
  //       `${routes.FORWARDERS}/return-request-approve`,
  //       approvData,
  //     );
  //     return response;
  //   },
  //   {
  //     onError: (err: ApolloError) => {
  //       console.log(err);
  //       addToast('error', err.message);
  //     },
  //     onSuccess: (data: any) => {
  //       if (data.statusCode === 500) {
  //         addToast('error', data.message);
  //         setVisibleSubmitModal(false);
  //         setRowSelection({});
  //         table.resetRowSelection();
  //         // refetchReturnItem();
  //         clearReturnItems();
  //       } else {
  //         setVisibleSubmitModal(false);
  //         addToast('success', 'Status updated');
  //         setRowSelection({});
  //         table.resetRowSelection();
  //         // refetchReturnItem();
  //         clearReturnItems();
  //       }
  //     },
  //   },
  // );
  const [attachmentsMap, setattachmentsMap] = useState<{
    [key: number]: any[];
  }>({});
  // console.log({ handleItemChecked });
  const returnItemColumn: ColumnDef<any>[] = [
    {
      accessorKey: 'id',
      header: 'Return Item Id',
      // enableHiding: false,
      cell({ row }) {
        const returnItemId = row.getValue('id');

        return <>{returnItemId}</>;
      },
    },
    {
      id: 'select',
      header: 'Select',
      cell: ({ row, table }) => {
        const [isChecked, setIsChecked] = useState(row.getIsSelected());
        const handleCheckboxChange = (event: any) => {
          if (
            row.original.status !== 'pending' &&
            row.original.status !== 'In Progress' &&
            row.original.status !== 'Awaited Information'
          ) {
            const { checked } = event.target;
            setIsChecked(checked);
            row.toggleSelected(checked);
            // addToast(
            //   'warning',
            //   'Cannot edit, action has already been taken on this item',
            // );
            return;
          }

          const { checked } = event.target;
          setIsChecked(checked);
          row.toggleSelected(checked);
          if (checked) {
            setreturnItemMap((map) => ({
              ...map,
              [row.original.id]: {
                ...map[row.original.id],
                id: row.original.id,
                sku: row.original.sku,
                return_action: row.original.action,
                selected: true,
              },
            }));
            // setItemstoAnArray(row.original);
            // setItemstoDefaultArray(row.original);
          } else {
            setreturnItemMap((map) => ({
              ...map,
              [row.original.id]: {
                ...map[row.original.id],
                selected: false,
                return_action: row.original.action,
              },
            }));
          }
        };

        return (
          <>
            <Row justifyContent="center" alignItems="center">
              <Col size={2}>
                <Field style={{ transform: 'translateY(50%)' }}>
                  <Checkbox
                    checked={isChecked}
                    onChange={handleCheckboxChange}
                    aria-label="Select row"
                    defaultChecked={false}
                  >
                    <Label hidden>Accessibly hidden label one</Label>
                  </Checkbox>
                </Field>
              </Col>
            </Row>
          </>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'order_id',
      header: 'Order Id',
      cell: ({ row }) => {
        const orderId = row.getValue('order_id');
        return <>{orderId}</>;
      },
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const productName = row.getValue('name');
        return <>{productName}</>;
      },
    },
    {
      accessorKey: 'sku',
      header: 'SKU',
      cell: ({ row }) => {
        //console.log('Row', row.original);
        const sku = row.getValue('sku');
        return <>{sku}</>;
      },
    },
    {
      accessorKey: 'qty_ordered',
      header: 'Qty Ordered',
      cell: ({ row }) => {
        //console.log('Row', row.original);
        const qty = parseInt(row.getValue('qty_ordered'));
        return <>{isNaN(qty) ? '' : qty}</>;
      },
    },
    {
      accessorKey: 'qty',
      header: 'Qty Requested',
      cell: ({ row }) => {
        //console.log('Row', row.original);
        const qty = parseInt(row.getValue('qty'));
        return <>{qty}</>;
      },
    },
    {
      accessorKey: 'actioned_qty',
      header: 'Actioned qty',
      cell: ({
        getValue,
        row: {
          id: rowId,
          index,
          original,
          getValue: getRowValue,
          getIsSelected,
        },
        column: { id },
      }) => {
        const initialValue = getValue();
        const qty = parseInt(getRowValue('actioned_qty'));
        const numberArray = Array.from(
          { length: qty as number },
          (_, index) => index + 1,
        );
        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );
        const [actionQty, setActionQty] = useState(initialValue);

        const [selectedQty, setSelectedQty] = useState<number | null>(
          actionQty as number | null,
        );

        const handleSelect = (qty: number) => {
          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              actioned_qty: qty,
            },
          }));
          // setSelectedQty(qty);
          // table.options.meta?.updateData(index, id, qty);
          // setReturnItems((prevReturnItems: any[]) => {
          //   const updatedReturnItems = prevReturnItems.map((item: any) => {
          //     if (item.id === original.id) {
          //       return {
          //         ...item,
          //         actioned_qty: qty,
          //       };
          //     }
          //     return item;
          //   });
          //   return updatedReturnItems;
          // });
        };

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        return (
          <>
            <Dropdown
              selectedItem={returnItemMap[original.id]?.actioned_qty}
              onSelect={(item: number) => handleSelect(item)}
              downshiftProps={{
                itemToString: (item: number) => item && item,
              }}
            >
              <DField>
                <DLabel hidden>Action qty</DLabel>
                <Select
                  disabled={
                    !active ||
                    (original?.status !== 'pending' &&
                      original?.status !== 'In Progress' &&
                      original?.status !== 'Awaited Information')
                  }
                >
                  {returnItemMap[original.id]?.actioned_qty
                    ? returnItemMap[original.id]?.actioned_qty
                    : qty
                    ? qty
                    : 'Select Action Quantity'}
                </Select>
              </DField>
              <Menu>
                {(original.status !== 'pending'
                  ? [original?.actioned_qty ?? 0]
                  : numberArray
                ).map((option) => (
                  <Item key={option} value={option}>
                    {option}
                  </Item>
                ))}
              </Menu>
            </Dropdown>
          </>
        );
      },
    },
    {
      accessorKey: 'reason',
      header: 'Reason',
      cell: ({ row }) => {
        //console.log('Row', row.original);
        const item_reason = row.getValue('reason');
        return (
          <>
            {item_reason}
            {reason &&
              reason?.map((ele: any) => {
                if (item_reason === ele.reason) {
                  return <>{`(${ele.code})`}</>;
                }
              })}
          </>
        );
      },
    },
    {
      accessorKey: 'sub_reason',
      header: 'Sub Reason',
      cell: ({ row }) => {
        //console.log('Row', row.original);
        const subReason = row.getValue('sub_reason');
        const item_reason = row.getValue('reason');
        return (
          <>
            {subReason}
            {reason &&
              subReason &&
              reason.filter((el: any) => item_reason === el.reason)[0]?.children
                ?.length > 0 &&
              `(${
                reason
                  .filter((el: any) => item_reason === el.reason)[0]
                  ?.children?.filter((ele: any) => ele.reason === subReason)[0]
                  ?.code
              })`}
          </>
        );
      },
    },
    {
      accessorKey: 'action',
      header: 'Customer Action',
      cell: ({
        getValue,
        row: { index, getIsSelected, original },
        column: { id },
      }) => {
        const initialValue = getValue();
        const actionList = actionResponse;

        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );
        const [activeActon, setActiveAction] = useState<IReturnAction>();

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        const onSelected = (returnAction: IReturnAction) => {
          // setActiveAction(returnAction);
          // setAction(returnAction.action);

          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              return_action: returnAction,
            },
          }));
          // table.options.meta?.updateData(index, id, returnAction.action);
          // setReturnItems((prevReturnItems: any[]) => {
          //   const updatedReturnItems = prevReturnItems.map((item: any) => {
          //     // console.log('original', original.id, 'item id', item.id);
          //     if (item.id === original.id) {
          //       return {
          //         ...item,
          //         action: returnAction.action,
          //       };
          //     }
          //     return item;
          //   });

          //   return updatedReturnItems;
          // });
        };
        return (
          <>
            <ReturnActionDropdown
              setActiveAction={setActiveAction}
              activeAction={activeActon}
              value={
                (returnItemMap[original.id]?.return_action?.action as string) ??
                initialValue
              }
              disable={
                !active ||
                (original?.status !== 'pending' &&
                  original?.status !== 'In Progress' &&
                  original?.status !== 'Awaited Information')
              }
              reasonResponse={actionList}
              onSelect={onSelected}
            />
          </>
        );
      },
    },
    {
      accessorKey: 'itemCustomerRemarks',
      header: 'Customer Remark',
      cell: ({ row }) => {
        const remarks: any[] = row.original.itemCustomerRemarks;
        const Id: any = row.getValue('id');
        const [visibleCustomerRemark, setVisibleCustomerRemark] =
          useState<boolean>(false);
        const handleVisibleRemark = () => {
          setVisibleCustomerRemark(false);
        };
        const [active, setActive] = useState<boolean | undefined>(
          row.getIsSelected(),
        );

        useEffect(() => {
          setActive(row.getIsSelected());
        }, [row.getIsSelected()]);

        return (
          <>
            <SRow>{remarks.slice(-1)[0] && remarks.slice(-1)[0].remarks}</SRow>
            <SRow>
              <Tag
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  active && setVisibleCustomerRemark(true);
                }}
                hue={
                  active
                    ? baseTheme.colors.deepBlue
                    : baseTheme.colors.veryDarkGray
                }
              >
                <Span>Add Remark</Span>
              </Tag>
            </SRow>
            {visibleCustomerRemark && (
              <AddCustomerRemark
                data={remarks}
                setInput={setInput}
                close={handleVisibleRemark}
                refetchItems={refetchReturnItem}
                id={Id}
              />
            )}
          </>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({
        getValue,
        row: { index, getIsSelected, original },
        column: { id },
      }) => {
        const initialValue = getValue();
        const [status, setStatus] = useState(initialValue);
        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );
        const [activeStatus, setActiveStatus] = useState<IStatusRes>();

        const onSelected = (retStatus: IStatusRes) => {
          // setActiveStatus(retStatus);
          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              status: retStatus,
            },
          }));
          // setStatus(retStatus.status);
          // table.options.meta?.updateData(index, id, retStatus.status);
          // setReturnItems((prevReturnItems: any[]) => {
          //   const updatedReturnItems = prevReturnItems.map((item: any) => {
          //     if (item.id === original.id) {
          //       return {
          //         ...item,
          //         status: retStatus.status.toLowerCase(),
          //       };
          //     }
          //     return item;
          //   });
          //   return updatedReturnItems;
          // });
        };

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        return (
          <>
            <ReturnStatusDropdown
              activeStatus={activeStatus}
              setActiveStatus={setActiveStatus}
              value={
                (returnItemMap[original.id]?.status?.status as string) ??
                initialValue
              }
              disable={
                !active ||
                (original?.status !== 'pending' &&
                  original?.status !== 'In Progress' &&
                  original?.status !== 'Awaited Information')
              }
              // disable={false}
              returnStatus={returnStatus}
              onSelect={onSelected}
            />
            {/* {status} */}
          </>
        );
      },
    },
    {
      accessorKey: 'admin_action',
      header: 'Admin Action',
      cell: ({
        getValue,
        row: { index, getIsSelected, original },
        column: { id },
      }) => {
        const initialValue = getValue();

        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );
        const [activeAdminAction, setActiveAdminAction] = useState<any>();

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        const onSelected = (adminAction: any) => {
          setActiveAdminAction(adminAction);
          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              admin_action_code: adminAction.code,
            },
          }));
        };

        return (
          <AdminActionDropdown
            setActiveAction={setActiveAdminAction}
            activeAction={activeAdminAction}
            value={
              (returnItemMap[original.id]?.admin_action_code as string) ??
              initialValue
            }
            disable={
              !active ||
              (original?.status !== 'pending' &&
                original?.status !== 'In Progress' &&
                original?.status !== 'Awaited Information')
            }
            adminActions={adminActions}
            onSelect={onSelected}
          />
        );
      },
    },
    {
      accessorKey: 'admin_action_reason',
      header: 'Admin Action Reason',
      cell: ({
        getValue,
        row: { index, getIsSelected, original },
        column: { id },
      }) => {
        const initialValue = getValue();
        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );
        const [activeAdminActionReason, setActiveAdminActionReason] =
          useState<string>();

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        const onSelected = (selectedReason: string) => {
          setActiveAdminActionReason(selectedReason);
          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              admin_action_reason: selectedReason, // Store the string, not an object
            },
          }));
        };

        return (
          <AdminActionReasonDropdown
            setActiveReason={setActiveAdminActionReason}
            activeReason={activeAdminActionReason}
            value={
              (returnItemMap[original.id]?.admin_action_reason as string) ??
              initialValue
            }
            disable={
              !active ||
              (original?.status !== 'pending' &&
                original?.status !== 'In Progress' &&
                original?.status !== 'Awaited Information')
            }
            reasons={adminActionReasons}
            onSelect={onSelected}
          />
        );
      },
    },

    {
      accessorKey: 'itemAdminRemarks',
      header: 'Admin Remark',
      cell: ({ row, cell: { id } }) => {
        const adminRemark: any[] = row.original.itemAdminRemarks;
        const Id: any = row.getValue('id');
        const [visibleAdminRemark, setVisibleAdminRemark] =
          useState<boolean>(false);
        const [active, setActive] = useState<boolean | undefined>(
          row.getIsSelected(),
        );
        const handleVisibleAdminRemark = () => {
          setVisibleAdminRemark(false);
        };
        useEffect(() => {
          setActive(row.getIsSelected());
        }, [row.getIsSelected()]);

        return (
          <>
            <SRow justifyContent="center">
              {adminRemark.slice(-1)[0] && adminRemark.slice(-1)[0].remarks}
            </SRow>
            <SRow justifyContent="center">
              <Tag
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  active && setVisibleAdminRemark(true);
                }}
                hue={
                  active
                    ? baseTheme.colors.deepBlue
                    : baseTheme.colors.veryDarkGray
                }
              >
                <Span>Add Remark</Span>
              </Tag>
            </SRow>
            {visibleAdminRemark && (
              <AddAdminRemark
                data={adminRemark}
                setInput={setAdminInput}
                close={handleVisibleAdminRemark}
                refetchItems={refetchReturnItem}
                id={Id}
              />
            )}
          </>
        );
      },
    },

    {
      accessorKey: 'action_detail',
      header: 'Action Detail',
      cell: ({
        getValue,
        row: { index, getIsSelected, original },
        column: { id },
      }) => {
        const initialValue = getValue();

        const [value, setValue] = useState(initialValue);

        const [active, setActive] = useState<boolean | undefined>(
          getIsSelected(),
        );

        useEffect(() => {
          if (!initialValue)
            setValue(returnItemMap[original.id]?.action_detail);

          if (
            initialValue &&
            returnItemMap[original.id]?.action_detail &&
            initialValue != returnItemMap[original.id]?.action_detail
          )
            setValue(returnItemMap[original.id]?.action_detail);
        }, [returnItemColumn]);

        useEffect(() => {
          setActive(getIsSelected());
        }, [getIsSelected()]);

        const onBlur = () => {
          setreturnItemMap((map) => ({
            ...map,
            [original.id]: {
              ...map[original.id],
              action_detail: value as string,
            },
          }));
          table.options.meta?.updateData(index, id, value);
          // setReturnItems((prevReturnItems: any[]) => {
          //   const updatedReturnItems = prevReturnItems.map((item: any) => {
          //     if (item.id === original.id) {
          //       return {
          //         ...item,
          //         action_detail: value,
          //       };
          //     }
          //     return item;
          //   });
          //   return updatedReturnItems;
          // });
        };

        return (
          <>
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base150}
            >
              <Field>
                <Textarea
                  disabled={
                    !active ||
                    (original?.status !== 'pending' &&
                      original?.status !== 'In Progress' &&
                      original?.status !== 'Awaited Information')
                  }
                  placeholder="Action Detail"
                  value={value as string}
                  onChange={(e) => {
                    // setreturnItemMap((map) => ({
                    //   ...map,
                    //   [original.id]: {
                    //     ...map[original.id],
                    //     action_detail: e.target.value,
                    //   },
                    // }));
                    setValue(e.target.value);
                  }}
                  onBlur={onBlur}
                />
              </Field>
            </HeaderDiv>
          </>
        );
      },
    },
    {
      accessorKey: 'approved_at',
      header: 'Approved at',
      cell: ({ row }) => {
        const appAt = row.getValue('approved_at');
        return (
          <>
            {isValidDate(appAt) ? (
              formatDateIntoDDMMYYYYAndTime(appAt)
            ) : (
              <Tag>Not Provided</Tag>
            )}
          </>
        );
      },
    },
    {
      accessorKey: 'rejected_at',
      header: 'Rejected at',
      cell: ({ row }) => {
        const rejAt = row.getValue('rejected_at');
        return (
          <>
            {isValidDate(rejAt) ? (
              formatDateIntoDDMMYYYYAndTime(rejAt)
            ) : (
              <Tag>Not Provided</Tag>
            )}
          </>
        );
      },
    },
    {
      accessorKey: 'attachments',
      header: 'Attachments',
      cell: ({ row }) => {
        const att = row.original?.attachments as any[];
        const [attachments, setattachments] = useState(att);
        // const [addedAttachments, setAddedAttachments] = useState<any[]>();

        const removeAttachment = (indexToRemove: number) => {
          const updatedAttachments = attachments.filter(
            (_, index) => index !== indexToRemove,
          );
          setattachments(updatedAttachments);
        };

        const addedRemoveAttachments = (indexToRemove: number) => {
          const updatedAttachments = attachmentsMap[row.original.id].filter(
            (_, index) => index !== indexToRemove,
          );
          setattachmentsMap((prev) => ({
            ...prev,
            [row.original.id]: updatedAttachments,
          }));
          // setAddedAttachments(updatedAttachments);
        };

        return (
          <>
            <Col>
              <Row alignItems="center">
                {orderID && (
                  <>
                    <div
                      style={{
                        marginRight: '20px',
                      }}
                    >
                      <Attachments
                        // setAttachments={setattachments}
                        disabled={!row.getIsSelected()}
                        orderId={orderID}
                        getAttachmentsImages={(attachment, loading) => {
                          setattachments((attch) => [...attch]);
                          setattachmentsMap((prev) => ({
                            ...prev,
                            [row.original.id]: [...attachment],
                          }));
                          // setAddedAttachments((prev) => [
                          //   ...prev,
                          //   ...attachment,
                          // ]);
                        }}
                        hasAttachment={false}
                        isInTable
                        // requestSent={requestSent}
                        // setRequestSent={setRequestSent}
                        // status={returnOrder.status}
                      />
                    </div>
                  </>
                )}
                {attachments &&
                  attachments.map((e: any, index: number) => {
                    const isVideo =
                      e && e.attachment
                        ? e?.attachment?.endsWith('.mp4')
                        : false;

                    return (
                      <>
                        <Tooltip
                          content={isVideo ? 'Play Video' : 'Show Image'}
                        >
                          <>
                            <Avatar
                              key={index}
                              backgroundColor={colors.lightGrey}
                              size="medium"
                              isSystem
                              style={{
                                cursor: 'pointer',
                                margin: baseTheme.space.sm,
                              }}
                              onClick={() => {
                                handleImageClick(e.attachment);
                              }}
                            >
                              {isVideo ? (
                                <VideoIcon
                                  style={{ width: '60%', height: '60%' }}
                                />
                              ) : (
                                <>
                                  <img src={e?.attachment} />
                                </>
                              )}
                            </Avatar>
                          </>
                        </Tooltip>
                      </>
                    );
                  })}
                {attachmentsMap[row.original.id] &&
                  attachmentsMap[row.original.id].map(
                    (e: any, index: number) => {
                      const isVideo =
                        e && e.name ? e?.name?.endsWith('.mp4') : false;

                      return (
                        <>
                          <Tooltip
                            content={isVideo ? 'Play Video' : 'Show Image'}
                          >
                            <>
                              <Avatar
                                key={index}
                                backgroundColor={colors.lightGrey}
                                size="medium"
                                isSystem
                                style={{
                                  cursor: 'pointer',
                                  margin: baseTheme.space.sm,
                                }}
                                onClick={() => {
                                  setaddedImage({
                                    image: e,
                                    showImage: true,
                                  });
                                }}
                              >
                                {isVideo ? (
                                  <VideoIcon
                                    style={{ width: '60%', height: '60%' }}
                                  />
                                ) : (
                                  <>
                                    <img src={e?.key} />
                                  </>
                                )}
                              </Avatar>
                            </>
                          </Tooltip>
                        </>
                      );
                    },
                  )}
              </Row>
            </Col>
          </>
        );
      },
    },
  ];

  const table = useReactTable<any>({
    data: items,
    columns: returnItemColumn,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
    meta: {
      updateData: (rowIndex: any, columnId: any, value: any) => {
        setReturnOrder((old: any) =>
          old.map((row: any, index: any) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex]!,
                [columnId]: value,
              };
            }
            return row;
          }),
        );
      },
      breacheValue: [],
    },
  });

  const [input, setInput] = useState<string>('');
  const [adminInput, setAdminInput] = useState<string>('');

  const handleApplySubmit = () => {
    // const hasNullActionedQty = items.some((item: any) => !item.actioned_qty);
    // if (!hasNullActionedQty) setVisibleSubmitModal(true);
    // else addToast('info', 'Please provide action quantity');

    // const attachmentArray: string[] = addedAttachments.map(
    //   (item) => item.presignedUrl,
    // );

    const itemsArr = Object.values(returnItemMap)
      .filter((i) => i.selected)
      .map((item) => ({
        item_id: item.id,
        actioned_qty: item.actioned_qty,
        status: item.status?.status,
        action_detail: item.action_detail,
        sku: item.sku,
        action: item.return_action?.action?.toLowerCase(),
        admin_action_code: item.admin_action_code,
        admin_action_reason: item.admin_action_reason,
        new_attachments: attachmentsMap[item.id]?.map((at) => at?.presignedUrl),
      }));

    const hasNullActionedQty = itemsArr.some((item: any) => !item.actioned_qty);

    if (hasNullActionedQty) {
      addToast('info', 'Please select the action quantity');
      return;
    }

    setApprovedData((d) => ({
      ...d,
      items: itemsArr as any,
    }));
    setVisibleSubmitModal(true);
  };

  const handleSubmit = () => {
    // console.log('Data', approvedData);
    // const { return_id: extractedReturnId, ...restOfObject } = approvedData;
    // console.log('Remaining object:', restOfObject);
    if (
      approvedData?.items?.find(
        (item: any) => item.status == 'approved' && !item?.admin_action_code,
      )
    ) {
      addToast('warning', 'Please provide admin action while approving return');
      return;
    }
    setButtonSaveDisable(true);
    editReturnItem(approvedData);
    setVisibleSubmitModal(false);
    setTimeout(() => {
      setButtonSaveDisable(false);
    }, 5000);

    // window.location.reload();
    // createReturnApproved(approvedData);
  };

  const disabledColumns = ['Product Id'];
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Rejected at',
    'Approved at',
    'Action Detail',
    'Customer Action',
    'Admin Action',
    'Admin Action Reason',
    'Admin Remark',
    'Customer Remark',
    'Sub Reason',
    'Reason',
    'Status',
    'Actioned qty',
    'Qty Requested',
    'Qty Ordered',
    'SKU',
    'Name',
    'Order Id',
    'Select',
    'Return Item Id',
    'Attachments',
  ]);

  return (
    <>
      {isOrderLoading || isAdminActionLoading || isAdminActionReasonsLoading ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          <Container>
            <Row>
              <Col>
                <TransportDropdown
                  transporters={transporters}
                  selectedItem={selectedTransporter}
                  setSelectedItem={setSelectedTrans}
                />
              </Col>
              <Col>
                <LocationDropdown
                  locations={locations}
                  selectedItem={seletedLocation}
                  setSelectedItem={setSelectedLocation}
                />
              </Col>
            </Row>
            <Row>
              <TableConatiner>
                <DataTable
                  data={items}
                  columns={returnItemColumn}
                  table={table}
                  alreadyEnabledColumn={alreadyEnabledColumn}
                />
              </TableConatiner>
            </Row>
            <Row justifyContent="end">
              <Col size={1.5} offset={10.5}>
                <Button
                  isPrimary
                  onClick={handleApplySubmit}
                  isStretched
                  disabled={loading || btnSaveDisable}
                >
                  Submit
                </Button>
              </Col>
            </Row>
          </Container>

          {showImage && (
            <>
              <Modal>
                <Body>
                  {image?.endsWith('.mp4') && (
                    <video
                      controls
                      style={{
                        width: baseTheme.components.dimension.width.base500,
                        height: `${
                          baseTheme.components.dimension.height.base * 50
                        }px`,
                      }}
                    >
                      <source src={`${image}`} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  )}
                  {(image?.endsWith('.jpg') ||
                    image?.endsWith('.png') ||
                    image?.endsWith('jpeg') ||
                    image?.endsWith('gif')) && (
                    <>
                      <img
                        style={{
                          width: baseTheme.components.dimension.width.base500,
                          height: `${
                            baseTheme.components.dimension.height.base * 50
                          }px`,
                        }}
                        src={`${image}`}
                      />
                    </>
                  )}

                  {image === '' && (
                    <>
                      <DentalkartIcon />
                    </>
                  )}
                </Body>
                <Footer>
                  <FooterItem>
                    <a href={image} target="_blank">
                      <Button style={{ marginRight: '10px' }}>
                        Open in new tab
                      </Button>
                    </a>
                    <Button onClick={() => setShowImage(false)}>Close</Button>
                  </FooterItem>
                </Footer>
              </Modal>
            </>
          )}
          {addedImage.showImage && (
            <>
              <Modal>
                <Body>
                  {addedImage.image &&
                    addedImage.image.name &&
                    addedImage.image?.name?.endsWith('.mp4') && (
                      <video
                        controls
                        style={{
                          width: baseTheme.components.dimension.width.base500,
                          height: `${
                            baseTheme.components.dimension.height.base * 50
                          }px`,
                        }}
                      >
                        <source
                          src={`${addedImage.image.key}`}
                          type="video/mp4"
                        />
                        Your browser does not support the video tag.
                      </video>
                    )}
                  {(addedImage.image.name?.endsWith('.jpg') ||
                    addedImage.image.name?.endsWith('.png') ||
                    addedImage.image.name?.endsWith('jpeg') ||
                    addedImage.image.name?.endsWith('gif')) && (
                    <>
                      <img
                        style={{
                          width: baseTheme.components.dimension.width.base500,
                          height: `${
                            baseTheme.components.dimension.height.base * 50
                          }px`,
                        }}
                        src={`${addedImage.image.key}`}
                      />
                    </>
                  )}

                  {addedImage.image.name == '' && (
                    <>
                      <DentalkartIcon />
                    </>
                  )}
                </Body>
                <Footer>
                  <FooterItem>
                    <a href={image} target="_blank">
                      <Button style={{ marginRight: '10px' }}>
                        Open in new tab
                      </Button>
                    </a>
                    <Button
                      onClick={() =>
                        setaddedImage({
                          image: {
                            file: '',
                            key: '',
                            name: '',
                          },
                          showImage: false,
                        })
                      }
                    >
                      Close
                    </Button>
                  </FooterItem>
                </Footer>
              </Modal>
            </>
          )}
          {visibleSubmitModal && (
            <ItemsForReturnModal
              data={approvedData.items}
              close={() => {
                setVisibleSubmitModal(false);
              }}
              loading={loading}
              submitRequest={handleSubmit}
            />
          )}
        </>
      )}
    </>
  );
};

export default ReturnItem;
