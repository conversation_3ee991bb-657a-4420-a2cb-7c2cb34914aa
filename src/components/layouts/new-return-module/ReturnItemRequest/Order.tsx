import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { LG as _LG, SM, XL, XXL } from '../../../UI-components/Typography';
import Textarea from '../../../UI-components/TextArea';
import { Label } from '../../../UI-components/Label';
import { Button } from '../../../UI-components/Button';
import { Paragraph, Span, XXXL } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import {
  ColumnDef,
  VisibilityState,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { _TableContainer } from '../../../UI-components/Table';
import { DataTable } from '../../../table/new-return-modules/DataTable';
import useAxios from '../../../../hooks/useAxios';
import { MagentoOrdersSummaryOutput } from '../../../../gql/graphql';
import { baseTheme } from '../../../../themes/theme';
import { Title, Well } from '@zendeskgarden/react-notifications';
import { ADD_ORDER_COMMENT } from '../../../../graphql/mutations';
import LazyLoading from '../../../UI-components/LazyLoading';
import { FrameIcon } from '../../../../utils/icons';
import useToast from '../../../../hooks/useToast';
import { useMutation, useQuery } from '@tanstack/react-query';
import krakendPaths from '../../../../constants/krakendPaths';

const LG = styled(_LG)`
  font-size: 16px;
`;

const TableContainer = styled(_TableContainer)`
  box-shadow: ${baseTheme.components.properties.boxShadow};
`;

interface IComment {
  order_id: string;
  comment: string;
  is_customer_notified: boolean;
  createdBy: string;
}

const Order = ({ orderId }: { orderId: string }) => {
  const [notify, setNotify] = useState<boolean>(false);

  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const axios = useAxios();
  const [returnOrder, setReturnOrder] = useState<any[]>([]);
  const [orderInfo, setOrderInfo] = useState<any>({});
  const [orderSummary, setOrderSummary] = useState<
    MagentoOrdersSummaryOutput[]
  >([]);
  const [comments, setComments] = useState<any[]>([]);
  const username = localStorage.getItem('username');
  const [currentOrderId, setCurrentOrderId] = useState<string>('');
  const addToast = useToast();

  const [currentComment, setCurrentComment] = useState<IComment>({
    order_id: currentOrderId,
    comment: '',
    is_customer_notified: false,
    createdBy: username ? username : 'Username',
  });

  useEffect(() => {
    orderId && setCurrentOrderId(orderId);
  }, []);

  useEffect(() => {
    setCurrentComment({
      order_id: currentOrderId,
      comment: '',
      is_customer_notified: false,
      createdBy: username ? username : 'Username',
    });
    setFilters({
      order_id: currentOrderId,
      pageNumber: 1,
    });

    setCFilters({
      id: currentOrderId,
    });
  }, [currentOrderId]);

  const [filters, setFilters] = useState({
    order_id: currentOrderId,
    pageNumber: 1,
  });

  const [cFilters, setCFilters] = useState({
    id: currentOrderId,
  });

  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_MAGENTO_ORDERS, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onCompleted: (data: any) => {
  //     // console.log('Order GQL', data);
  //     const resultData = data?.getMagentoOrders.result.find(
  //       (res: any) => res.order_id === currentOrderId,
  //     );
  //     setOrderInfo(resultData);
  //     setReturnOrder(resultData?.items);
  //     setOrderSummary(resultData?.order_summary);
  //   },
  //   onError: (err: any) => {
  //     // console.log('Order GQL Error', err);
  //   },
  // });

  const {} = useQuery({
    queryKey: ['get-magento-orders', filters],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.MAGENTO_URL}/admin-api/v1/orders`,
        {
          params: { ...filters },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response;
    },
    enabled: !!filters?.order_id,
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
    onSuccess: (data) => {
      const resultData = data.result.find(
        (res: any) => res.order_id === currentOrderId,
      );
      setOrderInfo(resultData);
      setReturnOrder(resultData?.items);
      setOrderSummary(resultData?.order_summary);
    },
  });

  // const [addComment, { loading: addCommentLoading }] = useMutation(
  //   ADD_ORDER_COMMENT,
  //   {
  //     refetchQueries: [
  //       {
  //         query: GET_ORDER_COMMENTS_BY_ID,
  //         variables: { ...cFilters },
  //       },
  //     ],
  //     onCompleted: (data) => {
  //       addToast('success', 'Added Successfully');
  //     },
  //     onError: (error) => {
  //       addToast('error', error.message);
  //     },
  //   },
  // );

  const { mutate: addComment, isLoading: addCommentLoading } = useMutation(
    async (obj: any) => {
      const response = await axios.post(
        `${krakendPaths.ORDER_COMMENTS_URL}/admin-api/v1/order-comments`,
        obj,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', 'Added Successfully');
      },
    },
  );

  // const {
  //   error: commentError,
  //   loading: commentLoading,
  //   data: commentData,
  //   refetch: commentRefetch,
  // } = useQuery(GET_ORDER_COMMENTS_BY_ID, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...cFilters,
  //   },
  //   onCompleted: (data: any) => {
  //     setComments(data.getOrderCommentsById);
  //   },
  //   onError: (err: any) => {
  //     // console.log('Order GQL Error', err);
  //   },
  // });

  const {
    data: commentData,
    isLoading: commentLoading,
    isLoading: queryLoading,
  } = useQuery({
    queryKey: ['get-order-comments', cFilters?.id],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.ORDER_COMMENTS_URL}/admin-api/v1/order-comments/${cFilters?.id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response?.collection;
    },
    enabled: !!cFilters?.id,
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
    onSuccess: (data) => {
      setComments(data);
    },
  });

  // useEffect(() => {
  //   // console.log('Return Order', returnOrder);
  //   // console.log(comments);
  // }, [returnOrder, comments]);

  const productOrderColumn: ColumnDef<any>[] = [
    {
      id: 'product_id',
      header: 'Id',
      enableHiding: true,
    },
    {
      accessorKey: 'name',
      header: 'Product',
      cell: ({ row }) => {
        // console.log('Row original', row.original.sku);
        const procutName: string = row.getValue('name');
        const sku: string | number = row.original.sku;
        const id: string | number = row.original.product_id;
        const type: string | number = row.original.type;
        const weight: string | number = row.original.weight;
        return (
          <>
            <SM isBold>{procutName}</SM>
            <SM>
              <Span isBold>SKU :</Span>
              {sku}
            </SM>
            <SM>
              <Span>ID :</Span>
              {id}
            </SM>
            <SM>
              <Span>Type :</Span>
              {type}
            </SM>
            <SM>
              <Span>Weight :</Span>
              {weight}
            </SM>
          </>
        );
      },
    },
    {
      accessorKey: 'qty_ordered',
      header: 'Qty',
      cell: ({ row }) => {
        const qty = parseInt(row.getValue('qty_ordered')).toString();
        return (
          <>
            <SM>Ordered : {qty}</SM>
          </>
        );
      },
    },
    {
      accessorKey: 'row_total_incl_tax',
      header: 'Price(Incl.tax)',
      cell: ({ row }) => {
        const price = parseInt(row.getValue('row_total_incl_tax')).toString();
        return (
          <>
            <SM>₹{price}</SM>
          </>
        );
      },
    },
    {
      accessorKey: 'tax_amount' && 'tax_percent',
      header: 'Tax',
      cell: ({ row }) => {
        const tax = row.original.tax_amount;
        const taxPercent: number = row.getValue('tax_percent');
        return (
          <>
            <SM>Tax amount: {tax}</SM>
            <SM>Tax percent: {taxPercent}</SM>
          </>
        );
      },
    },
    {
      accessorKey: 'discount_amount',
      header: 'Discount',
      cell: ({ row }) => {
        const discount = parseInt(row.getValue('discount_amount')).toString();
        return (
          <>
            <SM>{discount}</SM>
          </>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status: string = row.getValue('status');
        return (
          <>
            <SM>{status}</SM>
          </>
        );
      },
    },
  ];

  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Status',
    'Discount',
    'Tax',
    'Price(Incl.tax)',
    'Qty',
    'Product',
  ]);

  const table = useReactTable({
    data: returnOrder,
    columns: productOrderColumn,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const handleComment = (e: any, prop: any) => {
    if (prop === 'is_customer_notified') {
      setCurrentComment((prev) => ({
        ...prev,
        [prop]: !currentComment.is_customer_notified,
      }));
    } else {
      setCurrentComment((prev) => ({ ...prev, [prop]: e.target.value }));
    }
  };

  const handleSubmit = () => {
    // console.log(currentComment);
    if (currentComment.comment.trim() != '') {
      addComment({
        ...currentComment,
      });
    } else {
      addToast('error', 'Invalid Input');
    }
  };

  return (
    <>
      {queryLoading ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          <Row mt="md" justifyContent="center">
            <Col size={12}>
              {table.getRowModel().rows?.length ? (
                <TableContainer>
                  <DataTable
                    data={returnOrder}
                    table={table}
                    columns={productOrderColumn}
                    alreadyEnabledColumn={alreadyEnabledColumn}
                  />
                </TableContainer>
              ) : (
                <>
                  <Row mb="lg" justifyContent="center">
                    <Col textAlign="center" size={12}>
                      <FrameIcon />
                    </Col>
                    <Col textAlign="center">
                      <XXL>Nothing To Show</XXL>
                    </Col>
                  </Row>
                  <Row style={{ height: '20px' }}></Row>
                </>
              )}
            </Col>
          </Row>
          {table.getRowModel().rows?.length != 0 && (
            <Row mt="lg" style={{ marginLeft: '20px', marginRight: '20px' }}>
              <Col size={6}>
                <div style={{ margin: '12px 0' }}>
                  <XL>Notes for this Order</XL>
                </div>
                <div style={{ margin: '12px 0' }}>
                  <SM style={{ color: 'black' }}>Comments</SM>
                </div>
                <div style={{ margin: '12px 0' }}>
                  <Textarea
                    onChange={(e) => handleComment(e, 'comment')}
                    minRows={6}
                    maxRows={8}
                    style={{ fontSize: baseTheme.fontSizes.sm }}
                  />
                </div>
                <div style={{ margin: '12px 0' }}>
                  <Label>
                    <input
                      type="checkbox"
                      checked={currentComment.is_customer_notified}
                      onChange={(e) => handleComment(e, 'is_customer_notified')}
                    />
                    <Span> Notify Customer by Email</Span>
                  </Label>
                </div>
                <div style={{ margin: '12px 0' }}>
                  <Button onClick={handleSubmit} isPrimary isOrange>
                    Submit Comment
                  </Button>
                </div>
                {comments.map((comment, index) => (
                  <>
                    <Well style={{ marginBottom: '12px' }}>
                      <Title>
                        {comment.createdAt} | {comment.status} |{' '}
                        {comment.is_customer_notified
                          ? 'Customer notified'
                          : 'Customer not notified'}
                      </Title>
                      <Paragraph>{comment.comment}</Paragraph>
                    </Well>
                  </>
                ))}
              </Col>
              <Col size={6}>
                <div style={{ margin: '12px 0' }}>
                  <XL>Order Totals</XL>
                </div>
                <div
                  style={{
                    backgroundColor: '#F9F9F9',
                    height: '70%',
                    padding: '8px',
                  }}
                >
                  {orderSummary.length != 0 &&
                    orderSummary.map((orderSum, i) => (
                      <>
                        <Row key={i} style={{ marginBottom: '15px' }}>
                          <Col size={6}>
                            <LG>
                              {orderSum.label == 'DiscountEKFDWJ7037ON'
                                ? 'Discount'
                                : orderSum.label}
                            </LG>
                          </Col>
                          <Col size={6}>{orderSum.value}</Col>
                        </Row>
                      </>
                    ))}
                  <Row>
                    <Col size={6}>
                      <LG>Payment Method</LG>
                    </Col>
                    <Col size={6}>{orderInfo.payment_method_code}</Col>
                  </Row>
                </div>
              </Col>
            </Row>
          )}
        </>
      )}
    </>
  );
};

export default Order;
