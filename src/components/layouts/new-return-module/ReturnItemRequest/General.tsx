import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { MD } from '../../../UI-components/Typography';
import { baseTheme } from '../../../../themes/theme';
import Input from '../../../UI-components/Input';
import { EditInputIcon } from '../../../../utils/icons';
import { Button } from '../../../UI-components/Button';
import { ITag } from '../../../../types/new-return-types';
import TagDropdown from '../../../dropdown/new-return-module/TagDropdown';
import AssigneeDropdown from '../../../dropdown/new-return-module/AssigneeDropdown';
import ShowHistory from '../../../modal/new-return-module/return-item-request/ShowHistory';
import { Accordion } from '@zendeskgarden/react-accordions';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { Span } from '@zendeskgarden/react-typography';
import { Tag } from '@zendeskgarden/react-tags';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import LazyLoading from '../../../UI-components/LazyLoading';
import { useGetReturnAssignees, useGetTag } from '../../../../hooks/useReturn';
import { pageRoutes } from '../../../navigation/RouteConfig';
import constants from '../../../../constants';
import useToast from '../../../../hooks/useToast';
import { Textarea } from '@zendeskgarden/react-forms';
import { IconButton } from '../../../UI-components/IconButton';
import { useMutation } from '@apollo/client';
import EDIT_RETURN_ORDER from '../../../../graphql/mutations/editReturnOrder.gql';
import returnClient from '../../../../apollo-client/ReturnClient';
import { IReturnAssignee } from '../../../../types/types';

const Container = styled.div`
  padding: 25px 27px;
`;

const Row = styled(_Row)`
  margin-bottom: 30px;
`;

interface IRequest {
  status?: any;
  tag?: any[];
  reason?: string;
  description?: string;
  escalation_remark?: string;
  attachment?: any[];
}

const General = ({
  returnItemRequests,
  requestDataByOrder,
  reloadRequestThroughOrderID,
  isOrderLoading,
}: {
  returnItemRequests: any;
  requestDataByOrder: any[];
  reloadRequestThroughOrderID: () => void;
  isOrderLoading: boolean;
}) => {
  const [returnAssignee, setReturnAssignee] = useState<IReturnAssignee[]>([]);
  // const [tagData, setTagData] = useState<ITag[]>([]);
  const [selectedTag, setSelectedTag] = useState<ITag | undefined>(undefined);
  const [selectedTags, setSelectedTags] = useState<ITag[]>([]);
  const [selectedAssignee, setSelectedAssignee] = useState<
    IReturnAssignee | undefined
  >(undefined);
  const [attachments, setAttachments] = useState<any[]>([]);
  const [requestSent, setRequestSent] = useState(false);
  const [description, setDescription] = useState<string>('');
  const [escalationRemark, setEscalationRemark] = useState<string>('');
  const [rcaLink, setrcaLink] = useState<string>('');
  const [returnOrder, setReturnOrder] = useState<any>({});
  const [returnFullHistory, setReturnFullHistory] = useState<any[]>([]);
  const [historyData, setHistoryData] = useState<any[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showImage, setShowImage] = useState<boolean>(false);

  const [returnRequest, setReturnRequest] = useState<IRequest>({});
  const { returnId: returnItemID } = useParams();

  const [returnId, setReturnId] = useState<any>(returnItemID);
  const [orderID, setOrderID] = useState<string>();
  const navigate = useNavigate();
  const addToast = useToast();

  const {
    data: assignees,
    isLoading: isAssigneeLoading,
    refetch: reloadReturnAssignees,
  } = useGetReturnAssignees();

  const {
    data: tags,
    isLoading: isTagLoading,
    refetch: reloadTags,
  } = useGetTag();

  useEffect(() => {
    if (returnItemRequests) {
      const {
        order_id,
        assignee_history,
        tags: returnOrderTags,
        current_assignee,
      } = returnItemRequests;
      setOrderID(order_id);
      setReturnOrder(returnItemRequests);
      setHistoryData(assignee_history);

      if (current_assignee) setSelectedAssignee(current_assignee);
      if (returnOrderTags) {
        setSelectedTags(returnOrderTags);
        // const { results } = tags;
        // setTagData(tags);
      }
    }

    if (requestDataByOrder) {
      setReturnFullHistory(requestDataByOrder);
    }

    if (assignees) {
      const { results } = assignees;
      setReturnAssignee(results);
    }
  }, [returnItemRequests, requestDataByOrder, assignees]);

  useEffect(() => {
    if (selectedTag) {
      setSelectedTags((prevTags) => {
        const isTagAlreadySelected = prevTags.some(
          (tag) => tag.id === selectedTag.id,
        );
        if (!isTagAlreadySelected) {
          return [...prevTags, selectedTag];
        }
        return prevTags;
      });
    }
  }, [selectedTag]);

  useEffect(() => {
    setReturnRequest((prev) => ({
      ...prev,
      tag: selectedTags.map((tag) => tag.id),
    }));
    if (attachments.length != 0) {
      setReturnRequest((prev) => ({
        ...prev,
        attachment: [...attachments],
      }));
    }
  }, [selectedTags, attachments]);

  const onChangeDescription = (e: any) => {
    setDescription(e.target.value);
    setReturnRequest((prev) => ({
      ...prev,
      description: e.target.value,
    }));
  };

  const [editReturnOrder] = useMutation(EDIT_RETURN_ORDER, {
    client: returnClient,
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    onCompleted() {
      addToast('success', 'Return order edited successfully');
      returnClient.refetchQueries({
        // include: [GET_RETURN_ORDER],
      });
    },
  });

  const close = () => {
    setShowHistory(false);
  };

  const handleSaveClick = () => {
    editReturnOrder({
      variables: {
        return_order_id: returnItemRequests?.id,
        description: description,
        escalation_remark: escalationRemark,
        rca_attachment: rcaLink,
        assignee_id: selectedAssignee?.id ?? null,
        tags: selectedTags.map((t) => t.id),
      },
    });
  };

  const [editable, setEditable] = useState<boolean>(false);
  const [editableEscalation, setEditableEscalation] = useState<boolean>(false);
  const [editableRCA, setEditableRCA] = useState<boolean>(false);

  useEffect(() => {
    if (returnOrder != undefined) {
      setDescription(returnOrder?.description);
      setEscalationRemark(returnOrder?.escalation_remark);
      setrcaLink(returnOrder?.rca_attachment?.attachment);
      if (returnOrder.tag) {
        const currentTagsId: number[] = returnOrder.tag;
        const currentTags = tags?.filter((item) =>
          currentTagsId.includes(item.id),
        );
        setSelectedTags(currentTags);
      }
      setReturnRequest((prev) => ({
        ...prev,
        status: returnOrder.status,
        reason: returnOrder.reason,
      }));
    }
  }, [returnOrder]);

  // useEffect(() => {
  //   selectedTag &&
  //     setReturnRequest((prev) => ({ ...prev, tag: [selectedTag.id] }));
  // }, [selectedTag]);

  const handleRemoveTag = (index: number) => {
    setSelectedTags((prevTags) => {
      const updatedTags: any = [...prevTags];
      updatedTags.splice(index, 1);
      return updatedTags;
    });
  };

  // useEffect(() => {
  //   console.log('SelectedTags', selectedTags);
  // }, [selectedTags]);

  // useEffect(() => {
  //   console.log('Return Request', returnRequest);
  // }, [returnRequest]);

  return (
    <>
      {isOrderLoading ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          <Container>
            <Row justifyContent="start" alignItems="center">
              <Col size={2}>
                <MD hue="primary" isBold>
                  Order Id
                </MD>
              </Col>
              <Col size={6}>
                <Input
                  BackgroundColor={baseTheme.components.TextInput.bgColor}
                  disabled
                  value={returnOrder?.order_id}
                />
              </Col>
            </Row>
            <Row alignItems="center">
              <Col size={2}>
                <MD hue="primary" isBold>
                  Description
                </MD>
              </Col>
              <Col size={6}>
                <Row alignItems="center">
                  <Col size={10}>
                    <Textarea
                      minRows={2}
                      maxRows={12}
                      style={{
                        backgroundColor: baseTheme.components.TextInput.bgColor,
                        color: !editable
                          ? baseTheme.colors.veryDarkGray
                          : baseTheme.colors.deepBlue,
                      }}
                      value={description}
                      readOnly={!editable}
                      onChange={(e) => {
                        onChangeDescription(e);
                      }}
                    />
                  </Col>
                  <Col>
                    <IconButton>
                      <EditInputIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setEditable(!editable);
                        }}
                      />
                    </IconButton>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row alignItems="center">
              <Col size={2}>
                <MD hue="primary" isBold>
                  Escalation Remark
                </MD>
              </Col>
              <Col size={6}>
                <Row alignItems="center">
                  <Col size={10}>
                    <Textarea
                      minRows={2}
                      maxRows={12}
                      style={{
                        backgroundColor: baseTheme.components.TextInput.bgColor,
                        color: !editableEscalation
                          ? baseTheme.colors.veryDarkGray
                          : baseTheme.colors.deepBlue,
                      }}
                      value={escalationRemark}
                      readOnly={!editableEscalation}
                      onChange={(e) => {
                        setEscalationRemark(e.target.value);
                      }}
                    />
                  </Col>
                  <Col>
                    <IconButton>
                      <EditInputIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setEditableEscalation(!editableEscalation);
                        }}
                      />
                    </IconButton>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row alignItems="center">
              <Col size={2}>
                <MD hue="primary" isBold>
                  RCA
                </MD>
              </Col>
              <Col size={6}>
                <Row alignItems="center">
                  <Col size={10}>
                    {editableRCA ? (
                      <Textarea
                        minRows={2}
                        maxRows={12}
                        placeholder="Enter RCA Link"
                        style={{
                          backgroundColor:
                            baseTheme.components.TextInput.bgColor,
                          color: !editable
                            ? baseTheme.colors.veryDarkGray
                            : baseTheme.colors.deepBlue,
                        }}
                        value={rcaLink}
                        readOnly={!editableRCA}
                        onChange={(e) => {
                          setrcaLink(e.target.value);
                        }}
                      />
                    ) : (
                      <Link to={rcaLink} target="_blank">
                        {rcaLink || 'No RCA Link'}
                      </Link>
                    )}
                  </Col>
                  <Col>
                    <IconButton>
                      <EditInputIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setEditableRCA(!editableRCA);
                        }}
                      />
                    </IconButton>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row>
              <Col alignSelf="auto" size={2}>
                <MD
                  style={{ transform: 'translateY(50%)' }}
                  hue="primary"
                  isBold
                >
                  Tags
                </MD>
              </Col>
              <Col size={6}>
                <Row alignItems="center">
                  <Col size={7}>
                    <TagDropdown
                      data={tags}
                      selectedItem={selectedTag}
                      setSelectedItem={setSelectedTag}
                    />
                  </Col>
                  <Col>
                    {selectedTags.map((currentTag: ITag, i: number) => (
                      <>
                        <Tag
                          style={{ marginLeft: baseTheme.space.md }}
                          key={i}
                          hue={baseTheme.colors.deepBlue}
                          size="large"
                        >
                          <Span>{currentTag.label}</Span>
                          <Tooltip content="Remove Tag">
                            <Tag.Close
                              aria-label="Remove tag"
                              onClick={() => handleRemoveTag(i)}
                            />
                          </Tooltip>
                        </Tag>
                      </>
                    ))}
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col size={2}>
                <MD hue="primary" isBold>
                  Assign To
                </MD>
              </Col>
              <Col size={6}>
                <AssigneeDropdown
                  data={assignees}
                  selectedItem={selectedAssignee}
                  setSelectedItem={setSelectedAssignee}
                />
              </Col>
            </Row>
            <Row alignItems="center" justifyContent="start">
              <Col size={4}>
                <MD hue="primary" isBold>
                  Assignee History
                </MD>
              </Col>
              <Col size={8}>
                <Button
                  onClick={() => {
                    setShowHistory(true);
                  }}
                  isOrange
                  isPrimary
                >
                  Show Data
                </Button>
              </Col>
            </Row>
            <Row justifyContent="end">
              <Button
                isPrimary
                onClick={() => {
                  handleSaveClick();
                }}
              >
                Save
              </Button>
            </Row>
            <Row>
              <Col size={4}>
                <MD hue="primary" isBold>
                  Duplicate Returns
                </MD>
              </Col>
            </Row>
            <Row>
              <Col size={8}>
                <Accordion level={4} isExpandable>
                  {returnFullHistory &&
                    returnFullHistory
                      .filter((history) => history.id != returnId)
                      .map((history, index) => (
                        <>
                          <Accordion.Section>
                            <Accordion.Header>
                              <Accordion.Label>
                                <Span
                                  style={{
                                    color: baseTheme.colors.deepBlue,
                                  }}
                                  onClick={() => {
                                    navigate(
                                      `${pageRoutes['GO_TO_NEW_RETURN_ITEM_GENERAL']}/${history.id}`,
                                    );
                                  }}
                                >
                                  {history?.id}
                                </Span>
                              </Accordion.Label>
                            </Accordion.Header>
                            <Accordion.Panel>
                              <Row>
                                <Col>
                                  <Row>
                                    <Col size={2}>
                                      <Span isBold>Order Id</Span>
                                    </Col>
                                    <Col size={6}>{history?.order_id}</Col>
                                  </Row>
                                  <Row>
                                    <Col size={2}>
                                      <Span isBold>Payment method</Span>
                                    </Col>
                                    <Col size={6}>{history.payment_method}</Col>
                                  </Row>
                                  <Row>
                                    <Col size={2}>
                                      <Span isBold>Description</Span>
                                    </Col>
                                    <Col size={6}>{history.description}</Col>
                                  </Row>

                                  <Row>
                                    <Col size={2}>
                                      <Span isBold>Tags</Span>
                                    </Col>
                                    <Col size={6}></Col>
                                  </Row>
                                </Col>
                              </Row>
                            </Accordion.Panel>
                          </Accordion.Section>
                        </>
                      ))}
                </Accordion>
              </Col>
            </Row>
            {showHistory && (
              <>
                <ShowHistory historyData={historyData} close={close} />
              </>
            )}
          </Container>
        </>
      )}
    </>
  );
};

export default General;
