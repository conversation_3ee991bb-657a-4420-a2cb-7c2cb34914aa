import { useState, useEffect } from 'react';
import { IReason, IReturnAction } from '../../../types/types';
import ReturnCreateTable from '../../table/new-return-modules/ReturnCreate';
import useToast from '../../../hooks/useToast';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import useAxios from '../../../hooks/useAxios';
import krakendPaths from '../../../constants/krakendPaths';

const ReturnCreateLayout = () => {
  const [page, setPage] = useState<number>(1);
  const totalPages = 20;

  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  const [reasonData, setReasonData] = useState<IReason[] | undefined>([]);
  const [returnActionData, setReturnActionData] = useState<
    IReturnAction[] | undefined
  >([]);

  const [visibleContainer, setVisibleContainer] = useState<boolean>(false);
  const [requestSent, setRequestSent] = useState<boolean>(false);
  const [shouldEnableQuery, setshouldEnableQuery] = useState(false);
  // const [orderData, setOrderData] = useState<any | undefined>();

  const [returnCreateData, setReturnCreateData] = useState<any[]>([]);

  const addToast = useToast();
  const axios = useAxios();
  const {
    data: orderData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['adminReturnValidate', searchContent],
    queryFn: async () => {
      const response: any = await axios(
        `${krakendPaths.RETURN_URL}/admin-api/v1/returns/validate/${searchContent}`,
        {
          method: 'GET',
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response?.validated_return;
    },
    enabled: shouldEnableQuery, // Runs the query only when this is true
    onError: (error: any) => {
      addToast('error', error.message);
    },
  });

  useEffect(() => {
    if (
      reasonData != undefined &&
      returnActionData != undefined &&
      orderData != undefined
    ) {
      setReturnCreateData(orderData as any);
    }
  }, [reasonData, returnActionData, orderData]);

  const handleSearch = () => {
    const regex = /^[\d-]+$/;
    if (searchContent != undefined && searchContent != '') {
      if (regex.test(searchContent)) {
        setshouldEnableQuery(true);
        refetch();
        setVisibleContainer(true);
        setRequestSent(false);
        return;
      } else {
        addToast('info', 'Please provide a valid order id to search');
        return;
      }
    }
    addToast('info', 'Please provide order id to search');
  };

  return (
    <>
      <>
        <ReturnCreateTable
          searchContent={searchContent}
          setSearchContent={setSearchContent}
          returnCreateData={(orderData as any) ?? []}
          setReturnCreateData={setReturnCreateData}
          page={page}
          isLoading={isLoading}
          setPage={setPage}
          totalPage={totalPages}
          visibleContainer={visibleContainer}
          handleSearch={handleSearch}
          setVisibleContainer={setVisibleContainer}
          orderData={orderData}
          requestSent={requestSent}
          setRequestSent={setRequestSent}
        />
      </>
    </>
  );
};

export default ReturnCreateLayout;
