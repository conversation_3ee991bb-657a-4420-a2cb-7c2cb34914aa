import React, { useEffect, useState } from 'react';
import { baseTheme } from '../../themes/theme';

interface ReadMoreProps {
  text: string;
  limit: number;
}

export const ReadMore: React.FC<ReadMoreProps> = ({ text, limit }) => {
  const [expanded, setExpanded] = useState(false);
  useEffect(() => {
    setExpanded(false);
  }, [text, limit]);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const renderContent = () => {
    if (expanded) {
      return (
        <>
          {text}{' '}
          <span
            onClick={toggleExpanded}
            style={{
              color: baseTheme.colors.primaryHue,
              cursor: 'pointer',
              fontWeight: 500,
            }}
          >
            Read Less
          </span>
        </>
      );
    }

    if (text && text.length <= limit) {
      return <>{text}</>;
    }

    const displayedText = text && text.slice(0, limit).trim();
    const hasMoreText = text && text.length > limit;

    return (
      <>
        {displayedText}...
        {hasMoreText && (
          <span
            onClick={toggleExpanded}
            style={{
              color: baseTheme.colors.primaryHue,
              cursor: 'pointer',
              fontWeight: 500,
            }}
          >
            Read More
          </span>
        )}
      </>
    );
  };

  return <div className="content">{renderContent()}</div>;
};

export default ReadMore;
