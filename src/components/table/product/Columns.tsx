import { ColumnDef } from '@tanstack/react-table';
import styled from 'styled-components';
import {
  Link as _Link,
  useLocation,
  useNavigate,
  useNavigation,
  useParams,
} from 'react-router-dom';
import { baseTheme } from '../../../themes/theme';
import {
  DeleteIcon,
  DownIcon,
  Download,
  EditCustomIcon,
  PenIcon,
  RemoveRedIcon,
  SortArrow,
} from '../../../utils/icons';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import {
  ProductDataAttrOutput,
  ProductDataAttributeInput,
} from '../../../gql/graphql';
import { Button } from '../../UI-components/Button';
import { SetStateAction, useEffect, useRef, useState } from 'react';
import { ProductFeedAttributeModal } from '../../modal/ProductFeedAttributeModal';
import { Row } from '../../UI-components/Grid';
import { Tag } from '../../UI-components/Tags';
import { formatDateIntoDDMMYYYYAndTime } from '../../../helpers/helper';
import ViewChildModal from '../../modal/catalog-service/ViewChildModal';
import {
  Checkbox,
  Field as CField,
  Label,
  Field,
  Input,
} from '@zendeskgarden/react-forms';
import { Col } from '@zendeskgarden/react-grid';
import ChangeStatus from '../../modal/catalog-service/ChangeStatus';
import { pageRoutes } from '../../navigation/RouteConfig';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import CompletionModal from '../../modal/catalog-service/CompletionModal';
import ActivityLog from '../../modal/catalog-service/ActivityLog';
import CategoryAssociated from '../../modal/catalog-service/CategoryAssociatedModal';
import emptyImage from '../../../assets/item.png';
import { format } from 'date-fns';
import DeleteAttachmentModal from '../../modal/catalog-service/Attachment/DeleteAttachmentModal';
import DeleteBulkAttachmentModal from '../../modal/catalog-service/Attachment/DeleteBulkAttachmentModal';
import ChangeStatusModal from '../../modal/catalog-service/Attachment/ChangeAttachStatusModal';
import DeleteBulkAttachProductModal from '../../modal/catalog-service/Attachment/DeleteBulkAttachProductModal';
import DeleteBulkTagProductModal from '../../modal/catalog-service/Tags/DeleteBulkTagProductModal';
import DeleteBulkTagModal from '../../modal/catalog-service/Tags/DeleteBulkTagModal';
import ChangeTagStatusModal from '../../modal/catalog-service/Tags/ChangeTagStatusModal';
import ChangeAttachStatusModal from '../../modal/catalog-service/Attachment/ChangeAttachStatusModal';
import DeleteTagModal from '../../modal/catalog-service/Tags/DeleteTagModal';
import { XL } from '../../UI-components/Typography';
import { useTagContext } from '../../../pages/catalog-service/Tags/TagsContext';
import { useAttachmentContext } from '../../../pages/catalog-service/attachment/AttachmentContext';
import { Dropdown, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import UpdatePositionModal from '../../modal/catalog-service/UpdatePositionModal';
const thumbnailUrl = '';

const defaultPlacholder =
  'https://www.dentalkart.com/media/catalog/product/no_selection';

const Link = styled(_Link)`
  cursor: pointer;
`;
export type ProductFeedAttributesColumn = {
  product_id: number | undefined | null;
  sku: string | undefined | null;
  // product_type: string;
  categories: string;
  shopping_ads_excul_country: string | undefined | null;
  generate_feed: boolean;
  created_at?: string | undefined | null;
  updated_at?: string | undefined | null;
  allowChild: boolean;
  gtin: string | undefined | null;
};

interface HeaderDivProps {
  columnWidth: string;
  isCenter?: boolean;
}

const HeaderDiv = styled.div<HeaderDivProps>`
  width: ${(props) => props.columnWidth};
  ${(props) =>
    props.isCenter ? `justify-content:center; text-align: center;` : ''}
`;

function addTimeToISODateTime(isoDateTime: string): string {
  const date = new Date(isoDateTime);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');

  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12;
  const formattedHours = String(hours).padStart(2, '0');

  return `${year}-${month}-${day} ${formattedHours}:${minutes} ${ampm}`;
}

function formatDate(isoDateTime: string): string {
  const date = new Date(isoDateTime);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

export const ProductFeedAttributesColumns: ColumnDef<ProductDataAttrOutput>[] =
  [
    {
      accessorKey: 'action',
      header: 'Action',
      enableSorting: false,
      cell: ({ row }) => {
        const data: ProductFeedAttributesColumn = {
          allowChild: row.getValue('allowChild'),
          generate_feed: row.getValue('generate_feed'),
          product_id: row.getValue('product_id'),
          // product_type: row.getValue('product_type'),
          categories: row.getValue('categories'),
          shopping_ads_excul_country: row.getValue(
            'shopping_ads_excul_country',
          ),
          sku: row.getValue('sku'),
          created_at: row.getValue('created_at'),
          gtin: row.getValue('gtin'),
        };
        const [visible, setVisible] = useState<boolean>(false);

        return (
          <>
            {/* <Button style={{ border: 'none', width: '5px' }}>
              <PenIcon />
            </Button> */}
            <Row alignItems="center" justifyContent="center">
              <span
                style={{ height: '20px', width: '20px', cursor: 'pointer' }}
                onClick={() => setVisible(!visible)}
              >
                <PenIcon />
              </span>
            </Row>

            {visible && (
              <ProductFeedAttributeModal data={data} setVisible={setVisible} />
            )}
          </>
        );
      },
    },
    {
      accessorKey: 'product_id',
      header: 'Product Id',
      enableSorting: false,
    },
    {
      accessorKey: 'sku',
      header: 'Sku',
      enableSorting: false,
    },
    {
      accessorKey: 'product_name',
      header: 'Product Name',
      enableSorting: false,
    },
    {
      accessorKey: 'brand',
      header: 'Brand',
      enableSorting: false,
    },
    {
      accessorKey: 'product_status',
      header: 'Product Status',
      enableSorting: false,
      cell: ({ row }) => {
        const value = Number(row.getValue('product_status'));

        const getValue = (value: number) => {
          switch (value) {
            case 1:
              return 'Enabled';
            case 2:
              return 'Disabled';
            default:
              return '';
          }
        };
        return <span>{getValue(value)}</span>;
      },
    },
    {
      accessorKey: 'categories',
      header: 'Product Category',
      enableSorting: false,
    },
    {
      accessorKey: 'product_type',
      header: 'Product Type',
      enableSorting: false,
    },
    {
      accessorKey: 'shopping_ads_excul_country',
      header: 'Shopping Ads Excluding Country',
      enableSorting: false,
      cell: ({ row }) => {
        const value = row.getValue('shopping_ads_excul_country');
        return (
          <>
            <HeaderDiv
              isCenter
              columnWidth={baseTheme.components.dimension.width.base150}
            >
              {value != '' && value != null ? (
                <>{value}</>
              ) : (
                <Tag>Not Provided</Tag>
              )}
            </HeaderDiv>
          </>
        );
      },
    },
    {
      accessorKey: 'generate_feed',
      header: 'Generated Feed',
      enableSorting: false,
      cell: ({ row }) => {
        const dageneratedFeed: boolean = row.getValue('generate_feed');
        return <span>{String(dageneratedFeed)}</span>;
      },
    },
    {
      accessorKey: 'gtin',
      header: 'Gtin',
      enableSorting: false,
    },
    {
      accessorKey: 'stock_status',
      header: 'Stock Status',
      enableSorting: false,
    },
    {
      accessorKey: 'magento_stock_status',
      header: 'Magento Stock Status',
      enableSorting: false,
    },
    {
      accessorKey: 'type_id',
      header: 'Type Id',
      enableSorting: false,
    },
    // {
    //   accessorKey: 'group_id',
    //   header: 'Parent Id',
    //   enableSorting: false,
    // },
    {
      accessorKey: 'view_child',
      header: 'Child Data',
      enableSorting: false,
      cell: ({ row }) => {
        const parentId: number = row.getValue('product_id');
        const productType: string = row.getValue('type_id');

        const [viewChild, setViewChild] = useState(false);

        return productType === 'grouped' ? (
          <>
            <span
              style={{
                color: 'blue',
                textDecoration: 'underline',
                cursor: 'pointer',
              }}
              onClick={() => setViewChild(true)}
            >
              View Child
            </span>
            {viewChild && (
              <ViewChildModal
                parentId={parentId}
                viewChild={viewChild}
                close={() => {
                  setViewChild(false);
                }}
              />
            )}
          </>
        ) : (
          <span>No Child</span>
        );
      },
    },
    {
      accessorKey: 'product_visibility',
      header: 'Product Visibility',
      enableSorting: false,
      cell: ({ row }) => {
        const value = Number(row.getValue('product_visibility'));

        const getValue = (value: number) => {
          switch (value) {
            case 1:
              return 'Not Visible Individually';
            case 2:
              return 'Catalog';
            case 3:
              return 'Search';
            case 4:
              return 'Catalog, Search';
            default:
              return '';
          }
        };
        return <span>{getValue(value)}</span>;
      },
    },
    {
      accessorKey: 'internationally_enabled',
      header: 'Internationally Enabled,',
      enableSorting: false,
      cell: ({ row }) => {
        const value = Number(row.getValue('internationally_enabled'));

        const getValue = (value: number) => {
          switch (value) {
            case 1:
              return 'true';
            case 0:
              return 'false';
            default:
              return '';
          }
        };
        return <span>{getValue(value)}</span>;
      },
    },
    {
      accessorKey: 'last_generated_at',
      header: 'Last Generated At',
      enableSorting: false,
      cell: ({ row }) => {
        const value: string = row.getValue(`last_generated_at`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base200}>
            {value && formatDateIntoDDMMYYYYAndTime(value)}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'stock_status',
      header: 'Stock Status',
    },
    {
      accessorKey: 'type_id',
      header: 'Type Id',
    },
    {
      accessorKey: 'last_generated_at',
      header: 'Last Generated At',
      cell: ({ row }) => {
        const value: string = row.getValue(`last_generated_at`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base200}>
            {value && formatDateIntoDDMMYYYYAndTime(value)}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: ({ row }) => {
        const value: string = row.getValue(`created_at`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base200}>
            {value && formatDateIntoDDMMYYYYAndTime(value)}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'updated_at',
      header: 'Updated At',
      cell: ({ row }) => {
        const value: string = row.getValue(`updated_at`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base200}>
            {value && formatDateIntoDDMMYYYYAndTime(value)}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'feed_update_history',
      header: 'Feed Update History',
      enableSorting: false,
    },
    // allowChild,
    {
      accessorKey: 'allowChild',
      header: 'allowChild',
      enableHiding: true,
      enableSorting: false,
    },
  ];

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownContent = styled.div<{ open: boolean }>`
  display: ${({ open }) => (open ? 'block' : 'none')};
  position: absolute;
  top: 100%;
  left: 0;
  width: max-content;
  color: #373737;
  background-color: #fff;
  border-radius: 4px;
  padding: 8px;
  z-index: 100;
`;

const DropdownItem = styled.div`
  padding: 8px;
  cursor: pointer;

  &:hover {
    background-color: #f2f2f2;
  }
`;

const DropDownItemX = styled.div`
  padding: 10px 15px;
  margin: 8px 2px;
  cursor: pointer;
  color: black;
`;

export interface ProductLinks {
  associated: {
    product_id: number;
    position: number;
  }[];
  crosssell: {
    product_id: number;
    position: number;
  }[];
  upsell: {
    product_id: number;
    position: number;
  }[];
  related: {
    product_id: number;
    position: number;
  }[];
}
interface TierPrice {
  qty: number;
  value: number;
  customer_group: string;
  price_type: string;
}

interface GroupTierPrice {
  price_group: number;
  tier_prices: TierPrice[];
  child_ids: number[];
}

interface GroupTierPriceCollection {
  [key: string]: GroupTierPrice;
}

export interface ProductColumns {
  id: number;
  sku: string;
  status: boolean | undefined;
  type_id: string;
  completion_percentage: {
    percentage: number;
    complete_fields: string[];
    incomplete_fields: string[];
  };
  product_links: ProductLinks;
  attributes_list: {
    [key: string]: string | number | boolean | null | undefined;
    ts_packaging_type: string | null;
    upsells_products_last_fetched: string | null;
    mw_reward_point_sell_product: string | null;
    expiry?: string | null;
    has_options: string | null;
    required_options: string | null;
    url_path: string | null;
    generate_feed: string | null;
    msrp_display_actual_price_type: string | null;
    swatch_image: string | null;
    options_container: string | null;
    gift_message_available: string | null;
    is_recurring: string | null;
    name: string;
    description: string | null;
    short_description: string | null;
    other_info: string | null;
    price: any;
    special_price: string | null;
    special_from_date?: string | null;
    special_to_date?: string | null;
    weight: string | null;
    manufacturer: any;
    image: string | null;
    small_image: string | null;
    thumbnail: string | null;
    tier_price: string | null;
    news_from_date?: string | null;
    news_to_date?: string | null;
    url_key: string | null;
    visibility?: any;
    category_ids: string | null;
    country_of_manufacture?: any;
    msrp: string | null;
    tax_class_id?: any;
    key_specifications: string | null;
    featured: string | null;
    features: string | null;
    htext: string | null;
    packaging: string | null;
    hvideo: string | null;
    hsn_code: string | null;
    is_cod?: boolean | null;
    warranty: string | null;
    product_faq: string | null;
    reward_point_product?: number | null;
    international_active: boolean | null;
    average_rating: number | null;
    rating_count: number | null;
    return_period: number | null;
    dispatch_days?: any;
    pd_expiry_date?: string | null;
    gtin: string | null;
    dentalkart_custom_fee?: number | null;
    demo_available?: boolean | null;
    percent?: string | null;
    meta_title?: string | null;
    meta_keyword?: string | null;
    meta_description?: string | null;
    video: string | null;
    bgbd?: boolean | null;
    buyingguide_preferred_product?: boolean | null;
  };
  inventory_details: {
    qty: number | null;
    is_in_stock?: boolean | null;
    min_sale_qty: number | null;
    max_sale_qty: number | null;
    backorders: any;
  };
  tier_prices: {
    qty: number;
    value: number;
    customer_group: string;
    price_type: string;
  }[];
  group_tier_price: GroupTierPriceCollection;
  category_associated: any[];
  media_gallery_entries: {
    id: number;
    value: string;
    is_disabled?: boolean;
    image_tags: string;
    position?: number;
    url?: string;
    title?: string;
    description?: string;
  }[];
}
export const ProductTableColumns: ColumnDef<ProductColumns>[] = [
  {
    accessorKey: 'Checkbox',
    header: ({ table }) => {
      const [isChecked, setIsChecked] = useState(false);
      const [isChangeStatus, setIsChangeStatus] = useState(false);
      const [isOpen, setIsOpen] = useState(false);
      const [rotated, setRotated] = useState<boolean | undefined>();
      const handleToggleDropdown = () => {
        setIsOpen(!isOpen);
      };
      const handleChangeCheckbox = (e: any) => {
        const value = e.target.value;
        if (isChecked) {
          table.toggleAllPageRowsSelected(false);
        } else {
          table.toggleAllPageRowsSelected(true);
        }
        setIsChecked(!isChecked);
      };
      const selectedRows = table
        ?.getSelectedRowModel()
        .rows.map((item, index) => {
          return item.original.id;
        });

      const handleItemClick = () => {
        setIsOpen(false);
      };
      return (
        <Row>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <CField>
              <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                <Label hidden>Check</Label>
              </Checkbox>
            </CField>
          </Col>
          <Col
            style={{
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'start',
            }}
          >
            <Dropdown
              onSelect={(item) => alert(`You planted a ${item}`)}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
              }
            >
              <Trigger>
                <div>
                  <DownIcon onClick={handleToggleDropdown} />
                </div>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base150,
                  height: '50px',
                  borderRadius: baseTheme.borderRadii.sm,
                  alignContent: 'center',
                  justifyContent: 'center',
                }}
              >
                <div
                  onClick={() => {
                    handleItemClick();
                    setIsChangeStatus(!isChangeStatus);
                  }}
                  style={{
                    color: 'black',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                  }}
                >
                  Change Status
                </div>
              </Menu>
            </Dropdown>
          </Col>
          {isChangeStatus && (
            <>
              <ChangeStatus
                ids={selectedRows}
                visible={isChangeStatus}
                setVisible={setIsChangeStatus}
              />
            </>
          )}
        </Row>
      );
    },
    enableSorting: false,
    cell: ({ row }) => {
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        row.toggleSelected(checked);
      };

      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <CField>
                <Checkbox
                  checked={row.getIsSelected()}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </CField>
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'Id',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'id') {
          setField('id');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Id</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>{id}</Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      const thumbnail = row.original.media_gallery_entries;
      const [image, setImage] = useState('');
      const id = row.original.id;
      useEffect(() => {
        if (thumbnail) {
          const gallery = thumbnail.filter((item) => {
            return item?.image_tags
              ?.split(',')
              .map((tag) => tag.trim())
              .includes('thumbnail');
          });
          if (gallery.length > 0) {
            setImage(gallery[0].value);
          }
        }
      }, []);
      return (
        <Link
          to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <img
            src={image ? image : emptyImage}
            style={{
              width: '60px',
              height: '60px',
              minHeight: '60px',
              minWidth: '60px',
              borderRadius: '5px',
            }}
            alt="thumbnail"
          />
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'SKU',
    header: 'SKU',
    cell: ({ row }) => {
      const Id = row.original.sku;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>{Id}</Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Product Name',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'name') {
          setField('name');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <Row
          justifyContent="between"
          alignItems="center"
          ml="xxs"
          style={{ width: `${baseTheme.components.dimension.width.base400}` }}
        >
          <div>Product Name</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </Row>
      );
    },
    cell: ({ row }) => {
      const name = row.original.attributes_list?.name;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {name}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Product Type',
    header: 'Product Type',
    cell: ({ row }) => {
      const type_Id = row.original.type_id;
      let productType = '';
      const id = row.original.id;

      switch (type_Id) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = 'Unknown Product';
      }

      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {productType}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Brand',
    header: 'Brand',
    cell: ({ row }) => {
      const manufacturer = row.original.attributes_list.manufacturer;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {manufacturer}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      const id = row.original.id;
      const displayValue =
        status === true ? 'Enabled' : status === false ? 'Disabled' : '';
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {displayValue}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Price',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'price') {
          setField('price');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Price</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const price = row.original.attributes_list?.price;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {price}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Selling Price',
    header: 'Selling Price',
    cell: ({ row }) => {
      const val = row.original.attributes_list.special_price;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {val}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Visibility',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'visibility') {
          setField('visibility');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Visibility</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const visibility = row.original.attributes_list?.visibility;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {visibility}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Backorders',
    header: 'Backorders',
    cell: ({ row }) => {
      const backorders = row.original.inventory_details.backorders;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {backorders != undefined
            ? backorders === true
              ? 'Allow Qty Below 0'
              : 'No Backorders'
            : ''}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Quantity',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'qty') {
          setField('qty');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Quantity</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const qty = row.original.inventory_details.qty;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {qty}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Stock Status',
    header: 'Stock Status',
    cell: ({ row }) => {
      const stock = row.original.inventory_details.is_in_stock;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {stock === true ? 'In Stock' : stock === false ? 'Out of Stock' : ''}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'HSN Code',
    header: 'HSN Code',
    cell: ({ row }) => {
      const val = row.original.attributes_list.hsn_code;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {val}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Product Expiry Date',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'pd_expiry_date') {
          setField('pd_expiry_date');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: `${baseTheme.components.dimension.width.base200}`,
          }}
        >
          <div>Product Expiry Date</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const pdExpiry = row.original.attributes_list?.pd_expiry_date;
      const isoDateString = pdExpiry ? new Date(pdExpiry).toISOString() : '';
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {isoDateString ? formatDate(isoDateString) : ''}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'URL Key',
    header: 'URL Key',
    cell: ({ row }) => {
      const key = row.original.attributes_list?.url_key;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {key}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Request Price',
    header: 'Request Price',
    cell: ({ row }) => {
      const msrp = row.original.attributes_list?.msrp;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {msrp}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Reward Coin',
    header: () => {
      const { order, setOrder, field, setField } = useProductContext();
      const handleIdClick = () => {
        if (field !== 'reward_point_product') {
          setField('reward_point_product');
          setOrder('DESC');
        } else {
          setOrder((prevOrder) => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Reward Coin</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const reward_coin = row.original.attributes_list?.reward_point_product;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {reward_coin}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'COD',
    header: 'COD',
    cell: ({ row }) => {
      const val = row.original.attributes_list.is_cod;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {val === true ? 'YES' : val === false ? 'NO' : ''}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Tax Class',
    header: 'Tax Class',
    cell: ({ row }) => {
      const val = row.original.attributes_list.tax_class_id;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {val}
        </Link>
      );
    },
    enableSorting: false,
  },

  {
    accessorKey: 'Weight',
    header: 'Weight',
    cell: ({ row }) => {
      const weight = row.original.attributes_list.weight;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {weight}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'International View',
    header: 'International View',
    cell: ({ row }) => {
      const international = row.original.attributes_list.international_active;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {international === true
            ? 'Enabled'
            : international === false
            ? 'Disabled'
            : ''}
        </Link>
      );
    },
    enableSorting: false,
  },

  {
    accessorKey: 'Gtin',
    header: 'Gtin',
    cell: ({ row }) => {
      const gtin = row.original.attributes_list?.gtin;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {gtin}
        </Link>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Demo Available',
    header: 'Demo Available',
    cell: ({ row }) => {
      const demo = row.original.attributes_list?.demo_available;
      const id = row.original.id;
      return (
        <Link to={`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${id}`}>
          {demo === true ? 'Enabled' : demo === false ? 'Disabled' : ''}
        </Link>
      );
    },
    enableSorting: false,
  },

  {
    accessorKey: 'Categories',
    header: 'Categories',
    cell: ({ row }) => {
      const category = row.original.category_associated;
      const [isOpen, setIsOpen] = useState(false);
      const id = row.original.id;
      return (
        <>
          <div>
            {category.length > 0 ? (
              <div
                style={{
                  alignItems: 'center',
                  textAlign: 'center',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <span>{category[0]}</span>
                <DownIcon
                  style={{ cursor: 'pointer' }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
            ) : (
              <></>
            )}
          </div>
          {isOpen && (
            <CategoryAssociated
              visible={isOpen}
              setVisible={setIsOpen}
              values={category}
            />
          )}
        </>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'Completion Percentage',
    header: 'Completion Percentage',
    cell: ({ row }) => {
      const percent = row.original.completion_percentage.percentage;
      const completeField = row.original.completion_percentage.complete_fields;
      const inCompleteField =
        row.original.completion_percentage.incomplete_fields;
      const id = row.original.id;
      const [isOpen, setIsOpen] = useState(false);

      return (
        <div
          style={{ cursor: 'pointer' }}
          onClick={() => {
            setIsOpen(!isOpen);
          }}
        >
          {percent}%
          {isOpen && (
            <CompletionModal
              visible={isOpen}
              setVisible={setIsOpen}
              completed={completeField}
              incompleted={inCompleteField}
              percent={percent}
            />
          )}
        </div>
      );
    },
    enableSorting: false,
  },
  // {
  //   accessorKey: 'action',
  //   header: 'Action',
  // },
];

export interface RelatedProductColumns {
  id: string;
  thumbnail: string;
  product_name: string;
  status: boolean;
  attribute_set: string;
  sku: string;
  price: string;
  action: string;
}

export const RelatedProductTableColumns: ColumnDef<ProductColumns>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    enableSorting: false,
    cell: () => {
      return (
        <>
          <Row>
            <Col>
              <img src={ThumbnailIcon} alt="thumbnail" />
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'product_name',
    header: 'Name',
    cell: ({ row }) => {
      const name = row.original.attributes_list.name;
      return <>{name}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <>{status ? 'true' : 'false'}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.attributes_list.price;
      return <>{price}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'action',
    header: 'Actions',
    enableSorting: false,
    cell: ({ row }) => {
      const { contextProdData, setContextProdData, setContextUpdateProdData } =
        useProductContext();

      const id = row.original.id;
      const handleClick = () => {
        const updatedRelated = contextProdData.product_links?.related.filter(
          (item) => item.product_id !== id,
        );
        setContextProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            related: updatedRelated ? updatedRelated : [],
          },
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            related: updatedRelated ? updatedRelated : [],
          },
        }));
      };

      return (
        <>
          <RemoveRedIcon style={{ cursor: 'pointer' }} onClick={handleClick} />
        </>
      );
    },
  },
];
export interface UpSellsProductColumns {
  id: string;
  thumbnail: string;
  product_name: string;
  status: boolean;
  attribute_set: string;
  sku: string;
  price: string;
  action: string;
}

export const UpSellsProductTableColumns: ColumnDef<ProductColumns>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: () => {
      return (
        <>
          <Row>
            <Col>
              <img src={ThumbnailIcon} alt="thumbnail" />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'product_name',
    header: 'Name',
    cell: ({ row }) => {
      const name = row.original.attributes_list.name;
      return <>{name}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <>{status ? 'true' : 'false'}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.attributes_list.price;
      return <>{price}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'action',
    header: 'Actions',
    cell: ({ row }) => {
      const { contextProdData, setContextProdData, setContextUpdateProdData } =
        useProductContext();

      const id = row.original.id;
      const handleClick = () => {
        const updatedUpsell = contextProdData.product_links?.upsell.filter(
          (item) => item.product_id !== id,
        );
        setContextProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            upsell: updatedUpsell ? updatedUpsell : [],
          },
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            upsell: updatedUpsell ? updatedUpsell : [],
          },
        }));
      };

      return (
        <>
          <RemoveRedIcon style={{ cursor: 'pointer' }} onClick={handleClick} />
        </>
      );
    },
    enableSorting: false,
  },
];
export interface CrossSellsProductColumns {
  id: string;
  thumbnail: string;
  product_name: string;
  status: boolean;
  attribute_set: string;
  sku: string;
  price: string;
  action: string;
}

export const CrossSellProductTableColumns: ColumnDef<ProductColumns>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: () => {
      return (
        <>
          <Row>
            <Col>
              <img src={ThumbnailIcon} alt="thumbnail" />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'product_name',
    header: 'Name',
    cell: ({ row }) => {
      const name = row.original.attributes_list.name;
      return <>{name}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <>{status ? 'true' : 'false'}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.attributes_list.price;
      return <>{price}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'action',
    header: 'Actions',
    cell: ({ row }) => {
      const { contextProdData, setContextProdData, setContextUpdateProdData } =
        useProductContext();

      const id = row.original.id;
      const handleClick = () => {
        const updatedCrossSell =
          contextProdData.product_links?.crosssell.filter(
            (item) => item.product_id !== id,
          );
        setContextProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            crosssell: updatedCrossSell ? updatedCrossSell : [],
          },
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          product_links: {
            ...prevState.product_links,
            crosssell: updatedCrossSell ? updatedCrossSell : [],
          },
        }));
      };

      return (
        <>
          <RemoveRedIcon style={{ cursor: 'pointer' }} onClick={handleClick} />
        </>
      );
    },
    enableSorting: false,
  },
];

export interface ReviewData {
  id: number;
  customer_name: string;
  nickname: string;
  customer_email: string | null;
  item_name: string;
  item_image: string;
  product_id: number;
  customer_id: number | null;
  rating: number;
  title: string;
  description: string;
  status: string;
  source: string | null;
  admin_user: string | null;
  created_at: string;
  updated_at: string;
  admin_created_at: string | null;
  reviewed_at: string;
  created_by: string;
}

export const ProductReviewsTableColumns: ColumnDef<ReviewData>[] = [
  // {
  //   accessorKey: 'id',
  //   header: 'Id',
  //   enableSorting: false,
  // },
  {
    accessorKey: 'created_at',
    header: 'Created',
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    enableSorting: false,
  },
  {
    accessorKey: 'title',
    header: 'Title',
    enableSorting: false,
  },
  {
    accessorKey: 'nickname',
    header: 'Nickname',
    enableSorting: false,
  },
  {
    accessorKey: 'item_name',
    header: 'Product',
    enableSorting: false,
  },
  {
    accessorKey: 'description',
    header: 'Review',
    enableSorting: false,
  },
  // {
  //   accessorKey: 'visibility',
  //   header: 'Visibilty',
  //   enableSorting: false,
  // },
  // {
  //   accessorKey: 'type',
  //   header: 'Type',
  //   enableSorting: false,
  // },
  // {
  //   accessorKey: 'sku',
  //   header: 'SKU',
  //   enableSorting: false,
  // },
  // {
  //   accessorKey: 'action',
  //   header: 'Actions',
  //   enableSorting: false,
  // },
];

export const GroupedProdTableColumns: ColumnDef<any>[] = [
  {
    accessorKey: '',
    header: 'Id',
    cell: ({ row }) => {
      const id = row.original.product.id;
      return <>{id}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      return (
        <>
          <img src={emptyImage} alt="thumbnail" />
        </>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'name',
    header: 'Product Name',
    cell: ({ row }) => {
      const name = row.original?.name;
      return <>{name}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'type_id',
    header: 'Product Type',
    cell: ({ row }) => {
      const type = row.original?.product?.type_id;
      return <>{type}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const sku = row.original?.product?.sku;
      return <>{sku}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original?.special_price;
      return <>{price}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'quantity',
    header: 'Quantity',
    cell: ({ row }) => {
      const qty = row.original?.inventory?.qty;
      return <>{qty}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'position',
    header: 'Position',
    enableSorting: true,
    cell: ({ row }) => {
      const position = row.original?.position;
      const { contextProdData, setIsSearchChildProduct } = useProductContext();
      const [isOpen, setIsOpen] = useState(false);

      const handleSuccess = () => {
        setIsOpen(false);
        setIsSearchChildProduct(true);
      };

      return (
        <>
          <Input value={position} onClick={() => setIsOpen(true)} />
          {isOpen && (
            <UpdatePositionModal
              close={() => setIsOpen(false)}
              product={row.original}
              onSuccess={handleSuccess}
              isGroupedProduct={true}
              groupProductId={contextProdData.id}
            />
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'action',
    header: 'Actions',
    cell: ({ row }) => {
      const {
        contextProdData,
        setContextProdData,
        setContextUpdateProdData,
        groupAssociate,
        setGroupAssociate,
        groupProdData,
        setGroupProdData,
      } = useProductContext();

      const prodId = row.original?.product?.id;
      const location = useLocation();
      const queryParams = new URLSearchParams(location.search);
      const handleClick = () => {
        if (queryParams.get('id')) {
          const updatedGroupProd =
            contextProdData.product_links?.associated.filter(
              (item) => item.product_id !== prodId,
            );

          const updatedGroupProdData = groupProdData.filter(
            (item) => item.product.id !== prodId,
          );

          setContextProdData((prevState) => ({
            ...prevState,
            contextProdData,
            product_links: {
              ...prevState.product_links,
              associated: updatedGroupProd ? updatedGroupProd : [],
            },
          }));
          setContextUpdateProdData((prevState) => ({
            ...prevState,
            product_links: {
              ...prevState.product_links,
              associated: updatedGroupProd ? updatedGroupProd : [],
            },
          }));
          setGroupProdData(updatedGroupProdData);
        } else {
          const updatedGroupProd = groupAssociate.filter(
            (item) => item.product_id != prodId,
          );

          setGroupAssociate(updatedGroupProd);

          const updatedGroupProdData = groupProdData.filter(
            (item) => item.id !== prodId,
          );

          setGroupProdData(updatedGroupProdData);
        }
      };

      return (
        <>
          <RemoveRedIcon style={{ cursor: 'pointer' }} onClick={handleClick} />
        </>
      );
    },
    enableSorting: false,
  },
];

export const GroupedProductTableColumns: ColumnDef<ProductColumns>[] = [
  {
    accessorKey: 'action',
    header: ({ table }) => {
      const [isChecked, setIsChecked] = useState(false);
      const [isChangeStatus, setIsChangeStatus] = useState(false);
      const [isOpen, setIsOpen] = useState(false);

      const handleToggleDropdown = () => {
        setIsOpen(!isOpen);
      };
      const handleChangeCheckbox = (e: any) => {
        const value = e.target.value;
        if (isChecked) {
          table.toggleAllPageRowsSelected(false);
        } else {
          table.toggleAllPageRowsSelected(true);
        }
        setIsChecked(!isChecked);
      };
      const selectedRows = table
        .getSelectedRowModel()
        .rows.map((item, index) => {
          return item.original.id;
        });

      const handleItemClick = () => {
        setIsOpen(false);
      };
      return (
        <Row>
          <Col style={{ display: 'flex', justifyContent: 'center' }}>
            <CField>
              <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                <Label hidden>Check</Label>
              </Checkbox>
            </CField>
          </Col>
          {/* <Col
            style={{
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'start',
            }}
          >
            <DownIcon onClick={handleToggleDropdown} />
            <DropdownContainer>
              <DropdownContent open={isOpen}>
                <DropdownItem
                  onClick={() => {
                    handleItemClick();
                    setIsChangeStatus(!isChangeStatus);
                  }}
                >
                  Change Status
                </DropdownItem>
              </DropdownContent>
            </DropdownContainer>
          </Col> */}
          {/* {isChangeStatus && (
            <ChangeStatus
              ids={selectedRows}
              visible={isChangeStatus}
              setVisible={setIsChangeStatus}
            />
          )} */}
        </Row>
      );
    },
    enableSorting: false,
    cell: ({ row }) => {
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        row.toggleSelected(checked);
      };

      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <CField>
                <Checkbox
                  checked={row.getIsSelected()}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </CField>
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'id',
    header: 'Id',
    cell: ({ row }) => {
      const id = row.original.id;
      return <>{id}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      const thumbnail = row.original.attributes_list?.thumbnail;
      const id = row.original.id;
      const imageurl = `${thumbnailUrl}/${thumbnail}`;
      return (
        <>
          <img src={emptyImage} alt="thumbnail" />
        </>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'name',
    header: 'Product Name',
    cell: ({ row }) => {
      const name = row.original.attributes_list?.name;
      return <>{name}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'type_id',
    header: 'Product Type',
    cell: ({ row }) => {
      const type_Id = row.original.type_id;
      let productType = '';

      switch (type_Id) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = 'Unknown Product';
      }

      return <>{productType}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const sku = row.original.sku;
      return <>{sku}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'quantity',
    header: 'Quantity',
    cell: ({ row }) => {
      const qty = row.original.inventory_details.qty;
      return <>{qty}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.attributes_list?.price;
      return <>{price}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'manufacturer',
    header: 'Brand',
    cell: ({ row }) => {
      const manu = row.original.attributes_list.manufacturer;
      return <>{manu}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'visibility',
    header: 'Visibility',
    cell: ({ row }) => {
      const visi = row.original.attributes_list.visibility;
      return <>{visi}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'pd_expiry_date',
    header: 'Product Expiry Date',
    cell: ({ row }) => {
      const pdExpiry = row.original.attributes_list.pd_expiry_date;
      const isoDateString = pdExpiry ? new Date(pdExpiry).toISOString() : '';
      return <>{isoDateString ? formatDate(isoDateString) : ''}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'gtin',
    header: 'Gtin',
    cell: ({ row }) => {
      const gtin = row.original.attributes_list.gtin;
      return <>{gtin}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'demo_available',
    header: 'Demo Available',
    cell: ({ row }) => {
      const demo = row.original.attributes_list.demo_available;
      return <>{demo && demo === true ? 'Enabled' : 'Disabled'}</>;
    },
    enableSorting: false,
  },
];

export interface AdvancedPriceColumns {
  group_price: number;
  customer_group: any;
  qty: number;
  price_type: any;
  value: number;
  action: any;
}

export const AdvancedPriceTableColumns: ColumnDef<AdvancedPriceColumns>[] = [
  {
    accessorKey: 'group_price',
    header: 'Price Group',
  },
  {
    accessorKey: 'customer_group',
    header: 'Customer Group',
  },
  {
    accessorKey: 'qty',
    header: 'Quantity',
  },
  {
    accessorKey: 'price_type',
    header: 'Price Type',
  },
  {
    accessorKey: 'value',
    header: 'Price',
  },
  {
    accessorKey: 'action',
    header: 'Action',
  },
];

export interface ExportColumns {
  id: number;
  status: string;
  filename: string;
  report_url: string | null;
  created_at: string;
  updated_at: string;
}

export const ExportTableColumns: ColumnDef<ExportColumns>[] = [
  {
    accessorKey: 'id',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Report Id'}
        </div>
      );
    },
    cell: ({ row }) => {
      const id = row.original.id;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>{id}</div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Status'}
        </div>
      );
    },
    cell: ({ row }) => {
      const status = row.original.status;
      let textColor = '';
      let bgColor = '';
      if (status === 'pending') {
        textColor = '#FF9933';
        bgColor = 'rgba(255, 153, 51, 0.26)';
      } else if (status === 'complete') {
        textColor = '#10864D';
        bgColor = '#EAFFF4';
      } else {
        textColor = 'black';
        bgColor = 'white';
      }
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <div
            style={{
              display: 'flex',
              width: '86px',
              padding: '2px 8px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '4px',
              flexShrink: 0,
              borderRadius: '2px',
              color: textColor,
              backgroundColor: bgColor,
            }}
          >
            {status === 'complete' ? 'Complete' : 'Pending'}
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'filename',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Name'}
        </div>
      );
    },
    cell: ({ row }) => {
      const file = row.original.filename;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>{file}</div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'created_at',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Request Date'}
        </div>
      );
    },
    cell: ({ row }) => {
      const create = row.original.created_at;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {addTimeToISODateTime(create)}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'updated_at',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Complete Date'}
        </div>
      );
    },
    cell: ({ row }) => {
      const update = row.original.updated_at;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {addTimeToISODateTime(update)}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'report_url',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Download'}
        </div>
      );
    },
    cell: ({ row }) => {
      const url = row.original.report_url;
      const handleDownload = (url: any) => {
        const link = document.createElement('a');
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      if (url !== null) {
        return (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Download
              style={{ cursor: 'pointer' }}
              onClick={() => handleDownload(url)}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    enableSorting: false,
  },
];

export interface ImportColumns {
  id: number;
  status: string;
  filename: string;
  original_file_url: string;
  report_url: string;
  created_at: string;
  updated_at: string;
}

export const ImportTableColumns: ColumnDef<ImportColumns>[] = [
  {
    accessorKey: 'id',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Report Id'}
        </div>
      );
    },
    cell: ({ row }) => {
      const id = row.original.id;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>{id}</div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Status'}
        </div>
      );
    },
    cell: ({ row }) => {
      const status = row.original.status;
      let textColor = '';
      let bgColor = '';
      if (status === 'pending') {
        textColor = '#FF9933';
        bgColor = 'rgba(255, 153, 51, 0.26)';
      } else if (status === 'complete') {
        textColor = '#10864D';
        bgColor = '#EAFFF4';
      } else {
        textColor = 'black';
        bgColor = 'white';
      }
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <div
            style={{
              display: 'flex',
              width: '86px',
              padding: '2px 8px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '4px',
              flexShrink: 0,
              borderRadius: '2px',
              color: textColor,
              backgroundColor: bgColor,
            }}
          >
            {status === 'complete' ? 'Complete' : 'Pending'}
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'filename',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Name'}
        </div>
      );
    },
    cell: ({ row }) => {
      const file = row.original.filename;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>{file}</div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'created_at',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Request Date'}
        </div>
      );
    },
    cell: ({ row }) => {
      const create = row.original.created_at;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {addTimeToISODateTime(create)}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'updated_at',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Complete Date'}
        </div>
      );
    },
    cell: ({ row }) => {
      const update = row.original.updated_at;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {addTimeToISODateTime(update)}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'original_file_url',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Uploaded File'}
        </div>
      );
    },
    cell: ({ row }) => {
      const url = row.original.original_file_url;
      const handleDownload = (url: any) => {
        const link = document.createElement('a');
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      if (url !== null) {
        return (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Download
              style={{ cursor: 'pointer' }}
              onClick={() => handleDownload(url)}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    enableSorting: false,
  },
  {
    accessorKey: 'failed_product_ids',
    header: () => {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {'Failed Product Ids'}
        </div>
      );
    },
    cell: ({ row }) => {
      const url = row.original.report_url;

      const handleDownloadCsv = (url: any) => {
        const link = document.createElement('a');
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      if (url !== null) {
        return (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Download
              style={{ cursor: 'pointer' }}
              onClick={() => handleDownloadCsv(url)}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    enableSorting: false,
  },
];

interface Change {
  old_value: any;
  new_value: any;
}

interface Changes {
  [key: string]: Change | { [subKey: string]: Change };
}

export interface LogColumns {
  id: number;
  created_at: string;
  updated_at: string;
  admin_identifier: string;
  activity_type: string;
  revert: boolean;
  revert_log_id: number | null;
  user_meta_info: string | null;
  old_value: string;
  new_value: string;
  changes: Changes;
}

export const LogTableColumns: ColumnDef<LogColumns>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
  },
  {
    accessorKey: 'created_at',
    header: 'Date',
    cell: ({ row }) => {
      const date = row.original.created_at;
      return <>{addTimeToISODateTime(date)}</>;
    },
  },
  {
    accessorKey: 'admin-identifier',
    header: 'User Email ID',
  },
  {
    accessorKey: 'activity_type',
    header: 'Activity Type',
  },
  {
    accessorKey: 'revert_log_id',
    header: 'Revert Log Id',
  },
  {
    accessorKey: 'revert',
    header: 'Revert',
    cell: ({ row }) => {
      const revert = row.original.revert;
      let textColor = '';
      let bgColor = '';
      if (revert === false) {
        textColor = '#FF9933';
        bgColor = 'rgba(255, 153, 51, 0.26)';
      } else if (revert === true) {
        textColor = '#10864D';
        bgColor = '#EAFFF4';
      } else {
        textColor = 'black';
        bgColor = 'white';
      }
      return (
        <>
          <div
            style={{
              display: 'flex',
              width: '86px',
              padding: '2px 8px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '4px',
              flexShrink: 0,
              borderRadius: '60px',
              color: textColor,
              backgroundColor: bgColor,
            }}
          >
            {revert === true ? 'Successful' : 'Revert'}
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'task',
    header: 'Action',
    cell: ({ row }) => {
      const [isOpen, setIsOpen] = useState(false);
      const changes = row.original.changes;
      const identifier = row.original.admin_identifier;
      const logid = row.original.id;
      const revertType = row.original.activity_type;
      return (
        <>
          <div style={{ cursor: 'pointer' }} onClick={() => setIsOpen(!isOpen)}>
            View
          </div>
          {isOpen && logid && (
            <ActivityLog
              identifier={identifier}
              revertId={logid}
              type={revertType}
              changes={changes}
              visible={isOpen}
              setVisible={setIsOpen}
            />
          )}
        </>
      );
    },
  },
];

interface CatalogProductFlatRelations {
  id: number;
  name: string;
}

interface Product {
  id: number;
  sku: string;
  catalogProductFlatRelations: CatalogProductFlatRelations;
}

export interface StockAlertColumns {
  id: number;
  customer_name: string;
  notification_status: boolean;
  customer_email: string;
  created_at: string;
  updated_at: string;
  name: string;
  sku: string;
}

export const StockAlertTableColumns: ColumnDef<StockAlertColumns>[] = [
  {
    accessorKey: 'created_at',
    header: 'Date Added',
    cell: ({ row }) => {
      const created = row.original.created_at;

      return <>{format(new Date(created), 'dd/MM/yyyy hh:mm aa')}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'Product SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'name',
    header: 'Product Name',
    enableSorting: false,
  },
  {
    accessorKey: 'customer_name',
    header: 'Customer Name ',
    enableSorting: false,
  },
  {
    accessorKey: 'customer_email',
    header: 'Customer Email',
    enableSorting: false,
  },
];

export interface UpdateSKUColumns {
  id: number;
  old_sku: string;
  new_sku: string;
  status: boolean;
  error: string;
  modified_by: number;
  created_at: string;
  updated_at: string;
}

export const UpdateSKUTableColumns: ColumnDef<UpdateSKUColumns>[] = [
  // {
  //   accessorKey: 'id',
  //   header: 'Id',
  // },
  {
    accessorKey: 'old_sku',
    header: 'Old SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      let textColor = '';
      let bgColor = '';
      if (status === false) {
        textColor = '#FF9933';
        bgColor = 'rgba(255, 153, 51, 0.26)';
      } else if (status === true) {
        textColor = '#10864D';
        bgColor = '#EAFFF4';
      } else {
        textColor = 'black';
        bgColor = 'white';
      }
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <div
            style={{
              display: 'flex',
              width: '86px',
              padding: '2px 8px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '4px',
              flexShrink: 0,
              borderRadius: '2px',
              color: textColor,
              backgroundColor: bgColor,
            }}
          >
            {status === true ? 'Complete' : 'Fail'}
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'new_sku',
    header: 'New SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'error',
    header: 'Error',
    enableSorting: false,
  },
  {
    accessorKey: 'modified_by',
    header: 'Modified By',
    enableSorting: false,
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: ({ row }) => {
      const created = row.original.created_at;

      return <>{format(new Date(created), 'dd/MM/yyyy hh:mm aa')}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated Date',
    cell: ({ row }) => {
      const updated = row.original.updated_at;

      return <>{format(new Date(updated), 'dd/MM/yyyy hh:mm aa')}</>;
    },
    enableSorting: false,
  },
];

export interface AttachmentColumns {
  id: number;
  status: boolean;
  description: string;
  thumbnail: string;
  url: string;
  created_at: string;
  updated_at: string;
}

export const AttachmentTableColumns: ColumnDef<AttachmentColumns>[] = [
  {
    accessorKey: 'key',
    header: ({ table }) => {
      const [isChecked, setIsChecked] = useState(false);
      const [isChangeStatus, setIsChangeStatus] = useState(false);
      const [isDelete, setIsDelete] = useState(false);
      const [statusType, setStatusType] = useState<boolean | undefined>(
        undefined,
      );
      const [isOpen, setIsOpen] = useState(false);
      const dropdownRef = useRef<HTMLDivElement | null>(null);
      const [rotated, setRotated] = useState<any>();
      const handleToggleDropdown = () => {
        setIsOpen(!isOpen);
      };
      const handleChangeCheckbox = (e: any) => {
        const value = e.target.value;
        if (isChecked) {
          table.toggleAllPageRowsSelected(false);
        } else {
          table.toggleAllPageRowsSelected(true);
        }
        setIsChecked(!isChecked);
      };

      const handleItemClick = (item: string) => {
        if (item === 'delete') {
          setIsDelete(true);
        } else if (item === 'enable') {
          setStatusType(true);
          setIsChangeStatus(true);
        } else if (item === 'disable') {
          setStatusType(false);
          setIsChangeStatus(true);
        }
        setIsOpen(false);
      };
      const selectedRows = table
        .getSelectedRowModel()
        .rows.map((item, index) => {
          return item.original.id;
        });

      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target as Node)
          ) {
            setIsOpen(false);
          }
        };

        if (isOpen) {
          document.addEventListener('mousedown', handleClickOutside);
        } else {
          document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isOpen]);
      return (
        <Row>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <CField>
              <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                <Label hidden>Check</Label>
              </Checkbox>
            </CField>
          </Col>
          <Col
            style={{
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'start',
            }}
          >
            <Dropdown
              onSelect={() => {}}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
              }
            >
              <Trigger>
                <div>
                  <DownIcon onClick={handleToggleDropdown} />
                </div>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base100,
                  height: '100px',
                  borderRadius: baseTheme.borderRadii.sm,
                  alignContent: 'center',
                  justifyContent: 'center',
                }}
              >
                <DropDownItemX
                  onClick={() => {
                    handleItemClick('delete');
                  }}
                  // style={{color: 'black', display: 'flex', alignItems:'center', justifyContent: 'center', cursor: 'pointer'}}
                >
                  Delete
                </DropDownItemX>
                <DropDownItemX
                  onClick={() => {
                    handleItemClick('enable');
                  }}
                  // style={{color: 'black', display: 'flex', alignItems:'center', justifyContent: 'center', cursor: 'pointer'}}
                >
                  Enabled
                </DropDownItemX>
                <DropDownItemX
                  onClick={() => {
                    handleItemClick('disable');
                  }}
                  // style={{color: 'black', display: 'flex', alignItems:'center', justifyContent: 'center', cursor: 'pointer'}}
                >
                  Disabled
                </DropDownItemX>
              </Menu>
            </Dropdown>
          </Col>
          {isDelete && (
            <DeleteBulkAttachmentModal
              ids={selectedRows}
              close={() => {
                setIsDelete(false);
              }}
            />
          )}
          {isChangeStatus && (
            <ChangeAttachStatusModal
              ids={selectedRows}
              status={statusType}
              close={() => {
                setIsChangeStatus(false);
              }}
            />
          )}
        </Row>
      );
    },
    enableSorting: false,
    cell: ({ row }) => {
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        row.toggleSelected(checked);
      };

      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <CField>
                <Checkbox
                  checked={row.getIsSelected()}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </CField>
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'action',
    header: 'Action',
    cell: ({ row }) => {
      const id = row.original.id;
      const navigate = useNavigate();
      const [isOpen, setIsOpen] = useState(false);
      const toEditAttachment = () => {
        navigate(`${pageRoutes['GO_TO_CATALOGUE_EDIT_ATTACHMENT']}/${id}`);
      };
      const toDelete = () => {
        setIsOpen(true);
      };

      return (
        <Row justifyContent="center">
          <Col
            style={{ display: 'flex', justifyContent: 'center', gap: '30px' }}
          >
            <RemoveRedIcon style={{ cursor: 'pointer' }} onClick={toDelete} />
            <EditCustomIcon
              style={{ cursor: 'pointer' }}
              onClick={toEditAttachment}
            />
          </Col>
          {isOpen && (
            <DeleteAttachmentModal
              id={id}
              close={() => {
                setIsOpen(false);
              }}
            />
          )}
        </Row>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'id',
    header: () => {
      const { sortOrder, setSortOrder, sortBy, setSortBy } =
        useAttachmentContext();
      const handleIdClick = () => {
        if (sortBy !== 'id') {
          setSortBy('id');
          setSortOrder('desc');
        } else {
          setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Id</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const value = row.original.description;
      return (
        <Row justifyContent="center" alignItems="center">
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base500}>
            <Col
              style={{
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                whiteSpace: 'normal',
              }}
            >
              {value}
            </Col>
          </HeaderDiv>
        </Row>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      const thumbnail = row.original.thumbnail;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <img
            src={
              thumbnail !== '' && thumbnail !== 'none' ? thumbnail : emptyImage
            }
            style={{ width: '60px', height: '60px', borderRadius: '5px' }}
            alt="thumbnail"
          />
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'created_at',
    header: () => {
      const { sortOrder, setSortOrder, sortBy, setSortBy } =
        useAttachmentContext();
      const handleIdClick = () => {
        if (sortBy !== 'created_at') {
          setSortBy('created_at');
          setSortOrder('desc');
        } else {
          setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Created At</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const created = row.original.created_at;
      return <>{format(new Date(created), 'MM/dd/yyyy hh:mm aa')}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'updated_at',
    header: () => {
      const { sortOrder, setSortOrder, sortBy, setSortBy } =
        useAttachmentContext();
      const handleIdClick = () => {
        if (sortBy !== 'updated_at') {
          setSortBy('updated_at');
          setSortOrder('desc');
        } else {
          setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'));
        }
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>Updated At</div>
          <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
        </div>
      );
    },
    cell: ({ row }) => {
      const updated = row.original.updated_at;
      return <>{format(new Date(updated), 'MM/dd/yyyy hh:mm aa')}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <>{status === true ? 'Enabled' : 'Disabled'}</>;
    },
    enableSorting: false,
  },
];
export interface AttachmentProductsColumns {
  id: number;
  sku: string;
  status?: boolean;
  type_id: string;
  catalogProductFlatRelations: {
    name: string;
    price: number;
    thumbnail: string;
    visibility: number;
  };
}

export const AttachmentProductsTableColumns: ColumnDef<AttachmentProductsColumns>[] =
  [
    {
      accessorKey: 'key',
      header: ({ table }) => {
        const [isChecked, setIsChecked] = useState(false);
        const [isChangeStatus, setIsChangeStatus] = useState(false);
        const [isOpen, setIsOpen] = useState(false);
        const [isDelete, setIsDelete] = useState(false);
        const [rotated, setRotated] = useState<any>();
        const dropdownRef = useRef<HTMLDivElement | null>(null);
        const { id } = useParams();
        const [prodId, setProdId] = useState<number | undefined>(undefined);
        const handleToggleDropdown = () => {
          setIsOpen(!isOpen);
        };
        const handleChangeCheckbox = (e: any) => {
          const value = e.target.value;
          if (isChecked) {
            table.toggleAllPageRowsSelected(false);
          } else {
            table.toggleAllPageRowsSelected(true);
          }
          setIsChecked(!isChecked);
        };

        const handleItemClick = () => {
          setIsDelete(true);
        };

        const selectedRows = table
          .getSelectedRowModel()
          .rows.map((item, index) => {
            return item.original.id;
          });

        useEffect(() => {
          const handleClickOutside = (event: MouseEvent) => {
            if (
              dropdownRef.current &&
              !dropdownRef.current.contains(event.target as Node)
            ) {
              setIsOpen(false);
            }
          };

          if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
          } else {
            document.removeEventListener('mousedown', handleClickOutside);
          }

          return () => {
            document.removeEventListener('mousedown', handleClickOutside);
          };
        }, [isOpen]);

        useEffect(() => {
          if (id) {
            setProdId(Number(id));
          }
        }, [id]);
        return (
          <Row>
            <Col style={{ display: 'flex', justifyContent: 'end' }}>
              <CField>
                <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                  <Label hidden>Check</Label>
                </Checkbox>
              </CField>
            </Col>
            <Col
              style={{
                cursor: 'pointer',
                display: 'flex',
                justifyContent: 'start',
              }}
            >
              <Dropdown
                onSelect={() => {}}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
                }
              >
                <Trigger>
                  <div>
                    <DownIcon onClick={handleToggleDropdown} />
                  </div>
                </Trigger>
                <Menu
                  style={{
                    width: baseTheme.components.dimension.width.base100,
                    height: '50px',
                    borderRadius: baseTheme.borderRadii.sm,
                    alignContent: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    onClick={() => {
                      handleItemClick();
                    }}
                    style={{
                      color: 'black',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                    }}
                  >
                    Delete
                  </div>
                </Menu>
              </Dropdown>
            </Col>
            {isDelete && (
              <DeleteBulkAttachProductModal
                prodId={prodId}
                ids={selectedRows}
                close={() => {
                  setIsDelete(false);
                }}
              />
            )}
          </Row>
        );
      },
      enableSorting: false,
      cell: ({ row }) => {
        const handleCheckboxChange = (event: any) => {
          const { checked } = event.target;
          row.toggleSelected(checked);
        };

        return (
          <>
            <Row justifyContent="center" alignItems="center">
              <Col size={4}>
                <CField>
                  <Checkbox
                    checked={row.getIsSelected()}
                    onChange={handleCheckboxChange}
                    aria-label="Select row"
                  >
                    <Label hidden>Accessibly hidden label one</Label>
                  </Checkbox>
                </CField>
              </Col>
            </Row>
          </>
        );
      },
    },
    {
      accessorKey: 'id',
      header: () => {
        const {
          attachSortBy,
          setAttachSortBy,
          attachSortOrder,
          setAttachSortOrder,
        } = useAttachmentContext();
        const handleIdClick = () => {
          if (attachSortBy !== 'id') {
            setAttachSortBy('id');
            setAttachSortOrder('DESC');
          } else {
            setAttachSortOrder((prevOrder: any) =>
              prevOrder === 'ASC' ? 'DESC' : 'ASC',
            );
          }
        };
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>ID</div>
            <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      header: 'Product Name',
      cell: ({ row }) => {
        const val = row.original.catalogProductFlatRelations?.name;
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base300}>
            {val}
          </HeaderDiv>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'sku',
      header: 'SKU',
      cell: ({ row }) => {
        const val = row.original.sku;
        return <>{val}</>;
      },
      enableSorting: false,
    },
    {
      accessorKey: 'price',
      header: () => {
        const { attachSortBy, setAttachSortBy, setAttachSortOrder } =
          useAttachmentContext();
        const handleIdClick = () => {
          if (attachSortBy !== 'price') {
            setAttachSortBy('price');
            setAttachSortOrder('DESC');
          } else {
            setAttachSortOrder((prevOrder: any) =>
              prevOrder === 'ASC' ? 'DESC' : 'ASC',
            );
          }
        };
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>Price</div>
            <SortArrow style={{ cursor: 'pointer' }} onClick={handleIdClick} />
          </div>
        );
      },
      cell: ({ row }) => {
        const val = row.original.catalogProductFlatRelations?.price;
        return <>{val}</>;
      },
      enableSorting: false,
    },
    {
      accessorKey: 'visibility',
      header: 'Visibility',
      cell: ({ row }) => {
        const val = row.original.catalogProductFlatRelations?.visibility;
        return <>{val}</>;
      },
      enableSorting: false,
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        return <>{status === true ? 'Enabled' : 'Disabled'}</>;
      },
      enableSorting: false,
    },
    {
      accessorKey: 'type_id',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.original.type_id;
        let productType = '';

        switch (type) {
          case 'simple':
            productType = 'Simple Product';
            break;
          case 'grouped':
            productType = 'Grouped Product';
            break;
          case 'virtual':
            productType = 'Virtual Product';
            break;
          default:
            productType = 'Unknown Product';
        }

        return <>{productType}</>;
      },
      enableSorting: false,
    },
  ];

export interface TagColumns {
  id: number;
  status: boolean;
  name: string;
  unique_code: string;
  value: string;
  tag_type: string;
  position: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export const TagTableColumns: ColumnDef<TagColumns>[] = [
  {
    accessorKey: 'key',
    header: ({ table }) => {
      const [isChecked, setIsChecked] = useState(false);
      const [isChangeStatus, setIsChangeStatus] = useState(false);
      const [isOpen, setIsOpen] = useState(false);
      const [isDelete, setIsDelete] = useState(false);
      const [statusType, setStatusType] = useState<boolean | undefined>(
        undefined,
      );
      const handleToggleDropdown = () => {
        setIsOpen(!isOpen);
      };
      const handleChangeCheckbox = (e: any) => {
        const value = e.target.value;
        if (isChecked) {
          table.toggleAllPageRowsSelected(false);
        } else {
          table.toggleAllPageRowsSelected(true);
        }
        setIsChecked(!isChecked);
      };

      const selectedRows = table
        .getSelectedRowModel()
        .rows.map((item, index) => {
          return item.original.id;
        });

      const handleItemClick = (item: string) => {
        if (item === 'delete') {
          setIsDelete(true);
        } else if (item === 'enable') {
          setStatusType(true);
          setIsChangeStatus(true);
        } else if (item === 'disable') {
          setStatusType(false);
          setIsChangeStatus(true);
        }
        setIsOpen(false);
      };
      return (
        <Row>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <CField>
              <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                <Label hidden>Check</Label>
              </Checkbox>
            </CField>
          </Col>
          <Col
            style={{
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'start',
            }}
          >
            <DownIcon onClick={handleToggleDropdown} />
            <DropdownContainer>
              <DropdownContent open={isOpen}>
                <DropdownItem onClick={() => handleItemClick('delete')}>
                  Delete
                </DropdownItem>
                <DropdownItem onClick={() => handleItemClick('enable')}>
                  Enable
                </DropdownItem>
                <DropdownItem onClick={() => handleItemClick('disable')}>
                  Disabled
                </DropdownItem>
              </DropdownContent>
            </DropdownContainer>
          </Col>
          {isDelete && (
            <DeleteBulkTagModal
              ids={selectedRows}
              close={() => {
                setIsDelete(false);
              }}
            />
          )}
          {isChangeStatus && (
            <ChangeTagStatusModal
              ids={selectedRows}
              status={statusType}
              close={() => {
                setIsChangeStatus(false);
              }}
            />
          )}
        </Row>
      );
    },
    enableSorting: false,
    cell: ({ row }) => {
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        row.toggleSelected(checked);
      };
      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <CField>
                <Checkbox
                  checked={row.getIsSelected()}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </CField>
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'action',
    header: 'Action',
    cell: ({ row }) => {
      const id = row.original.id;
      const navigate = useNavigate();
      const toEditAttachment = () => {
        navigate(`${pageRoutes['GO_TO_CATALOGUE_EDIT_TAG']}/${id}`);
      };
      const [isOpen, setIsOpen] = useState(false);
      const handleDelete = () => {
        setIsOpen(true);
      };
      return (
        <Row justifyContent="center">
          <Col
            style={{ display: 'flex', justifyContent: 'center', gap: '30px' }}
          >
            <RemoveRedIcon
              style={{ cursor: 'pointer' }}
              onClick={handleDelete}
            />{' '}
            <EditCustomIcon
              style={{ cursor: 'pointer' }}
              onClick={toEditAttachment}
            />
          </Col>
          {isOpen && (
            <DeleteTagModal
              id={id}
              close={() => {
                setIsOpen(false);
              }}
            />
          )}
        </Row>
      );
    },
  },
  {
    accessorKey: 'id',
    header: 'Tag Id',
  },
  {
    accessorKey: 'value',
    header: 'Image Thumbnail',
    cell: ({ row }) => {
      const thumbnail = row.original.value;
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <img
            src={thumbnail.includes('https') ? thumbnail : emptyImage}
            style={{ width: '60px', height: '60px', borderRadius: '5px' }}
            alt="thumbnail"
          />
        </div>
      );
    },
  },
  {
    accessorKey: 'tag_type',
    header: 'Tag Type',
    cell: ({ row }) => {
      const val = row.original.tag_type;
      return <>{val === 'image' ? 'Image' : 'Text'}</>;
    },
  },
  {
    accessorKey: 'unique_code',
    header: 'Identifier',
  },
  {
    accessorKey: 'position',
    header: 'Position',
    cell: ({ row }) => {
      const value = row.original.position;
      return <>{value === 'top_left' ? 'Top Left' : 'Bottom Right'}</>;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const val = row.original.status;
      return <>{val === true ? 'Enabled' : 'Disabled'}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const value = row.original.created_at;
      return <>{format(new Date(value), 'dd/MM/yyyy hh:mm aa')}</>;
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: ({ row }) => {
      const value = row.original.updated_at;
      return <>{format(new Date(value), 'dd/MM/yyyy hh:mm aa')}</>;
    },
  },
];

export interface TagProductsColumns {
  id: number;
  sku: string;
  status: boolean;
  type_id: string;
  catalogProductFlatRelations: {
    name: string;
    price: number;
    thumbnail: string;
    visibility: number;
  };
}

export const TagProductTableColumns: ColumnDef<TagProductsColumns>[] = [
  {
    accessorKey: 'key',
    header: ({ table }) => {
      const { id } = useParams();
      const [isChecked, setIsChecked] = useState(false);
      const [isDelete, setIsDelete] = useState(false);
      const [isOpen, setIsOpen] = useState(false);
      const [prodId, setProdId] = useState<number | undefined>(undefined);

      const handleToggleDropdown = () => {
        setIsOpen(!isOpen);
      };
      const handleChangeCheckbox = (e: any) => {
        const value = e.target.value;
        if (isChecked) {
          table.toggleAllPageRowsSelected(false);
        } else {
          table.toggleAllPageRowsSelected(true);
        }
        setIsChecked(!isChecked);
      };

      const handleItemClick = () => {
        setIsDelete(true);
      };

      const [rotated, setRotated] = useState<boolean | undefined>();

      const selectedRows = table
        .getSelectedRowModel()
        .rows.map((item, index) => {
          return item.original.id;
        });

      useEffect(() => {
        if (id) {
          setProdId(Number(id));
        }
      }, [id]);
      return (
        <Row>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <CField>
              <Checkbox checked={isChecked} onChange={handleChangeCheckbox}>
                <Label hidden>Check</Label>
              </Checkbox>
            </CField>
          </Col>
          <Col
            style={{
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'start',
            }}
          >
            {/* <DownIcon onClick={handleToggleDropdown} />
            <DropdownContainer>
              <DropdownContent open={isOpen}>
                <DropdownItem onClick={() => handleItemClick()}>
                  Delete
                </DropdownItem>
              </DropdownContent>
            </DropdownContainer> */}
            <Dropdown
              onSelect={() => {}}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
              }
            >
              <Trigger>
                <div>
                  <DownIcon onClick={handleToggleDropdown} />
                </div>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base100,
                  height: '50px',
                  borderRadius: baseTheme.borderRadii.sm,
                  alignContent: 'center',
                  justifyContent: 'center',
                }}
              >
                <div
                  onClick={() => {
                    handleItemClick();
                  }}
                  style={{
                    color: 'black',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                  }}
                >
                  Delete
                </div>
              </Menu>
            </Dropdown>
          </Col>
          {isDelete && (
            <DeleteBulkTagProductModal
              prodId={prodId}
              ids={selectedRows}
              close={() => {
                setIsDelete(false);
              }}
            />
          )}
        </Row>
      );
    },
    enableSorting: false,
    cell: ({ row }) => {
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        row.toggleSelected(checked);
      };

      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <CField>
                <Checkbox
                  checked={row.getIsSelected()}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </CField>
            </Col>
          </Row>
        </>
      );
    },
  },
  {
    accessorKey: 'id',
    header: 'ID',
    enableSorting: false,
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => {
      const value = row.original?.catalogProductFlatRelations?.name;
      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base400}>
          {value && value != '' ? value : <Tag>Not Provided</Tag>}
        </HeaderDiv>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    enableSorting: false,
  },
  {
    accessorKey: 'visibility',
    header: 'Visibility',
    cell: ({ row }) => {
      const value = row.original?.catalogProductFlatRelations?.visibility;
      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base400}>
          {value ? value : <Tag>Not Provided</Tag>}
        </HeaderDiv>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const val = row.original.status;
      return <>{val === true ? 'Enabled' : 'Disabled'}</>;
    },
    enableSorting: false,
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const value = row.original?.catalogProductFlatRelations?.price;
      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base400}>
          {value ? value : <Tag>Not Provided</Tag>}
        </HeaderDiv>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'type_id',
    header: 'Tag Type',
    cell: ({ row }) => {
      const type = row.original.type_id;
      let productType = '';

      switch (type) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = 'Unknown Product';
      }

      return <>{productType}</>;
    },
    enableSorting: false,
  },
];

export interface ProductFeedbackColumns {
  id: number;
  sku: string;
  name: string;
  quality: any;
  price: number;
  other_feedback: any;
  user: any;
}
export const ProductFeedbackTableColumns: ColumnDef<ProductFeedbackColumns>[] =
  [
    {
      accessorKey: 'id',
      header: 'Product Id',
      cell: ({ row }) => {
        const value = row.original.id;
        return (
          <Row justifyContent="center" alignItems="center">
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base100}
            >
              <Col
                style={{
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                }}
              >
                {value}
              </Col>
            </HeaderDiv>
          </Row>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'sku',
      header: 'Product SKU',
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      header: 'Product Name',
      enableSorting: false,
      cell: ({ row }) => {
        const value = row.original.name;
        return (
          <Row justifyContent="center" alignItems="center">
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base300}
            >
              <Col
                style={{
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                }}
              >
                {value}
              </Col>
            </HeaderDiv>
          </Row>
        );
      },
    },
    {
      accessorKey: 'quality',
      header: 'Product Quality',
      enableSorting: false,
      cell: ({ row }) => {
        const value: string = row.getValue(`quality`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {value || value != '' ? value : <Tag>Not Provided</Tag>}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'price',
      header: 'Product Price',
      enableSorting: false,
      cell: ({ row }) => {
        const value: string = row.getValue(`price`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base250}>
            {value || value != '' ? value : <Tag>Not Provided</Tag>}
          </HeaderDiv>
        );
      },
    },
    {
      accessorKey: 'other_feedback',
      header: 'Other Feedback',
      enableSorting: false,
      cell: ({ row }) => {
        const value = row.original.other_feedback;
        return (
          <Row justifyContent="center" alignItems="center">
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base400}
            >
              <Col
                style={{
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                }}
              >
                {value}
              </Col>
            </HeaderDiv>
          </Row>
        );
      },
    },
    {
      accessorKey: 'user',
      header: 'User',
      enableSorting: false,
    },
  ];

export interface ProductSuggestionColumns {
  id: number;
  searched_key: string;
  brand: string;
  product_name: string;
  comment: any;
  user: any;
  created_at: string;
}

export const ProductSuggestionTableColumns: ColumnDef<ProductSuggestionColumns>[] =
  [
    {
      accessorKey: 'product_name',
      header: 'Product Name',
      cell: ({ row }) => {
        const value = row.original.product_name;
        return (
          <Row justifyContent="center" alignItems="center">
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base300}
            >
              <Col
                style={{
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                }}
              >
                {value}
              </Col>
            </HeaderDiv>
          </Row>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'searched_key',
      header: 'Searched Key',
      enableSorting: false,
      cell: ({ row }) => {
        const value: string = row.getValue(`searched_key`);
        return <HeaderDiv columnWidth={'80px'}>{value}</HeaderDiv>;
      },
    },
    {
      accessorKey: 'brand',
      header: 'Brand Name',
      enableSorting: false,
    },
    {
      accessorKey: 'comment',
      header: 'Comment',
      cell: ({ row }) => {
        const value = row.original.comment;
        return (
          <Row justifyContent="center" alignItems="center">
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base300}
            >
              <Col
                style={{
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                }}
              >
                {value}
              </Col>
            </HeaderDiv>
          </Row>
        );
      },
      enableSorting: false,
    },

    {
      accessorKey: 'user',
      header: 'User',
      enableSorting: false,
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: ({ row }) => {
        const value: string = row.getValue(`created_at`);
        return (
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base200}>
            {formatDateIntoDDMMYYYYAndTime(value)}
          </HeaderDiv>
        );
      },
    },
  ];
