// ProductFilterContext.tsx

import { useQuery } from '@tanstack/react-query';
import React, {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import { DropOption } from '../ProductFilterContext';
import useAxios from '../../../hooks/useAxios';
import constants from '../../../constants';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';

export interface AttachmentFilterTypes {
  id?: number;
  description?: string;
  from_created_at?: string;
  to_created_at?: string;
  from_updated_at?: string;
  to_updated_at?: string;
  status?: boolean;
  page: number;
  limit: number;
}

export interface AttachmentValue {
  description?: string;
  thumbnail?: string;
  active_device?: string;
  customer_group?: string;
  status?: boolean;
  url?: string;
  id?: number;
  created_at?: string;
  updated_at?: string;
}
interface AttachmentContextProps {
  filters: AttachmentFilterTypes;
  setFilters: React.Dispatch<React.SetStateAction<AttachmentFilterTypes>>;
  attachmentValue: AttachmentValue;
  setAttachmentValue: React.Dispatch<React.SetStateAction<AttachmentValue>>;
  newAttachmentValue: AttachmentValue;
  setNewAttachmentValue: React.Dispatch<React.SetStateAction<AttachmentValue>>;
  refetchAttachment: boolean;
  setRefetchAttachment: React.Dispatch<React.SetStateAction<boolean>>;
  refetchAttachProduct: boolean;
  setRefetchAttachProduct: React.Dispatch<React.SetStateAction<boolean>>;
  sortOrder: string;
  setSortOrder: React.Dispatch<React.SetStateAction<string>>;
  sortBy: string;
  setSortBy: React.Dispatch<React.SetStateAction<string>>;
  attachSortBy: any;
  setAttachSortBy: React.Dispatch<React.SetStateAction<any>>;
  attachSortOrder: any;
  setAttachSortOrder: React.Dispatch<React.SetStateAction<any>>;
  visibilityList: string[];
  visibilityData: DropOption[] | undefined;
}

const AttachmentContext = createContext<AttachmentContextProps | undefined>(
  undefined,
);

export const useAttachmentContext = () => {
  const context = useContext(AttachmentContext);

  if (!context) {
    throw new Error(
      'useAttachmentContext must be used within a AttachmentFilterProvider',
    );
  }
  return context;
};

export const AttachmentContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const queryParams = new URLSearchParams(location.search);
  const [filters, setFilters] = useState<AttachmentFilterTypes>({
    page: 1,
    limit: 20,
  });
  const [attachmentValue, setAttachmentValue] = useState<AttachmentValue>(
    {} as AttachmentValue,
  );
  const [newAttachmentValue, setNewAttachmentValue] = useState<AttachmentValue>(
    {} as AttachmentValue,
  );
  const [refetchAttachment, setRefetchAttachment] = useState<boolean>(false);
  const [refetchAttachProduct, setRefetchAttachProduct] = useState(false);
  const [attachSortOrder, setAttachSortOrder] = useState<any>('DESC');
  const [attachSortBy, setAttachSortBy] = useState<any>('id');
  const [sortOrder, setSortOrder] = useState<string>('desc');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [visibilityList, setVisibilityList] = useState<string[]>([]);
  const axios = useAxios();
  const addToast = useToast();

  const {
    isLoading,
    isError,
    error,
    data: visibilityData,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['get-visibility-dropdown-v1-list'],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/products/attributes-options?attributeId=102&sortOrder=ASC`,
        // `/v1/catalog-admin/attributes-options?attributeId=102&sortOrder=ASC`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response?.attributes_options as unknown as DropOption[];
    },
    onError: (err) => {
      addToast('error', `visibility dropdown list error`);
    },
  });

  useEffect(() => {
    if (visibilityData) {
      const visibilityName = visibilityData?.map(
        (item: DropOption) => item.value,
      );
      setVisibilityList(visibilityName);
    }
  }, [visibilityData]);

  return (
    <AttachmentContext.Provider
      value={{
        filters,
        setFilters,
        attachmentValue,
        setAttachmentValue,
        newAttachmentValue,
        setNewAttachmentValue,
        refetchAttachment,
        setRefetchAttachment,
        refetchAttachProduct,
        setRefetchAttachProduct,
        sortOrder,
        setSortOrder,
        sortBy,
        setSortBy,
        attachSortBy,
        attachSortOrder,
        setAttachSortBy,
        setAttachSortOrder,
        visibilityData,
        visibilityList,
      }}
    >
      {children}
    </AttachmentContext.Provider>
  );
};
