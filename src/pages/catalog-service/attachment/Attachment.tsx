import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import AttachmentTable from '../../../components/table/product/AttachmentTable';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { useQuery } from '@tanstack/react-query';
import routes from '../../../constants/routes';
import { baseTheme, colors } from '../../../themes/theme';
import { Button } from '../../../components/UI-components/Button';
import {
  AddIconWhiteBG,
  FilterIcon,
  RefetchIcon,
  ResetIcon,
} from '../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import { pageRoutes } from '../../../components/navigation/RouteConfig';
import styled from 'styled-components';
import SearchInput from '../../../components/search-input/SearchInput';
import { Row, Col } from '../../../components/UI-components/Grid';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import AttachmentFilterDrawer from '../../../components/layouts/catalog-service/attachment/AttachmentFilterDrawer';
import { useAttachmentContext } from './AttachmentContext';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const TableContainer = styled.div`
  overflow-x: auto;
  border-radius: ${baseTheme.components.dimension.width.zero};
  background-color: white;
  height: 78vh;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 74vh;
  }
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

export interface AttachmentFilters {
  id?: number;
  description?: string;
  from_created_at?: string;
  to_created_at?: string;
  from_updated_at?: string;
  to_updated_at?: string;
  status?: boolean;
  page: number;
  limit: number;
}
const Attachment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    refetchAttachment,
    setRefetchAttachment,
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
  } = useAttachmentContext();
  const queryParams = new URLSearchParams(location.search);
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<AttachmentFilters>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    limit: 20,
  });
  const {
    attachmentValue,
    setAttachmentValue,
    newAttachmentValue,
    setNewAttachmentValue,
  } = useAttachmentContext();
  const [currentSearch, setCurrentSearch] = useState<string>();
  const [searchContent, setSearchContent] = useState<string | undefined>('');
  const [count, setCount] = useState<number>(0);
  const axios = useAxios();
  const addToast = useToast();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['attachment-list'],
      queryFn: async (): Promise<any> => {
        return await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/attachment-list`,
          `/v1/catalog-admin/attachments/list`,
          {
            params: {
              ...filters,
              id: searchContent
                ? searchContent
                : filters?.id
                ? filters.id
                : undefined,
              sort_by: sortBy ? sortBy : 'created_at',
              sort_direction: sortOrder ? sortOrder : 'desc',
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              admin_identifier: `${localStorage.getItem('username')}`,
            },
          },
        );
      },
      onError: (err) => {
        addToast('error', 'Error Occured');
        setCurrentSearch('');
      },
      onSuccess: (data) => {
        setCount(data.count);
      },
    });

  const addProduct = () => {
    navigate(`${pageRoutes['GO_TO_CATALOGUE_ADD_ATTACHMENT']}/`);
  };
  const reset = () => {
    setPage(1);
    setFilters({
      page: 1,
      limit: 20,
    });
    setSearchContent('');
    setCurrentSearch && setCurrentSearch('');
  };

  const handleRefetch = () => {
    setSortBy('');
    setSortOrder('');
    refetch();
  };
  const handleReset = () => {
    reset();
  };

  const handleSearch = () => {
    if (searchContent) {
      setCurrentSearch(searchContent);
      refetch();
    }
  };

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev) => ({
        ...prev,
        page: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
    }
  }, [queryParams.get('page')]);

  useEffect(() => {
    refetch();
  }, [filters, refetchAttachment, sortBy, sortOrder]);

  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Attachment',
      breadcrumbParent: '',
    });
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
    setAttachmentValue({});
  }, []);
  return (
    <div>
      <TopBarDiv>
        <Row>
          <Col size={3}>
            <SearchInput
              searchContent={searchContent}
              setSearchContent={(val: any) => {
                if (/^\d*$/.test(val)) {
                  setSearchContent(val);
                }
              }}
              handleSearch={handleSearch}
              placeholder="Search by Id"
            />
          </Col>
          <Col
            style={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <>
              <Button
                size="medium"
                isAction
                onClick={() => {
                  setIsOpen(true);
                }}
              >
                <Button.StartIcon>
                  <FilterIcon />
                </Button.StartIcon>
                <Span>Filter</Span>
              </Button>
            </>
            <>
              <Button size="medium" isAction onClick={handleReset}>
                <Button.StartIcon>
                  <ResetIcon />
                </Button.StartIcon>
                <Span>Reset Filter</Span>
              </Button>
            </>
            <>
              <Button size="medium" isAction onClick={handleRefetch}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
            </>
            <>
              <Button
                isPrimary
                onClick={() => {
                  addProduct();
                }}
              >
                <Button.StartIcon>
                  <AddIconWhiteBG />
                </Button.StartIcon>
                Add New Attachment
              </Button>
            </>
          </Col>
        </Row>
      </TopBarDiv>
      <Container>
        <TableContainer>
          {isLoading || isRefetching || isFetching ? (
            <LazyLoading />
          ) : (
            <AttachmentTable
              data={data?.attachments ?? []}
              count={count}
              filters={filters}
              setFilters={setFilters}
              page={page}
              setPage={setPage}
            />
          )}
        </TableContainer>
      </Container>
      {isOpen && (
        <AttachmentFilterDrawer
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          setFilters={setFilters}
          filters={filters}
          reset={reset}
        />
      )}
    </div>
  );
};

export default Attachment;
