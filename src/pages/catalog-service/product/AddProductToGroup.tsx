import React, { ReactNode, useEffect, useRef, useState } from 'react';
import TopBar from '../../../components/topbar/TopBar';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { Col, Row } from '../../../components/UI-components/Grid';
import SearchInput from '../../../components/search-input/SearchInput';
import { Button as _Button } from '../../../components/UI-components/Button';
import {
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  RefetchIcon,
  ResetIcon,
  RightArrowIcon,
} from '../../../utils/icons';
import { SM, Span } from '@zendeskgarden/react-typography';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import {
  ProductColumns,
  GroupedProductTableColumns as columns,
} from '../../../components/table/product/Columns';
import { pageRoutes } from '../../../components/navigation/RouteConfig';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../../components/UI-components/Table';
import {
  Autocomplete,
  Dropdown,
  Field,
  Item,
  Menu,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { debounce } from 'lodash';
import { DataTable } from '../../../components/table/product/DataTable';
import NothingToshow from '../../../components/UI-components/NothingToShow';
import { Pagination } from '../../../components/UI-components/Pagination';
import { useProductContext } from '../ProductFilterContext';
import useToast from '../../../hooks/useToast';
import axios from 'axios';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Checkbox, Fieldset, Label } from '@zendeskgarden/react-forms';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import {
  useScreenDefaultWidth,
  useScreenResSize,
} from '../../../hooks/useScreenSize';
import GroupProductFilterDrawer from '../../../components/drawer/catalog-service/GroupProductFilterDrawer';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';
import { Tooltip } from '@zendeskgarden/react-tooltips';

const Button = styled(_Button)`
  margin: 5px 10px;
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;
const TopBarDiv = styled.div`
  /* ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)} */
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

type SearchType = 'name' | 'id' | 'sku' | 'url_key' | '';

const options = ['simple', 'grouped', 'virtual'];
const AddProductToGroup = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const { setHeaderInformation } = useAuth();
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const { id } = useParams();
  const {
    groupFilters,
    setGroupFilters,
    contextProdData,
    contextUpdateProdData,
    setContextProdData,
    setContextUpdateProdData,
    groupAssociate,
    setGroupAssociate,
    detectBrowser,
  } = useProductContext();
  const [isOpen, setIsOpen] = useState(false);
  const [toggleReset, setToggleReset] = useState(true);
  const [isdropdownVisble, setIsDropdownVisible] = useState(false);
  const [data, setReturnData] = useState<ProductColumns[]>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const addToast = useToast();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isFilterDisabled, setIsFilterDisabled] = useState<{
    count: number;
    isdisabled: boolean;
  }>({ count: 0, isdisabled: false });
  const [count, setCount] = useState(0);
  const [rotatedType, setRotatedType] = useState<boolean>();
  const [searchType, setSearchType] = useState<SearchType>('name');
  const isSmallScreen = useScreenDefaultWidth();
  const [searchName, setSearchName] = useState<string>('');
  const [searchId, setSearchId] = useState<any>();
  const [isReset, setIsReset] = useState<boolean>(false);
  const [currentSearch, setCurrentSearch] = useState<string>('');
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });

  const {
    isLoading,
    isError,
    error,
    data: prodData,
    isRefetching,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['get-product-byIds'],
    queryFn: async (): Promise<any> => {
      let productIds: number[] | undefined = undefined;
      if (!(groupFilters.id?.from && groupFilters.id?.to)) {
        productIds = searchId ? [searchId] : [];
      }

      try {
        const response = await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products`,
          `/v1/catalog-admin/list-products`,
          {
            params: {
              id_from: groupFilters?.id?.from,
              id_to: groupFilters?.id?.to,
              status: groupFilters?.status,
              visibility: groupFilters?.visibility,
              manufacturer: groupFilters?.manufacturer,
              page: page,
              size: pageSize,
              search_by_keyword_field:
                searchType === 'id' ||
                searchContent === undefined ||
                searchContent === ''
                  ? undefined
                  : searchType,
              search_by_keyword_value:
                searchType === 'id' || searchType === '' || searchContent === ''
                  ? undefined
                  : searchContent,
              name: groupFilters?.name,
              product_id: searchType === 'id' ? searchContent : undefined,
              // url_key:
              sku: groupFilters?.sku,
              price_from: groupFilters?.price?.from,
              price_to: groupFilters.price?.to,
              product_expiry_from: groupFilters.product_expiry?.from,
              product_expiry_to: groupFilters?.product_expiry?.to,
              backorders: groupFilters?.backorders,
              is_in_stock: groupFilters.is_in_stock,
              demo_available: groupFilters.demo_available,
              min_qty: groupFilters?.quantity?.min,
              max_qty: groupFilters?.quantity?.max,
              gstin: groupFilters?.gtin,
              type_id: 'simple',
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('access-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'application/json',
            },
          },
        );
        return response.data;
      } catch (error) {
        throw new Error('Failed to fetch data');
      }
    },
    onError: (err) => {
      addToast('error', `${err}`);
    },
    onSuccess: (data: any) => {
      setCount(data.item_count);
      setReturnData(data.items);
    },
  });

  const { mutate, isLoading: isUpdateLoading } = useMutation(
    async (dataObject: any) => {
      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}`,
        `/v1/catalog-admin/product/${prodId}`,
        {
          product_links: {
            associated: dataObject,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('access-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
            'admin-identifier': `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (err) => {
        // console.log(err);
        addToast('error', 'Error found on Updating Product.');
      },
      onSuccess() {
        addToast('success', 'Product is successfully added.');
        handleCancel();
      },
    },
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setPageSize(pageSize);
    table.setPageSize(pageSize);
  };

  const [searchContent, setSearchContent] = useState<string | undefined>('');

  const [filtersReset, setFiltersReset] = useState(false);

  const reset = () => {
    setPage(1);
    setSearchContent('');
    setCurrentSearch('');
    setSearchId(undefined);
    setSearchName('');
    setFiltersReset(true);
    table.toggleAllPageRowsSelected(false);
    refetch();
  };

  const getActiveFilterLabels = () => {
    const activeFilters = [];

    if (groupFilters?.id?.from)
      activeFilters.push(`ID From: ${groupFilters.id.from}`);
    if (groupFilters?.id?.to)
      activeFilters.push(`ID To: ${groupFilters.id.to}`);
    if (groupFilters?.name) activeFilters.push(`Name: ${groupFilters.name}`);
    if (groupFilters?.sku) activeFilters.push(`SKU: ${groupFilters.sku}`);
    if (groupFilters?.price?.from)
      activeFilters.push(`Price From: ${groupFilters.price.from}`);
    if (groupFilters?.price?.to)
      activeFilters.push(`Price To: ${groupFilters.price.to}`);
    if (groupFilters?.msrp?.from)
      activeFilters.push(`MSRP From: ${groupFilters.msrp.from}`);
    if (groupFilters?.msrp?.to)
      activeFilters.push(`MSRP To: ${groupFilters.msrp.to}`);
    if (groupFilters?.product_expiry?.from)
      activeFilters.push(`Expiry From: ${groupFilters.product_expiry.from}`);
    if (groupFilters?.product_expiry?.to)
      activeFilters.push(`Expiry To: ${groupFilters.product_expiry.to}`);
    if (groupFilters?.visibility)
      activeFilters.push(`Visibility: ${groupFilters.visibility}`);
    if (groupFilters?.manufacturer)
      activeFilters.push(`Manufacturer: ${groupFilters.manufacturer}`);
    if (groupFilters?.backorders)
      activeFilters.push(
        `Backorders: ${
          groupFilters?.backorders === true
            ? 'Allow Qty Below 0'
            : 'Do Not Allow Qty Below 0'
        }`,
      );
    if (groupFilters?.is_in_stock)
      activeFilters.push(
        `Stock Status: ${
          groupFilters.is_in_stock ? 'In Stock' : 'Out of Stock'
        }`,
      );
    if (groupFilters?.demo_available)
      activeFilters.push(
        `Demo Available: ${
          groupFilters.demo_available ? 'Enabled' : 'Disabled'
        }`,
      );
    if (groupFilters?.quantity?.min)
      activeFilters.push(`Min Quantity: ${groupFilters.quantity.min}`);
    if (groupFilters?.quantity?.max)
      activeFilters.push(`Max Quantity: ${groupFilters.quantity.max}`);
    if (groupFilters?.gtin) activeFilters.push(`GTIN: ${groupFilters.gtin}`);
    if (groupFilters?.type_id)
      activeFilters.push(`Product Type: ${groupFilters.type_id}`);
    if (groupFilters?.status)
      activeFilters.push(
        `Status: ${groupFilters.status ? 'Enabled' : 'Disabled'}`,
      );
    console.log('activeFilters', activeFilters);
    return activeFilters;
  };

  useEffect(() => {
    if (filtersReset) {
      refetch();
      setFiltersReset(false);
    }
  }, [filtersReset]);

  const handleCancel = () => {
    if (prodId) {
      navigate(`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${prodId}`);
    } else {
      navigate(`${pageRoutes['GO_TO_ADD_PRODUCT']}/`);
    }
  };

  const [ischecked, setIsChecked] = useState(false);
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Id',
    'Thumbnail',
    'Product Name',
    'Product Type',
    'Quantity',
    'SKU',
    'Backorder',
    'Brand',
    'Visibility',
    'Reward Coin',
    'Product Expiry Date',
    'Status',
    'Price',
    'Gtin',
    'Demo Available',
    'Stock Status',
    'Categories',
    'Completion Percentage',
  ]);
  const [disabledColumn, setDisableColumn] = useState<string[]>([
    'Id',
    'SKU',
    'Product Name',
    'Product Type',
    'Visibility',
    'Status',
  ]);
  const [initialFetch, setInitialFetch] = useState(true);
  const from = 'group-products';
  const childlength = contextUpdateProdData?.product_links?.associated?.length
    ? contextUpdateProdData?.product_links?.associated?.length
    : contextProdData?.product_links?.associated?.length
    ? contextProdData?.product_links?.associated?.length
    : 1;
  const selectedRows = table.getSelectedRowModel().rows.map((item, index) => {
    return item.original.id;
  });

  const handleAddProd = () => {
    const updatedGroupProduct = selectedRows.map(
      (productId: number, index: number) => ({
        product_id: productId,
        position: index + childlength,
      }),
    );
    setGroupAssociate((prevState) => [...prevState, ...updatedGroupProduct]);
    console.log(updatedGroupProduct, 'updatedGroupProduct');

    const combineProductLists = (existing: any = [], updates: any = []) => {
      return [...existing, ...updates];
    };

    const allIds = contextProdData?.product_links?.associated
      ? combineProductLists(
          contextProdData.product_links.associated,
          updatedGroupProduct,
        )
      : updatedGroupProduct;

    if (allIds.length === 0) {
      addToast('info', 'Please select the Product.');
      return;
    }
    const uniqueIds = Array.from(
      new Set(allIds.map((item: any) => item.product_id)),
    );

    const uniqueAllIds = uniqueIds.map((productId: number, index: number) => ({
      product_id: productId,
      position: index + 1,
    }));

    if (updatedGroupProduct.length > 0) {
      if (prodId) {
        mutate(uniqueAllIds);
      } else {
        addToast('success', 'Product is successfully added.');
        handleCancel();
      }
    } else {
      addToast('info', 'Please Select Products!');
    }
  };

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);
      const newColumns = alreadyEnabledColumn && [
        ...alreadyEnabledColumn,
        header,
      ];
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );
      const newColumns =
        alreadyEnabledColumn &&
        alreadyEnabledColumn.filter((item) => item !== header);
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(false);
    }
  };

  useEffect(() => {
    if (searchName !== '' || searchId !== undefined) {
      refetch();
    }
  }, [searchName, searchId]);

  useEffect(() => {
    // console.log(groupFilters, 'groupFilters');
    if (!initialFetch) {
      refetch();
    }
    table.toggleAllPageRowsSelected(false);
  }, [groupFilters]);

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setPage(Number(queryParams.get('page')));
    } else if (Number(queryParams.get('page')) === 0) {
      setPage(1);
    }
    table.toggleAllPageRowsSelected(false);
  }, [queryParams.get('page')]);

  useEffect(() => {
    refetch();
  }, [prodId, id, pageSize]);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
    refetch();
  }, [queryParams.get('id')]);

  const handleSearch = () => {
    if (searchContent) {
      setPage(1);
      // if (searchType == 'id') {
      //   setSearchName('');
      //   setSearchId(searchContent);
      // } else {
      //   setSearchId(undefined);
      //   setSearchName(searchContent.trim());
      // }
      setCurrentSearch(searchContent.trim());
    }
    refetch();
  };

  useEffect(() => {
    setHeaderInformation({
      title: 'Add Products To Group',
      breadcrumbParent: '',
    });
    setInitialFetch(false);
    table.setPageSize(Number(pageSize));
    const metaData = detectBrowser();
    setUserData(metaData);
    table.setPageSize(Number(pageSize));
  }, []);

  const searchOptions = [
    {
      label: 'ID',
      value: 'id',
    },
    {
      label: 'Name',
      value: 'name',
    },
    {
      label: 'SKU',
      value: 'sku',
    },
    {
      label: 'URL Key',
      value: 'url_key',
    },
  ];

  useEffect(() => {
    console.log(isFilterDisabled, 'isFilterDisabled');
  }, [isFilterDisabled]);
  return (
    <div>
      <TopBarDiv>
        <Row justifyContent="between" alignItems="center">
          <Col size={6}>
            {prodId && (
              <Row>
                <Col style={{ color: `${baseTheme.colors.deepBlue}` }}>
                  <span>
                    Product Name -{' '}
                    {localStorage.getItem('groupedId') != 'undefined'
                      ? localStorage.getItem('groupedId')
                      : ''}
                  </span>
                </Col>
              </Row>
            )}
            <Row alignItems="center">
              <Col>
                <SearchInput
                  isLabelHidden
                  searchContent={searchContent}
                  setSearchContent={setSearchContent}
                  placeholder={'search by Keyword'}
                  handleSearch={handleSearch}
                  label=""
                />
              </Col>
              <Col>
                {searchOptions && (
                  <Col offset={0.5} size={2}>
                    <>
                      <Dropdown
                        onSelect={(item) => setSearchType(item)}
                        onStateChange={(options) =>
                          Object.hasOwn(options, 'isOpen') &&
                          setRotatedType(options.isOpen)
                        }
                      >
                        <Trigger>
                          <Button
                            style={{
                              transform: isSmallScreen
                                ? 'translateX(-30px)'
                                : 'translateX(0)',
                            }}
                            isPrimary
                          >
                            {searchOptions.map((option: any) => {
                              return option.value === searchType
                                ? option.label
                                : null;
                            })}
                            <Button.EndIcon isRotated={rotatedType}>
                              <DownIcon
                                style={{
                                  height: baseTheme.iconSizes.md,
                                  width: baseTheme.iconSizes.md,
                                }}
                              />
                            </Button.EndIcon>
                          </Button>
                        </Trigger>
                        <Menu
                          style={{
                            width: baseTheme.components.dimension.width.base200,
                            transform: 'translateX(4px)',
                            borderRadius: baseTheme.borderRadii.lg,
                          }}
                        >
                          {searchOptions.map((option: any) => (
                            <Item key={option.value} value={option.value}>
                              {option.label}
                            </Item>
                          ))}
                        </Menu>
                      </Dropdown>
                    </>
                  </Col>
                )}
              </Col>
            </Row>
          </Col>
          <Col>
            <Row justifyContent="end">
              <Button isOrange isPrimary onClick={handleAddProd}>
                <Span>Add selected Products</Span>
              </Button>
              <Button onClick={handleCancel}>Cancel</Button>
            </Row>
            <Row justifyContent="end">
              <Tooltip
                style={{
                  backgroundColor: 'white',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #ccc',
                }}
                type="dark"
                size="large"
                placement="bottom-start"
                zIndex={9999}
                content={
                  <div
                    style={{
                      maxWidth: '300px',
                      maxHeight: '200px',
                      overflow: 'auto',
                      color: 'black',
                    }}
                  >
                    {isFilterDisabled.count > 0 ? (
                      <ul style={{ margin: '5px', padding: '5px 20px' }}>
                        {getActiveFilterLabels().map((filter, index) => (
                          <li key={index} style={{ textAlign: 'left' }}>
                            {filter}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <span>No active filters</span>
                    )}
                  </div>
                }
              >
                <Button
                  size="medium"
                  isAction
                  onClick={() => {
                    setIsReset(true);
                  }}
                  disabled={isFilterDisabled.isdisabled}
                >
                  <Button.StartIcon>
                    <ResetIcon />
                  </Button.StartIcon>
                  <Span>
                    Reset Filter
                    {isFilterDisabled.count > 0 && (
                      <Span
                        style={{
                          backgroundColor: `${baseTheme.colors.primaryHue}`,
                          color: 'white',
                          borderRadius: '9999px',
                          padding: '2px 6px',
                          fontSize: '12px',
                          marginLeft: '4px',
                        }}
                      >
                        {isFilterDisabled.count}
                      </Span>
                    )}
                  </Span>
                </Button>
              </Tooltip>
              <Button size="medium" isAction onClick={() => setIsOpen(!isOpen)}>
                <Button.StartIcon>
                  <FilterIcon />
                </Button.StartIcon>
                <Span>Filter</Span>
              </Button>
              <Button size="medium" isAction onClick={() => reset()}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
              {/* <Button>Select Item</Button> */}
              {!isdropdownVisble && table && (
                <>
                  <Dropdown
                    onSelect={(item) => alert(`You planted a ${item}`)}
                    onStateChange={(options) =>
                      Object.hasOwn(options, 'isOpen') &&
                      setRotated(options.isOpen)
                    }
                  >
                    <Trigger>
                      <Button isPrimary>
                        Select Item
                        <Button.EndIcon isRotated={rotated}>
                          <DownIcon
                            style={{
                              height: baseTheme.iconSizes.md,
                              width: baseTheme.iconSizes.md,
                            }}
                          />
                        </Button.EndIcon>
                      </Button>
                    </Trigger>
                    <Menu
                      style={{
                        width: baseTheme.components.dimension.width.base250,
                        transform: 'translateX(4px)',
                        borderRadius: baseTheme.borderRadii.lg,
                        padding: `${baseTheme.paddings.md} ${baseTheme.paddings.md}`,
                      }}
                    >
                      <Fieldset>
                        {table
                          .getAllColumns()
                          .filter(
                            (column) =>
                              column.getCanHide() && column.columnDef.header,
                          )
                          .map((column, index) => {
                            if (index === 0) {
                              return null;
                            }

                            return (
                              <>
                                {!column.columnDef.enableHiding &&
                                  !column.columnDef.enableColumnFilter && (
                                    <Field key={index}>
                                      <Checkbox
                                        disabled={disabledColumn?.includes(
                                          column.columnDef.header as string,
                                        )}
                                        key={column.id}
                                        checked={alreadyEnabledColumn?.includes(
                                          column.columnDef.header as string,
                                        )}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            e,
                                            column,
                                            column.columnDef.header as string,
                                          )
                                        }
                                      >
                                        <Label>
                                          <>
                                            {
                                              column.columnDef
                                                .header as ReactNode
                                            }
                                          </>
                                        </Label>
                                      </Checkbox>
                                    </Field>
                                  )}
                              </>
                            );
                          })}
                      </Fieldset>
                    </Menu>
                  </Dropdown>
                </>
              )}
            </Row>
          </Col>
        </Row>
        <Row justifyContent="end">
          {currentSearch && currentSearch != '' && (
            <SM
              style={{
                marginLeft: baseTheme.space.md,
                marginRight: baseTheme.space.md,
                marginTop: baseTheme.space.xs,
              }}
            >
              You have searched for "{currentSearch}"
            </SM>
          )}
        </Row>
      </TopBarDiv>
      <Container>
        <TableContainer>
          {isLoading || isRefetching || isFetching ? (
            <LazyLoading />
          ) : (
            <>
              <TableHolder>
                {table.getRowModel().rows?.length ? (
                  <DataTable table={table} columns={columns} data={data} />
                ) : (
                  <NothingToshow divHeight="55vh" />
                )}
              </TableHolder>
              {table.getRowModel().rows?.length && count > 0 ? (
                <div style={{ overflowX: 'clip' }}>
                  <Row
                    style={{
                      height: `${
                        baseTheme.components.dimension.width.base * 5
                      }px`,
                      marginTop: baseTheme.space.sm,
                      backgroundColor: baseTheme.colors.white,
                      paddingLeft: baseTheme.space.lg,
                      paddingRight: baseTheme.space.lg,
                    }}
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="center">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={page <= 1 ? true : false}
                          onClick={() => {
                            setGroupFilters((prev: any) => ({
                              ...prev,
                              page: page - 1,
                            }));
                            setPage(page - 1);
                            queryParams.set('page', (page - 1).toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        >
                          <Button.StartIcon>
                            <LeftArrowIcon />
                          </Button.StartIcon>
                          Previous
                        </Button>
                      </Row>
                    </Col>
                    <Col textAlign="center" lg={2} md={2}>
                      <Dropdown
                        onSelect={(item) => handleRowPerPage(item)}
                        onStateChange={(options) =>
                          Object.hasOwn(options, 'isOpen') &&
                          setRotated(options.isOpen)
                        }
                      >
                        <Trigger>
                          <Button size="medium" isAction>
                            Row Per Page:
                            <Span style={{ paddingLeft: baseTheme.space.sm }}>
                              {table.getState().pagination.pageSize}
                            </Span>
                            <Button.EndIcon
                              isRotated={rotated}
                              style={{ marginLeft: 0 }}
                            >
                              <DownIcon />
                            </Button.EndIcon>
                          </Button>
                        </Trigger>
                        <Menu>
                          <Item value={20}>20</Item>
                          <Item value={50}>50</Item>
                          <Item value={100}>100</Item>
                        </Menu>
                      </Dropdown>
                    </Col>
                    <Col lg={5} md={5}>
                      <Row justifyContent="center" alignItems="center">
                        <Pagination
                          color={baseTheme.colors.deepBlue}
                          totalPages={Math.ceil(
                            count / table.getState().pagination.pageSize,
                          )}
                          pagePadding={2}
                          currentPage={page}
                          onChange={(e) => {
                            setGroupFilters((prev: any) => ({
                              ...prev,
                              page: e,
                            }));
                            setPage(e);
                            queryParams.set('page', e.toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        />
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="end">
                        <Button
                          size="medium"
                          isAction
                          style={{ cursor: 'default' }}
                        >
                          {(page - 1) * table.getState().pagination.pageSize +
                            1}
                          -
                          {count < page * table.getState().pagination.pageSize
                            ? count
                            : page * table.getState().pagination.pageSize}{' '}
                          of {count}
                        </Button>
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="end" alignItems="end">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={
                            page >=
                            Math.ceil(
                              count / table.getState().pagination.pageSize,
                            )
                              ? true
                              : false
                          }
                          onClick={() => {
                            setGroupFilters((prev: any) => ({
                              ...prev,
                              page: page + 1,
                            }));
                            setPage(page + 1);
                            queryParams.set('page', (page + 1).toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        >
                          Next
                          <Button.EndIcon>
                            <RightArrowIcon />
                          </Button.EndIcon>
                        </Button>
                      </Row>
                    </Col>
                  </Row>
                </div>
              ) : (
                <>{''}</>
              )}
            </>
          )}
        </TableContainer>
      </Container>

      <GroupProductFilterDrawer
        filters={groupFilters}
        setFilters={setGroupFilters}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        reset={reset}
        isreset={isReset}
        setIsReset={setIsReset}
        setPage={(e) => setPage(e)}
        setIsFilterDisabled={setIsFilterDisabled}
      />
    </div>
  );
};

export default AddProductToGroup;
