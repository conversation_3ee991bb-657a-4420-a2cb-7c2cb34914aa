// ProductFilterContext.tsx

import React, {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import { ProductColumns } from '../../components/table/product/Columns';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useToast from '../../hooks/useToast';
import routes from '../../constants/routes';
import constants from '../../constants';
import krakendPaths from '../../constants/krakendPaths';
import { Item } from '../../components/accordion/catalog-service/GroupedProducts';
import { GroupTierPriceCollection } from '../../components/layouts/catalog-service/productInformation/AdvancePricing';

export interface ProductFilterTypes {
  id?: {
    from?: number;
    to?: number;
  };
  name?: string;
  sku?: string;
  price?: {
    from?: number;
    to?: number;
  };
  msrp?: {
    from?: number;
    to?: number;
  };
  product_expiry?: {
    from?: Date;
    to?: Date;
  };
  visibility?: any;
  manufacturer?: any;
  backorders?: any;
  is_in_stock?: any;
  gtin?: string;
  demo_available?: any;
  quantity?: {
    min?: number;
    max?: number;
  };
  status?: any;
  type_id?: string;
}

interface ProductErrorType {
  selling_price: string;
  price: string;
  compare_at_price: string;
  url_key: string;
}

export interface CategoryData {
  id: number;
  name: string;
  status: boolean;
  meta_title: string;
  meta_description: string;
  banner_web: string;
  banner_app: string;
  banner_title?: string;
  description: string;
  web_link?: string;
  url_key: string;
  meta_keyword: string;
  image: string;
  path: string;
  parent_id: number;
  created_at: string;
  updated_at: string;
}

export interface DropOption {
  id: number;
  value: string;
  created_at: string;
  updated_at: string;
  attribute: {
    id: number;
    code: string;
    label: string;
    apply_to: any;
    is_unique: boolean;
    is_required: boolean;
    is_sortable: boolean;
    is_filterable: boolean;
    is_editable: boolean;
    is_deletable: boolean;
    default_value: string;
    backend_type: string;
    frontend_input: string;
    created_at: string;
    updated_at: string;
  };
}

interface ProductFilterContextProps {
  filters: ProductFilterTypes;
  groupFilters: ProductFilterTypes;
  prodcountrating: number;
  prodcountavg: number;
  setFilters: React.Dispatch<React.SetStateAction<ProductFilterTypes>>;
  setGroupFilters: React.Dispatch<React.SetStateAction<ProductFilterTypes>>;
  setProdCountrating: any;
  setProdCountavg: any;
  contextUpdateProdData: ProductColumns;
  setContextUpdateProdData: React.Dispatch<
    React.SetStateAction<ProductColumns>
  >;
  contextProdData: ProductColumns;
  setContextProdData: React.Dispatch<React.SetStateAction<ProductColumns>>;
  category: string[];
  setCategory: React.Dispatch<React.SetStateAction<string[]>>;
  addgroupId: number[];
  setAddGroupId: React.Dispatch<React.SetStateAction<number[]>>;
  field: string;
  setField: React.Dispatch<React.SetStateAction<string>>;
  order: string;
  setOrder: React.Dispatch<React.SetStateAction<'ASC' | 'DESC' | ''>>;
  page: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  countryList: string[];
  countryData: DropOption[] | undefined;
  manfacturerList: string[];
  manufacturerData: DropOption[] | undefined;
  visibilityList: string[];
  visibilityData: DropOption[] | undefined;
  dispatchDaysList: string[];
  dispatchDaysData: DropOption[] | undefined;
  taxclassList: string[];
  taxClassData: DropOption[] | undefined;
  categoryIds: number[] | [];
  setCategoryIds: React.Dispatch<React.SetStateAction<number[]>>;
  columnList: string[] | [];
  setColumnList: React.Dispatch<React.SetStateAction<string[]>>;
  selectedFileData: {
    id: number;
    value?: string;
    is_disabled?: boolean;
    position?: number;
    image_tags?: string[] | null;
    url?: string;
    title?: string;
    description?: string;
    delete?: boolean;
  } | null;
  setSelectedFileData: React.Dispatch<
    React.SetStateAction<{
      id: number;
      value?: string;
      is_disabled?: boolean;
      position?: number;
      image_tags?: string[] | null;
      url?: string;
      title?: string;
      description?: string;
      delete?: boolean;
    } | null>
  >;
  groupProdName: string;
  setGroupProdName: React.Dispatch<React.SetStateAction<string>>;
  imageTags: string[];
  setImageTags: React.Dispatch<React.SetStateAction<string[]>>;
  isMediaRemove: boolean;
  setIsMediaRemove: React.Dispatch<React.SetStateAction<boolean>>;
  statusIds: number[];
  setStatusIds: React.Dispatch<React.SetStateAction<number[]>>;
  detectBrowser: any;
  redirect: boolean | null;
  setRedirect: React.Dispatch<React.SetStateAction<boolean | null>>;
  groupAssociate: { product_id: number; position: number }[] | [];
  setGroupAssociate: React.Dispatch<
    React.SetStateAction<{ product_id: number; position: number }[] | []>
  >;
  isId: boolean;
  setIsId: React.Dispatch<React.SetStateAction<boolean>>;
  refetchLog: boolean;
  setRefetchLog: React.Dispatch<React.SetStateAction<boolean>>;
  productErrors: ProductErrorType;
  setProductErrors: React.Dispatch<React.SetStateAction<ProductErrorType>>;
  isUpdate: boolean;
  setIsUpdate: React.Dispatch<React.SetStateAction<boolean>>;
  isRevert: boolean;
  setIsRevert: React.Dispatch<React.SetStateAction<boolean>>;
  groupProdData: Item[];
  setGroupProdData: React.Dispatch<React.SetStateAction<Item[]>>;
  updateTierPrice: GroupTierPriceCollection | undefined;
  setUpdateTierPrice: React.Dispatch<
    React.SetStateAction<GroupTierPriceCollection | undefined>
  >;
}

const ProductFilterContext = createContext<
  ProductFilterContextProps | undefined
>(undefined);

export const useProductContext = () => {
  const context = useContext(ProductFilterContext);

  if (!context) {
    throw new Error(
      'useProductFilterContext must be used within a ProductFilterProvider',
    );
  }

  return context;
};

function detectBrowser() {
  const userAgent = navigator.userAgent;
  const platform = navigator.platform;
  let browser = 'Unknown';
  let device = 'Desktop';
  let os = 'Unknown';

  // Detect browser
  if (/chrome|crios|chromium/i.test(userAgent)) {
    browser = 'Chrome';
  } else if (/firefox|fxios/i.test(userAgent)) {
    browser = 'Firefox';
  } else if (
    /safari/i.test(userAgent) &&
    !/chrome|crios|chromium/i.test(userAgent)
  ) {
    browser = 'Safari';
  } else if (/edg/i.test(userAgent)) {
    browser = 'Edge';
  } else if (/opr\//i.test(userAgent)) {
    browser = 'Opera';
  }

  // Detect device type
  if (
    /mobi|android|touch|silk|kindle|opera mini|opera mobi|phone/i.test(
      userAgent,
    )
  ) {
    device = 'Mobile';
  } else if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    device = 'Tablet';
  }

  // Detect operating system
  if (/win/i.test(platform)) {
    os = 'Windows';
  } else if (/mac/i.test(platform)) {
    os = 'macOS';
  } else if (/linux/i.test(platform)) {
    os = 'Linux';
  } else if (/android/i.test(userAgent)) {
    os = 'Android';
  } else if (/ios|iphone|ipad|ipod/i.test(userAgent)) {
    os = 'iOS';
  }

  return { browser, device, os };
}

export const ProductContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const queryParams = new URLSearchParams(location.search);
  const [filters, setFilters] = useState<ProductFilterTypes>({
    id: { from: undefined, to: undefined },
    name: undefined,
    sku: undefined,
    price: { from: undefined, to: undefined },
    product_expiry: { from: undefined, to: undefined },
    manufacturer: undefined,
    backorders: undefined,
    is_in_stock: undefined,
    quantity: { min: undefined, max: undefined },
  });
  const [groupFilters, setGroupFilters] = useState<ProductFilterTypes>({
    id: { from: undefined, to: undefined },
    name: undefined,
    sku: undefined,
    price: { from: undefined, to: undefined },
    product_expiry: { from: undefined, to: undefined },
    manufacturer: undefined,
    backorders: undefined,
    is_in_stock: undefined,
    quantity: { min: undefined, max: undefined },
  });
  const [updateTierPrice, setUpdateTierPrice] = useState<
    GroupTierPriceCollection | undefined
  >(undefined);
  const [isUpdate, setIsUpdate] = useState(false);
  const [productErrors, setProductErrors] = useState<ProductErrorType>({
    selling_price: '',
    price: '',
    compare_at_price: '',
    url_key: '',
  });
  const [page, setPage] = useState(1);
  const [prodcountavg, setProdCountavg] = useState<number>(0);
  const [prodcountrating, setProdCountrating] = useState<number>(0);
  const [addgroupId, setAddGroupId] = useState<number[]>([]);
  const [contextProdData, setContextProdData] = useState<ProductColumns>(
    {} as ProductColumns,
  );
  const [groupProdData, setGroupProdData] = useState<Item[]>({} as Item[]);
  const [contextUpdateProdData, setContextUpdateProdData] =
    useState<ProductColumns>({} as ProductColumns);
  const [searchkeyword, setSearchkeyword] = useState('');
  const [field, setField] = useState<string>('');
  const [order, setOrder] = useState<'ASC' | 'DESC' | ''>('');
  const [imageTags, setImageTags] = useState<string[]>([]);
  const [isMediaRemove, setIsMediaRemove] = useState<boolean>(false);
  const [statusIds, setStatusIds] = useState<number[]>([]);
  const [category, setCategory] = useState<string[]>([]);
  const [countryList, setCountryList] = useState<string[]>([]);
  const [manfacturerList, setManufacturerList] = useState<string[]>([]);
  const [visibilityList, setVisibilityList] = useState<string[]>([]);
  const [dispatchDaysList, setDispatchDaysList] = useState<string[]>([]);
  const [taxclassList, setTaxClassList] = useState<string[]>([]);
  const [redirect, setRedirect] = useState<boolean | null>(null);
  const [selectedFileData, setSelectedFileData] = useState<{
    id: number;
    value?: string;
    is_disabled?: boolean;
    position?: number;
    url?: string;
    title?: string;
    description?: string;
  } | null>(null);
  const [groupAssociate, setGroupAssociate] = useState<
    { product_id: number; position: number }[]
  >([]);
  const [isRevert, setIsRevert] = useState(false);
  const [isId, setIsId] = useState(false);

  const [groupProdName, setGroupProdName] = useState('');

  const [columnList, setColumnList] = useState<string[]>([] as string[]);
  const [categoryIds, setCategoryIds] = useState<number[]>([]);

  const [refetchLog, setRefetchLog] = useState(false);
  const [countryData, setCountryData] = useState<any>();
  const [manufacturerData, setManufacturerData] = useState<any>();
  const [visibilityData, setVisibilityData] = useState<any>();
  const [dispatchDaysData, setDispatchDaysData] = useState<any>();
  const [taxClassData, settaxClassData] = useState<any>();

  const addToast = useToast();

  const { isLoading, isError, error, data, isFetching, refetch } = useQuery({
    queryKey: ['get-dropdown-list'],
    queryFn: async (): Promise<DropOption[]> => {
      try {
        const response = await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/attributes-options?sortOrder=ASC`,
          `/v1/catalog-admin/attributes-options?sortOrder=ASC`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'application/json',
            },
          },
        );
        // return response?.data?.attributes_options;
        return response?.data;
      } catch (error) {
        throw new Error('Failed to fetch data');
      }
    },
    onError: (err) => {
      addToast('error', `dropdown list error`);
    },
  });

  useEffect(() => {
    if (data) {
      const taxClass_Data = data.filter(
        (val: DropOption) => val.attribute.id === 121,
      );
      const taxClassName = taxClass_Data.map((item: DropOption) => item.value);
      settaxClassData(taxClass_Data);
      setTaxClassList(taxClassName);
      const dispatchDays_Data = data.filter(
        (val: DropOption) => val.attribute.id === 243,
      );
      const dispatchDaysName = dispatchDays_Data.map(
        (item: DropOption) => item.value,
      );
      const dispatchDaysOption = ['Select Dispatch Days', ...dispatchDaysName];
      setDispatchDaysData(dispatchDays_Data);
      setDispatchDaysList(dispatchDaysOption);
      const visibility_Data = data.filter(
        (val: DropOption) => val.attribute.id === 102,
      );
      const visibilityName = visibility_Data.map(
        (item: DropOption) => item.value,
      );
      setVisibilityData(visibility_Data);
      setVisibilityList(visibilityName);
      const manufacturer_Data = data.filter(
        (val: DropOption) => val.attribute.id === 81,
      );
      const manufacturerName = manufacturer_Data.map(
        (item: DropOption) => item.value,
      );
      setManufacturerData(manufacturer_Data);
      setManufacturerList(manufacturerName);
      const country_Data = data.filter(
        (val: DropOption) => val.attribute.id === 117,
      );
      const countryName = country_Data.map((item: DropOption) => item.value);
      const countryOptions = ['', ...countryName];
      setCountryData(country_Data);
      setCountryList(countryOptions);
    }
  }, [data]);

  return (
    <ProductFilterContext.Provider
      value={{
        filters,
        setFilters,
        groupFilters,
        setGroupFilters,
        prodcountavg,
        setProdCountavg,
        prodcountrating,
        setProdCountrating,
        contextUpdateProdData,
        setContextUpdateProdData,
        contextProdData,
        setContextProdData,
        category,
        setCategory,
        addgroupId,
        setAddGroupId,
        page,
        setPage,
        categoryIds,
        setCategoryIds,
        countryList,
        countryData,
        manfacturerList,
        visibilityList,
        dispatchDaysList,
        manufacturerData,
        visibilityData,
        dispatchDaysData,
        taxClassData,
        taxclassList,
        columnList,
        setColumnList,
        field,
        setField,
        order,
        setOrder,
        selectedFileData,
        setSelectedFileData,
        groupProdName,
        setGroupProdName,
        imageTags,
        setImageTags,
        isMediaRemove,
        setIsMediaRemove,
        statusIds,
        setStatusIds,
        detectBrowser,
        redirect,
        setRedirect,
        groupAssociate,
        setGroupAssociate,
        isId,
        setIsId,
        refetchLog,
        setRefetchLog,
        productErrors,
        setProductErrors,
        isUpdate,
        setIsUpdate,
        isRevert,
        setIsRevert,
        groupProdData,
        setGroupProdData,
        updateTierPrice,
        setUpdateTierPrice,
      }}
    >
      {children}
    </ProductFilterContext.Provider>
  );
};
