import React, { <PERSON>actNode, useEffect, useState } from 'react';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import routes from '../../../constants/routes';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../../components/UI-components/Table';
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  Visibility,
  VisibilityState,
} from '@tanstack/react-table';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import {
  ProductSuggestionColumns,
  ProductSuggestionTableColumns as columns,
} from '../../../components/table/product/Columns';
import { Col, Row } from '../../../components/UI-components/Grid';
import { Dropdown, Item, <PERSON><PERSON>, Trigger } from '@zendeskgarden/react-dropdowns';
import { Button } from '../../../components/UI-components/Button';
import { Span } from '@zendeskgarden/react-typography';
import {
  DownIcon,
  RefetchIcon,
  RightArrowIcon,
  LeftArrowIcon,
} from '../../../utils/icons';
import { Checkbox, Field, Fieldset, Label } from '@zendeskgarden/react-forms';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import { DataTable } from '../../../components/table/product/DataTable';
import { Pagination } from '../../../components/UI-components/Pagination';
import NothingToshow from '../../../components/UI-components/NothingToShow';
import constants from '../../../constants';
import { useLocation, useNavigate } from 'react-router-dom';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;
const StyledDropdown = styled(Dropdown)`
  margin-right: 10px;
`;

const Suggestions = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    page_size: 20,
  });
  const [count, setCount] = useState(0);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rotated, setRotated] = useState<boolean | undefined>();
  const axios = useAxios();
  const addToast = useToast();
  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-all-products-suggestions'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `https://dental-admin.dentalkart.com/product/suggestion`,
          {
            params: { ...filters },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'admin-identifier': `${localStorage.getItem('username')}`,
            },
          },
        );
        return response;
      },
      onError: (err) => {
        addToast('error', `${err}`);
      },
      onSuccess: (data) => {
        setCount(data.item_count);
        // console.log('count: ',count, data.item_count)
      },
    });
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Product Name',
    'Searched Key',
    'Brand Name',
    'Comment',
    'User',
    'Created At',
  ]);

  const disabledColumn = [''];

  const table = useReactTable({
    data: data?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const from = '';
  const [isChecked, setIsChecked] = useState(false);

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);
      const newColumns = alreadyEnabledColumn && [
        ...alreadyEnabledColumn,
        header,
      ];
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );
      const newColumns =
        alreadyEnabledColumn &&
        alreadyEnabledColumn.filter((item) => item !== header);
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(false);
    }
  };

  const handleRefetch = () => {
    table.toggleAllPageRowsSelected(false);
    refetch();
  };
  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setFilters({
      page_size: pageSize,
      page: 1,
    });
    table.setPageSize(pageSize);
  };
  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev: any) => ({
        ...prev,
        page: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
    }
  }, [queryParams.get('page')]);

  useEffect(() => {
    refetch();
  }, [filters]);

  const { setHeaderInformation } = useAuth();

  useEffect(() => {
    setHeaderInformation({
      title: 'Product Suggestions',
      breadcrumbParent: '',
    });
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
    table.setPageSize(pageSize);
  }, []);
  return isLoading || isRefetching || isFetching ? (
    <LazyLoading />
  ) : (
    <>
      <div style={{ height: '100%' }}>
        <TopBarDiv>
          <Row>
            <Col
              style={{
                display: 'flex',
                gap: '5px',
                justifyContent: 'end',
                alignItems: 'center',
              }}
            >
              <Button size="medium" isAction onClick={handleRefetch}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
              <StyledDropdown>
                <Dropdown
                  onSelect={(item) => alert(`You planted a ${item}`)}
                  onStateChange={(options) =>
                    Object.hasOwn(options, 'isOpen') &&
                    setRotated(options.isOpen)
                  }
                >
                  <Trigger>
                    <Button isPrimary>
                      Select Item
                      <Button.EndIcon isRotated={rotated}>
                        <DownIcon
                          style={{
                            height: baseTheme.iconSizes.md,
                            width: baseTheme.iconSizes.md,
                          }}
                        />
                      </Button.EndIcon>
                    </Button>
                  </Trigger>
                  <Menu
                    style={{
                      width: baseTheme.components.dimension.width.base200,
                      transform: 'translateX(4px)',
                      borderRadius: baseTheme.borderRadii.lg,
                      padding: `${baseTheme.paddings.md} ${baseTheme.paddings.md}`,
                    }}
                  >
                    <Fieldset>
                      {table
                        .getAllColumns()
                        .filter(
                          (column) =>
                            column.getCanHide() && column.columnDef.header,
                        )
                        .map((column: any, index) => {
                          return (
                            <>
                              {!column.columnDef.enableHiding &&
                                !column.columnDef.enableColumnFilter && (
                                  <Field>
                                    <Checkbox
                                      disabled={disabledColumn?.includes(
                                        column.columnDef.header as string,
                                      )}
                                      key={column.id}
                                      checked={alreadyEnabledColumn?.includes(
                                        column.columnDef.header as string,
                                      )}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          e,
                                          column,
                                          column.columnDef.header as string,
                                        )
                                      }
                                    >
                                      <Label>
                                        <>
                                          {column.columnDef.header as ReactNode}
                                        </>
                                      </Label>
                                    </Checkbox>
                                  </Field>
                                )}
                            </>
                          );
                        })}
                    </Fieldset>
                  </Menu>
                </Dropdown>
              </StyledDropdown>
            </Col>
          </Row>
        </TopBarDiv>
        <Container>
          <TableContainer>
            <TableHolder>
              {table.getRowModel().rows?.length ? (
                <DataTable table={table} columns={columns} data={data?.items} />
              ) : (
                <NothingToshow divHeight="55vh" />
              )}
            </TableHolder>
            {table.getRowModel().rows?.length && count > 0 ? (
              <div style={{ overflowX: 'clip' }}>
                <Row
                  style={{
                    height: `${
                      baseTheme.components.dimension.width.base * 5
                    }px`,
                    marginTop: baseTheme.space.sm,
                    backgroundColor: baseTheme.colors.white,
                    paddingLeft: baseTheme.space.lg,
                    paddingRight: baseTheme.space.lg,
                  }}
                  justifyContent="center"
                  alignItems="center"
                >
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="start" alignItems="center">
                      <Button
                        style={{
                          maxWidth:
                            baseTheme.components.dimension.width.base100,
                        }}
                        size="medium"
                        isAction
                        disabled={page <= 1 ? true : false}
                        onClick={() => {
                          setPage(page - 1);
                        }}
                      >
                        <Button.StartIcon>
                          <LeftArrowIcon />
                        </Button.StartIcon>
                        Previous
                      </Button>
                    </Row>
                  </Col>
                  <Col textAlign="center" lg={2} md={2}>
                    <Dropdown
                      onSelect={(item) => handleRowPerPage(item)}
                      onStateChange={(options) =>
                        Object.hasOwn(options, 'isOpen') &&
                        setRotated(options.isOpen)
                      }
                    >
                      <Trigger>
                        <Button size="medium" isAction>
                          Row Per Page:
                          <Span style={{ paddingLeft: baseTheme.space.sm }}>
                            {table.getState().pagination.pageSize}
                          </Span>
                          <Button.EndIcon
                            isRotated={rotated}
                            style={{ marginLeft: 0 }}
                          >
                            <DownIcon />
                          </Button.EndIcon>
                        </Button>
                      </Trigger>
                      <Menu>
                        <Item value={20}>20</Item>
                        <Item value={50}>50</Item>
                        <Item value={100}>100</Item>
                      </Menu>
                    </Dropdown>
                  </Col>
                  <Col lg={5} md={5}>
                    <Row justifyContent="center" alignItems="center">
                      <Pagination
                        color={baseTheme.colors.deepBlue}
                        totalPages={Math.ceil(
                          count / table.getState().pagination.pageSize,
                        )}
                        pagePadding={2}
                        currentPage={page}
                        onChange={(e) => {
                          setPage(e);
                        }}
                      />
                    </Row>
                  </Col>
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="start" alignItems="end">
                      <Button
                        size="medium"
                        isAction
                        style={{ cursor: 'default' }}
                      >
                        {(page - 1) * table.getState().pagination.pageSize + 1}-
                        {count < page * table.getState().pagination.pageSize
                          ? count
                          : page * table.getState().pagination.pageSize}{' '}
                        of {count}
                      </Button>
                    </Row>
                  </Col>
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="end" alignItems="end">
                      <Button
                        style={{
                          maxWidth:
                            baseTheme.components.dimension.width.base100,
                        }}
                        size="medium"
                        isAction
                        disabled={
                          page >=
                          Math.ceil(
                            count / table.getState().pagination.pageSize,
                          )
                            ? true
                            : false
                        }
                        onClick={() => setPage(page + 1)}
                      >
                        Next
                        <Button.EndIcon>
                          <RightArrowIcon />
                        </Button.EndIcon>
                      </Button>
                    </Row>
                  </Col>
                </Row>
              </div>
            ) : (
              <>{''}</>
            )}
          </TableContainer>
        </Container>
      </div>
    </>
  );
};

export default Suggestions;
