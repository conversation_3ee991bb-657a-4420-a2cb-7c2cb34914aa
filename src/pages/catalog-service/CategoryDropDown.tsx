import { useMutation } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import useToast from '../../hooks/useToast';
import useAxios from '../../hooks/useAxios';
import routes from '../../constants/routes';
import SwapOrChangeParentModal from '../../components/modal/catalog-service/SwapOrChangeParentModal';
import constants from '../../constants';
import { baseTheme } from '../../themes/theme';
import { useLocation, useNavigate } from 'react-router-dom';
import krakendPaths from '../../constants/krakendPaths';

export interface MenuItem {
  id: number;
  name: string;
  position: number;
  url_path: string;
  children?: MenuItem[];
}

const MenuWrapper = styled.div`
  max-height: 75vh;
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 4px;
  margin-top: 10px;
  margin-bottom: 20px;

  /* Custom scrollbar for better UX */
  &::-webkit-scrollbar {
    width: 14px;
    height: 14px;
  }

  &::-webkit-scrollbar-track {
    background: #f9f9f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 15px;
    border: 3px solid #fafafa;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #aaa;
  }
`;

const ParentMenu = styled.div<{ hasChildren: boolean; active: boolean }>`
  padding: 10px 12px;
  margin-top: 8px;
  background-color: ${(_) =>
    _.active ? 'rgba(10, 95, 121, 0.15)' : '#f9f9f9'};
  border: 1px solid ${(_) => (_.active ? '#0A5F79' : 'grey')};
  // border-left: 4px solid ${(_) => (_.active ? '#0A5F79' : '#e2e2e2')};
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  box-shadow: ${(_) =>
    _.active
      ? '0 1px 3px rgba(0, 0, 0, 0.1)'
      : '0 1px 2px rgba(0, 0, 0, 0.03)'};

  &:hover {
    background-color: ${(_) =>
      _.active ? 'rgba(10, 95, 121, 0.2)' : '#f2f2f2'};
    border-color: ${(_) => (_.active ? '#0A5F79' : '#cccccc')};
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &::after {
    content: '${(_) => (_.hasChildren ? '▾' : '')}';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%)
      ${(_) => (_.hasChildren && !_.active ? '' : 'rotate(-180deg)')};
    transition: transform 0.3s;
    display: ${(_) => (_.hasChildren ? 'block' : 'none')};
    color: ${(_) => (_.active ? '#0A5F79' : '#666')};
    font-size: 16px;
  }

  &.collapsed::after {
    transform: translateY(-50%) rotate(-180deg);
  }

  span {
    color: ${(_) => (_.active ? '#0A5F79' : '#444')};
    font-weight: ${(_) => (_.active ? '600' : 'normal')};
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: calc(100% - 25px);
  }
`;

interface MenuProps {
  refetch: () => void;
  menuItems: MenuItem[];
  selectedCategory: MenuItem | undefined;
  setselectedCategory: React.Dispatch<
    React.SetStateAction<MenuItem | undefined>
  >;
  paramId: number | undefined;
  setProdId: any;
}

// Container for horizontal scrolling when categories are deeply nested
const NestedCategoryScrollContainer = styled.div`
  overflow-x: auto;
  width: 100%;

  /* Custom scrollbar for horizontal scrolling */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f9f9f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
    border: 2px solid #fafafa;
  }

  &::-webkit-scrollbar-corner {
    background: #f9f9f9;
  }
`;

const MenuContainer = styled.div<{ level?: number }>`
  padding: 4px 0px 4px 16px;
  background: #fafafa;
  cursor: pointer;
  max-height: 250px;
  overflow-y: auto;
  margin-left: 8px;
  border-left: 1px dashed #ccc;
  /* Set min-width to ensure deep nesting levels don't get too narrow */
  min-width: ${(props) =>
    props.level && props.level > 3
      ? `${Math.min(800, 300 + props.level * 50)}px`
      : 'auto'};

  /* Custom scrollbar for better UX */
  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  &::-webkit-scrollbar-track {
    background: #f9f9f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 4px;
    border: 2px solid #fafafa;
  }

  /* Style for deeper nested levels */
  ${(props) =>
    props.level && props.level > 3
      ? `
    border-left: 1px solid #aaa;
  `
      : ''}

  /* Horizontal scrollbar styles */
  &::-webkit-scrollbar-corner {
    background: #f9f9f9;
  }
`;

const MenuItemStyled = styled.div<{
  level: number;
  hasChildren: boolean;
  active: boolean;
}>`
  cursor: pointer;
  padding: 8px 12px;
  margin: 6px 0;
  margin-left: ${(_) => _.level * 8}px;
  background-color: ${(_) =>
    _.active ? 'rgba(10, 95, 121, 0.1)' : 'transparent'};
  border-radius: 3px;
  border-left: 3px solid ${(_) => (_.active ? '#0A5F79' : 'transparent')};
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(_) =>
      _.active ? 'rgba(10, 95, 121, 0.15)' : '#f2f2f2'};
  }

  &::after {
    content: '${(_) => (_.hasChildren ? '▾' : '')}';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%) ${(_) => (!_.active ? '' : 'rotate(-180deg)')};
    transition: transform 0.3s;
    display: ${(_) => (_.hasChildren ? 'block' : 'none')};
    color: ${(_) => (_.active ? '#0A5F79' : '#777')};
    font-size: 14px;
  }

  &.collapsed::after {
    transform: translateY(-50%) rotate(-180deg);
  }

  span {
    color: ${(_) => (_.active ? '#0A5F79' : '#444')};
    font-weight: ${(_) => (_.active ? '500' : 'normal')};
    font-size: 13.5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: calc(100% - 20px);
  }
`;

// Visual indicator for drag operations
const DragTarget = styled.div`
  position: absolute;
  height: 3px;
  width: 100%;
  background-color: #0a5f79;
  left: 0;
  z-index: 1;
`;

// Icon for showing children indicators
const ItemNameText = styled.span.attrs({ className: 'item-name-text' })`
  display: inline;
  width: max-content;
  cursor: pointer;
`;

const HasChildrenIcon = styled.span`
  color: #0a5f79;
  margin-left: 6px;
  font-size: 14px;
  opacity: 0.7;
`;

function getAllIds(menu: MenuItem[]): number[] {
  const ids: number[] = [];

  function traverseMenu(items: MenuItem[]) {
    items.forEach((item) => {
      ids.push(item.id);
      if (item.children) {
        traverseMenu(item.children);
      }
    });
  }

  traverseMenu(menu);
  return ids;
}
const CollapsibleMenu = ({
  refetch,
  menuItems,
  selectedCategory,
  setselectedCategory,
  paramId,
  setProdId,
}: MenuProps) => {
  const [collapsedItems, setCollapsedItems] = useState<number[]>(
    getAllIds(menuItems),
  );
  const [draggedItem, setDraggedItem] = useState<MenuItem | null>(null);
  const [dropItem, setDropItem] = useState<MenuItem | null>(null);
  const [hoveredItem, setHoveredItem] = useState<MenuItem | null>(null);
  const [swapChange, setSwapChange] = useState(false);
  const [parentDataX, setParentDataX] = useState<any>();
  const [parentDataY, setParentDataY] = useState<any>();
  const [isOpenChange, setIsOpenChange] = useState(false);
  const [dropPosition, setDropPosition] = useState<{
    id: number;
    position: 'top' | 'bottom' | null;
  }>({ id: 0, position: null });

  const addToast = useToast();
  const axios = useAxios();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const { mutate, isLoading } = useMutation({
    mutationFn: async (updatedCategory: {
      id: any;
      parent_id: any;
      new_position: any;
    }) => {
      const response = await axios.put(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/hierarchy`,
        `/v1/catalog-admin/category/update-hierarchy/`,
        {
          category_id: updatedCategory.id,
          new_position: updatedCategory.new_position,
          new_parent_id: updatedCategory.parent_id,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'admin-identifier': `${localStorage.getItem('username')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            // 'Content-Type': 'multipart/form-data;',
          },
        },
      );
      return response;
    },
    onSuccess() {
      addToast('success', 'Category updated successfully');
      refetch();
    },
    onError(err) {
      addToast('error', (err as any)?.message);
    },
    onSettled() {
      setDraggedItem(null);
      setDropItem(null);
      setDropPosition({ id: 0, position: null });
    },
  });

  useEffect(() => {
    if (paramId) {
      setCollapsedItems((prev) => prev.filter((id) => id !== paramId));
    }
  }, [paramId]);

  const handleItemClick = (item: MenuItem) => {
    if (collapsedItems.includes(item.id)) {
      setCollapsedItems(collapsedItems.filter((itemId) => itemId !== item.id));
    } else {
      setCollapsedItems([...collapsedItems, item.id]);
    }
  };

  const handleDragStart = (e: React.DragEvent, item: MenuItem) => {
    e.stopPropagation();
    setDraggedItem(item);
  };

  const handleDragOver = (e: React.DragEvent, item: MenuItem) => {
    e.preventDefault();
    if (!draggedItem || draggedItem.id === item.id) return;

    // Determine if hovering at top or bottom half
    const rect = e.currentTarget.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const position = y < rect.height / 2 ? 'top' : 'bottom';

    setDropPosition({ id: item.id, position });
  };

  const handleDragLeave = () => {
    setDropPosition({ id: 0, position: null });
  };

  const fetchParentData = async (id: number) => {
    const response = (await axios.get(
      // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/fetch-parent/${id}`,
      `/v1/catalog-admin/category/fetch-parent/${id}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          'admin-identifier': `${localStorage.getItem('username')}`,
          'x-api-key': `${constants.CATALOG_KEY}`,
        },
      },
    )) as { searched_category: number; parent_category: { id: number } };
    return response;
  };

  const handleDrop = async (e: React.DragEvent, item: MenuItem) => {
    e.preventDefault();
    e.stopPropagation();
    if (!draggedItem || draggedItem.id === item.id) return;

    // Check if the drop target is a descendant of the dragged item
    if (draggedItem.children && isDescendantOf(item, draggedItem)) {
      addToast('error', 'Cannot move a category into its own descendant');
      setDropPosition({ id: 0, position: null });
      return;
    }

    setDropItem(item);
    setDropPosition({ id: 0, position: null });

    try {
      const [parentXData, parentYData] = await Promise.all([
        fetchParentData(draggedItem.id),
        fetchParentData(item.id),
      ]);

      setParentDataX(parentXData);
      setParentDataY(parentYData);

      if (parentXData?.parent_category.id === parentYData?.parent_category.id) {
        setIsOpenChange(true);
      } else {
        const mutateData = {
          id: draggedItem?.id,
          parent_id: parentYData?.parent_category?.id,
          new_position: item?.position,
        };
        mutate(mutateData);
      }
    } catch (error) {
      addToast('error', 'Error fetching parent data.');
    }
  };

  const renderMenuItems = (items: MenuItem[], level = 0) => {
    return items.map((item) => {
      const hasChildren =
        item?.children && item?.children?.length > 0 ? true : false;

      const isActive = Number(queryParams.get('id')) === item.id;
      const isDropTarget = dropPosition.id === item.id;

      return (
        <div
          key={item.id}
          draggable
          onDragStart={(e) => handleDragStart(e, item)}
          onDragOver={(e) => handleDragOver(e, item)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, item)}
          style={{ position: 'relative' }}
        >
          {isDropTarget && dropPosition.position === 'top' && (
            <DragTarget style={{ top: '-1px' }} />
          )}

          {level === 0 ? (
            <ParentMenu
              hasChildren={hasChildren}
              className={
                item.children &&
                selectedCategory &&
                selectedCategory.id === item.id
                  ? 'collapsed'
                  : ''
              }
              active={isActive}
              onClick={(e) => {
                handleItemClick(item);
              }}
            >
              <span>
                <ItemNameText
                  onClick={(e) => {
                    e.stopPropagation();
                    setProdId(item.id);
                    setselectedCategory(item);
                  }}
                >
                  {item.name}
                </ItemNameText>
              </span>
            </ParentMenu>
          ) : (
            <MenuItemStyled
              level={level}
              onClick={() => handleItemClick(item)}
              hasChildren={hasChildren}
              active={isActive}
              className={
                hasChildren && collapsedItems.includes(item.id)
                  ? 'collapsed'
                  : ''
              }
            >
              <span>
                <ItemNameText
                  onClick={(e) => {
                    e.stopPropagation();
                    setProdId(item.id);
                    setselectedCategory(item);
                  }}
                >
                  {item.name}
                </ItemNameText>
              </span>
            </MenuItemStyled>
          )}

          {isDropTarget && dropPosition.position === 'bottom' && (
            <DragTarget style={{ bottom: '-1px' }} />
          )}

          {item.children &&
            item.children.length > 0 &&
            !collapsedItems.includes(item.id) && (
              <NestedCategoryScrollContainer>
                <MenuContainer level={level + 1}>
                  {renderMenuItems(item.children, level + 1)}
                </MenuContainer>
              </NestedCategoryScrollContainer>
            )}
        </div>
      );
    });
  };

  return (
    <>
      <MenuWrapper>{renderMenuItems(menuItems)}</MenuWrapper>
      {isOpenChange && (
        <SwapOrChangeParentModal
          change={setSwapChange}
          refetch={refetch}
          visible={isOpenChange}
          close={() => setIsOpenChange(false)}
          draggedItem={draggedItem}
          setDraggedItem={setDraggedItem}
          dropItem={dropItem}
          parentX={parentDataX}
          parentY={parentDataY}
        />
      )}
    </>
  );
};

export default CollapsibleMenu;

// Add this function after the getAllIds function
function isDescendantOf(
  potentialChild: MenuItem,
  potentialParent: MenuItem,
): boolean {
  // If the potential child has no ID or the IDs match, return false
  if (!potentialChild || potentialChild.id === potentialParent.id) {
    return false;
  }

  // Check if the potential parent has children
  if (potentialParent.children && potentialParent.children.length > 0) {
    // Check if the potential child is a direct child
    const isDirectChild = potentialParent.children.some(
      (child) => child.id === potentialChild.id,
    );
    if (isDirectChild) {
      return true;
    }

    // Recursively check if the potential child is a descendant of any of the children
    return potentialParent.children.some((child) =>
      isDescendantOf(potentialChild, child),
    );
  }

  return false;
}
