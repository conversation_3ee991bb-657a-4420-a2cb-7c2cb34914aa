import { useState, useEffect } from 'react';
import useToast from '../../hooks/useToast';
import LazyLoading from '../../components/UI-components/LazyLoading';
import { IItem } from '../../components/drawer/ViewCancelRequestDrawer';
import { useParams } from 'react-router-dom';
import { useNavigate, useLocation } from 'react-router-dom';
import NewOrderContextProvider from '../order/NewOrderContext';
import { useAuth } from '../../components/providers/AuthProvider';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../hooks/useAxios';
import { OrderListTable } from '../../components/table/order-lists/OrderListTable';
import krakendPaths from '../../constants/krakendPaths';

export interface filterTypes {
  page_no: number;
  page_size: number;
  order_id?: string;
}
const NewOrders = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [customer_id, setCustomer_id] = useState<string | undefined>();
  const [payment_method_code, setPayment_method_code] = useState<string[]>([]);
  const [source, setSource] = useState<string[]>([]);
  const [erpStatus, setErpStatus] = useState<string[]>([]);
  const [country_id, setCountry_id] = useState<IItem | undefined>();
  const [order_status, setOrder_status] = useState<string>('');
  const [entity_id, setEntity_id] = useState<string | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);

  const addToast = useToast();
  const { setHeaderInformation } = useAuth();
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  const { orderIds } = useParams();

  useEffect(() => {
    if (orderIds) {
      setSearchContent(orderIds);
      setFilters({
        page_no: 1,
        page_size: 20,
        order_id: orderIds,
      });
    }
  }, [orderIds]);

  const [filters, setFilters] = useState<filterTypes>({
    page_no:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    page_size: 20,
  });

  const axios = useAxios();

  const {
    isLoading: queryLoading,
    refetch,
    data: queryData,
  } = useQuery({
    queryKey: ['get-order-list', filters],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.NEW_ORDER_URL}/admin-api/v1/orders`,
        {
          params: {
            ...filters,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
  });

  const handleSearch = () => {
    if (searchContent?.length === 0 || !searchContent)
      addToast('warning', 'Please Enter an Order Id to Search');
    // else if (!searchContent?.match(/^[0-9]+(-[0-9]+)*$/))
    //   addToast(
    //     'warning',
    //     'Invalid Order Id format. Please enter a valid Order Id.',
    //   );
    else
      setFilters({
        page_no: 1,
        page_size: 20,
        order_id: searchContent,
      });
  };

  useEffect(() => {
    if (Number(queryParams.get('page')) !== filters.page_no) {
      queryParams.set('page', filters.page_no.toString());
      navigate({ search: queryParams.toString() });
    }
  }, [filters]);

  useEffect(() => {
    setHeaderInformation({
      title: 'New Orders',
      breadcrumbParent: '',
    });
    queryParams.set(
      'page',
      filters.page_no > 0 ? filters.page_no.toString() : '1',
    );
    navigate({ search: queryParams.toString() });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page_no &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev: any) => ({
        ...prev,
        page_no: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page_no: 1 }));
    }
  }, [queryParams.get('page')]);

  const reset = () => {
    setCustomer_id(undefined);
    setPayment_method_code([]);
    setSource([]);
    setErpStatus([]);
    setCountry_id(undefined);
    setOrder_status('');
    setEntity_id(undefined);
    setEndDate(undefined);
    setStartDate(undefined);
    setSearchContent(undefined);
    setFilters({
      page_size: 20,
      page_no: 1,
    });
    setTimeout(() => {
      refetch();
    }, 300);
  };

  if (queryLoading) return <LazyLoading />;
  else {
    return (
      <NewOrderContextProvider>
        <OrderListTable
          data={queryData?.orders ? queryData?.orders : []}
          searchContent={searchContent}
          setSearchContent={setSearchContent}
          refetch={refetch}
          filters={filters}
          setFilters={setFilters}
          count={
            queryData?.total_order_count ? queryData?.total_order_count : 0
          }
          handleSearch={handleSearch}
          reset={reset}
          customer_id={customer_id}
          setCustomer_id={setCustomer_id}
          payment_method_code={payment_method_code}
          setPayment_method_code={setPayment_method_code}
          source={source}
          setSource={setSource}
          erpStatus={erpStatus}
          setErpStatus={setErpStatus}
          country_id={country_id}
          setCountry_id={setCountry_id}
          order_status={order_status}
          setOrder_status={setOrder_status}
          entity_id={entity_id}
          setEntity_id={setEntity_id}
          endDate={endDate}
          setEndDate={setEndDate}
          startDate={startDate}
          setStartDate={setStartDate}
          page={queryData?.pagination}
        />
      </NewOrderContextProvider>
    );
  }
};

export default NewOrders;
