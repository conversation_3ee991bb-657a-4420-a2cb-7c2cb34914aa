import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svg from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), svg()],
  server: {
    proxy: {
      '/return/': {
        target: 'https://ordermg-prod.dentalkart.com',
        changeOrigin: true,
        secure: false,
      },
      '/forwarders/': {
        target: 'https://dental-admin.dentalkart.com',
        // target: 'http://localhost:3002',
        secure: false,
        changeOrigin: true,
      },
      '/react-admin/gql': {
        target: 'https://dental-admin.dentalkart.com/',
        secure: false,
        changeOrigin: true,
      },
      '/rest/': {
        target: 'https://dental-admin.dentalkart.com/',
        // target: 'http://localhost:3002',
        secure: false,
        changeOrigin: true,
      },
      '/form-response': {
        // target:'http://localhost:3000/',
        target: 'https://dental-admin.dentalkart.com',
        changeOrigin: true,
      },
      '/payment/': {
        target: 'https://ordermg-prod.dentalkart.com',
        changeOrigin: true,
      },
      '/v1/catalog-admin/': {
        // target: 'https://catalog-service.dentalkart.com',
        target: 'http://localhost:3002',
        secure: false,
        changeOrigin: true,
      },
    },
  },
});
