server {
	listen 80;
	location / {
		root /usr/share/nginx/html;
		index index.html index.htm;
		try_files $uri $uri/ /index.html =404;
	}

	location /return/ {
		proxy_pass https://ordermg-prod.dentalkart.com;
	}
	location /react-admin/gql {
		proxy_pass https://dental-admin.dentalkart.com;
	}
	location /forwarders/ {
		proxy_pass https://dental-admin.dentalkart.com;
	}
	
	location /rest/ {
        proxy_pass https://dental-admin.dentalkart.com;
    }
	location /payment/refund/ {
        proxy_pass https://ordermg-prod.dentalkart.com;
    }
	location /v1/catalog-admin/ {
        proxy_pass https://catalog-service.dentalkart.com;
    }

}
